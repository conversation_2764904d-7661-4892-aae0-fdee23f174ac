import { News } from '@/lib/types/api';

/**
 * Transform backend news data to frontend format with computed properties
 */
export function transformNewsData(backendNews: any): News {
  return {
    ...backendNews,
    // Computed properties for compatibility
    author: `Author ${backendNews.authorId}`, // You might want to fetch actual author name
    summary: backendNews.excerpt,
    imageUrl: backendNews.featuredImage,
    isPublished: backendNews.status === 'published',
    isHot: backendNews.isFeatured,
    publishDate: backendNews.publishedAt || backendNews.createdAt,
  };
}

/**
 * Transform frontend form data to backend format
 */
export function transformCreateNewsData(frontendData: any) {
  return {
    title: frontendData.title,
    content: frontendData.content,
    categoryId: frontendData.categoryId,
    excerpt: frontendData.summary,
    featuredImage: frontendData.imageUrl,
    tags: frontendData.tags,
    status: frontendData.isPublished ? 'published' : 'draft',
    publishedAt: frontendData.isPublished ? frontendData.publishDate : undefined,
    isFeatured: frontendData.isHot,
    metaTitle: frontendData.metaTitle,
    metaDescription: frontendData.metaDescription,
    priority: frontendData.isHot ? 1 : 0,
  };
}

/**
 * Transform frontend update data to backend format
 */
export function transformUpdateNewsData(frontendData: any) {
  const updateData: any = {};

  // Basic content fields
  if (frontendData.title !== undefined) updateData.title = frontendData.title;
  if (frontendData.content !== undefined) updateData.content = frontendData.content;
  if (frontendData.excerpt !== undefined) updateData.excerpt = frontendData.excerpt;
  if (frontendData.featuredImage !== undefined) updateData.featuredImage = frontendData.featuredImage;
  if (frontendData.tags !== undefined) updateData.tags = frontendData.tags;
  if (frontendData.status !== undefined) updateData.status = frontendData.status;

  // Category field
  if (frontendData.categoryId !== undefined) {
    updateData.categoryId = typeof frontendData.categoryId === 'string'
      ? parseInt(frontendData.categoryId)
      : frontendData.categoryId;
  }

  // SEO fields
  if (frontendData.metaTitle !== undefined) updateData.metaTitle = frontendData.metaTitle;
  if (frontendData.metaDescription !== undefined) updateData.metaDescription = frontendData.metaDescription;

  // Related content fields
  if (frontendData.relatedLeagueId !== undefined) updateData.relatedLeagueId = frontendData.relatedLeagueId;
  if (frontendData.relatedTeamId !== undefined) updateData.relatedTeamId = frontendData.relatedTeamId;
  if (frontendData.relatedPlayerId !== undefined) updateData.relatedPlayerId = frontendData.relatedPlayerId;
  if (frontendData.relatedFixtureId !== undefined) updateData.relatedFixtureId = frontendData.relatedFixtureId;

  // Feature and priority fields
  if (frontendData.isFeatured !== undefined) updateData.isFeatured = frontendData.isFeatured;
  if (frontendData.priority !== undefined) updateData.priority = frontendData.priority;

  // Publishing fields
  if (frontendData.publishedAt !== undefined) updateData.publishedAt = frontendData.publishedAt;

  // Handle legacy field names for backward compatibility
  if (frontendData.summary !== undefined) updateData.excerpt = frontendData.summary;
  if (frontendData.imageUrl !== undefined) updateData.featuredImage = frontendData.imageUrl;

  // Handle legacy status changes
  if (frontendData.isPublished !== undefined) {
    updateData.status = frontendData.isPublished ? 'published' : 'draft';
  }

  // Handle legacy featured status
  if (frontendData.isHot !== undefined) {
    updateData.isFeatured = frontendData.isHot;
  }

  return updateData;
}

/**
 * Transform filters from frontend to backend format
 */
export function transformNewsFilters(frontendFilters: any) {
  const backendFilters: any = { ...frontendFilters };

  // Transform legacy filters
  if (frontendFilters.isPublished !== undefined) {
    backendFilters.status = frontendFilters.isPublished ? 'published' : 'draft';
    delete backendFilters.isPublished;
  }

  if (frontendFilters.isHot !== undefined) {
    backendFilters.isFeatured = frontendFilters.isHot;
    delete backendFilters.isHot;
  }

  return backendFilters;
}
