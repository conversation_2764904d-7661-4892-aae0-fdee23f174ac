import { apiClient } from './client';
import { News, PaginatedResponse } from '@/lib/types/api';
import { transformNewsData, transformCreateNewsData, transformUpdateNewsData, transformNewsFilters } from '@/lib/utils/news-transform';

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        return parsed.state?.accessToken || null;
      }
    } catch (error) {
      console.warn('Failed to parse auth storage:', error);
    }
    return localStorage.getItem('accessToken');
  }
  return null;
};

export interface NewsFilters {
  page?: number;
  limit?: number;
  search?: string;
  status?: 'draft' | 'published' | 'archived';
  isFeatured?: boolean;
  categoryId?: number;
  tags?: string[];
  dateFrom?: string;
  dateTo?: string;
  // Legacy compatibility
  author?: string;
  isPublished?: boolean;
  isHot?: boolean;
}

export interface CreateNewsData {
  title: string;
  content: string;
  categoryId?: number;
  excerpt?: string;
  featuredImage?: string;
  tags?: string[];
  status?: 'draft' | 'published' | 'archived';
  publishedAt?: string;
  metaTitle?: string;
  metaDescription?: string;
  relatedLeagueId?: number;
  relatedTeamId?: number;
  relatedPlayerId?: number;
  relatedFixtureId?: number;
  isFeatured?: boolean;
  priority?: number;
}

export interface UpdateNewsData {
  title?: string;
  content?: string;
  categoryId?: number;
  excerpt?: string;
  featuredImage?: string;
  tags?: string[];
  status?: 'draft' | 'published' | 'archived';
  publishedAt?: string;
  metaTitle?: string;
  metaDescription?: string;
  relatedLeagueId?: number;
  relatedTeamId?: number;
  relatedPlayerId?: number;
  relatedFixtureId?: number;
  isFeatured?: boolean;
  priority?: number;
}

export const newsApi = {  // Public/Admin endpoint via proxy (includes auth if available)
  getNews: async (filters: NewsFilters = {}): Promise<PaginatedResponse<News>> => {
    const transformedFilters = transformNewsFilters(filters);
    const params = new URLSearchParams();
    Object.entries(transformedFilters).forEach(([key, value]) => {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news?${params.toString()}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch news');
    }

    const data = await response.json();

    // Transform the news data
    if (data.data) {
      data.data = data.data.map(transformNewsData);
    }

    return data;
  },
  // Get single news item (includes auth if available)
  getNewsById: async (id: number): Promise<News> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news/${id}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch news ${id}`);
    }

    const data = await response.json();
    return transformNewsData(data);
  },

  // Editor+ access required
  createNews: async (data: CreateNewsData): Promise<News> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const transformedData = transformCreateNewsData(data);
    const response = await fetch('/api/news', {
      method: 'POST',
      headers,
      body: JSON.stringify(transformedData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create news');
    }

    const responseData = await response.json();
    return transformNewsData(responseData);
  },

  // Editor+ access required
  updateNews: async (id: number, data: UpdateNewsData): Promise<News> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const transformedData = transformUpdateNewsData(data);
    const response = await fetch(`/api/news/${id}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(transformedData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to update news ${id}`);
    }

    const responseData = await response.json();
    return transformNewsData(responseData);
  },

  // Admin access required
  deleteNews: async (id: number): Promise<void> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news/${id}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to delete news ${id}`);
    }
  },

  // Helper methods for common operations
  getPublishedNews: async (filters: Omit<NewsFilters, 'status'> = {}): Promise<PaginatedResponse<News>> => {
    return newsApi.getNews({ ...filters, status: 'published' });
  },

  getHotNews: async (filters: Omit<NewsFilters, 'isFeatured'> = {}): Promise<PaginatedResponse<News>> => {
    return newsApi.getNews({ ...filters, isFeatured: true });
  },

  getNewsByAuthor: async (author: string, filters: Omit<NewsFilters, 'author'> = {}): Promise<PaginatedResponse<News>> => {
    return newsApi.getNews({ ...filters, author });
  },

  toggleNewsStatus: async (id: number, isPublished: boolean): Promise<News> => {
    return newsApi.updateNews(id, {
      status: isPublished ? 'published' : 'draft'
    });
  },

  toggleHotStatus: async (id: number, isHot: boolean): Promise<News> => {
    return newsApi.updateNews(id, { isFeatured: isHot });
  },
};
