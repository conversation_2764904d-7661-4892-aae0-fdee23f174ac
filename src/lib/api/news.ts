import { apiClient } from './client';
import { News, PaginatedResponse } from '@/lib/types/api';

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        return parsed.state?.accessToken || null;
      }
    } catch (error) {
      console.warn('Failed to parse auth storage:', error);
    }
    return localStorage.getItem('accessToken');
  }
  return null;
};

export interface NewsFilters {
  page?: number;
  limit?: number;
  search?: string;
  author?: string;
  isPublished?: boolean;
  isHot?: boolean;
  tags?: string[];
  dateFrom?: string;
  dateTo?: string;
}

export interface CreateNewsData {
  title: string;
  content: string;
  author: string;
  categoryId?: number;
  summary?: string;
  imageUrl?: string;
  tags?: string[];
  isPublished?: boolean;
  isHot?: boolean;
  publishDate?: string;
}

export interface UpdateNewsData {
  title?: string;
  content?: string;
  author?: string;
  categoryId?: number;
  summary?: string;
  imageUrl?: string;
  tags?: string[];
  isPublished?: boolean;
  isHot?: boolean;
  publishDate?: string;
}

export const newsApi = {  // Public/Admin endpoint via proxy (includes auth if available)
  getNews: async (filters: NewsFilters = {}): Promise<PaginatedResponse<News>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        if (Array.isArray(value)) {
          value.forEach(v => params.append(key, v.toString()));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news?${params.toString()}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch news');
    }

    return await response.json();
  },
  // Get single news item (includes auth if available)
  getNewsById: async (id: number): Promise<News> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news/${id}`, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch news ${id}`);
    }

    return await response.json();
  },

  // Editor+ access required
  createNews: async (data: CreateNewsData): Promise<News> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch('/api/news', {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create news');
    }

    return await response.json();
  },

  // Editor+ access required
  updateNews: async (id: number, data: UpdateNewsData): Promise<News> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news/${id}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to update news ${id}`);
    }

    return await response.json();
  },

  // Admin access required
  deleteNews: async (id: number): Promise<void> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`/api/news/${id}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to delete news ${id}`);
    }
  },

  // Helper methods for common operations
  getPublishedNews: async (filters: Omit<NewsFilters, 'isPublished'> = {}): Promise<PaginatedResponse<News>> => {
    return newsApi.getNews({ ...filters, isPublished: true });
  },

  getHotNews: async (filters: Omit<NewsFilters, 'isHot'> = {}): Promise<PaginatedResponse<News>> => {
    return newsApi.getNews({ ...filters, isHot: true });
  },

  getNewsByAuthor: async (author: string, filters: Omit<NewsFilters, 'author'> = {}): Promise<PaginatedResponse<News>> => {
    return newsApi.getNews({ ...filters, author });
  },

  toggleNewsStatus: async (id: number, isPublished: boolean): Promise<News> => {
    return newsApi.updateNews(id, { isPublished });
  },

  toggleHotStatus: async (id: number, isHot: boolean): Promise<News> => {
    return newsApi.updateNews(id, { isHot });
  },
};
