import { CategoryFilters, CreateCategoryData, UpdateCategoryData, NewsCategory, PaginatedResponse } from '@/lib/types/api';

export const categoriesApi = {
        // Get categories with filtering and pagination
        async getCategories(filters: CategoryFilters = {}): Promise<PaginatedResponse<NewsCategory>> {
                const params = new URLSearchParams();

                if (filters.page) params.append('page', filters.page.toString());
                if (filters.limit) params.append('limit', filters.limit.toString());
                if (filters.search) params.append('search', filters.search);
                if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
                if (filters.isPublic !== undefined) params.append('isPublic', filters.isPublic.toString());

                const url = `/api/news/categories${params.toString() ? `?${params.toString()}` : ''}`;

                const response = await fetch(url, {
                        headers: {
                                'Content-Type': 'application/json',
                        },
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }

                return response.json();
        },

        // Get a single category by ID
        async getCategoryById(id: number): Promise<NewsCategory> {
                const response = await fetch(`/api/news/categories/${id}`, {
                        headers: {
                                'Content-Type': 'application/json',
                        },
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result.data || result;
        },

        // Get public categories only
        async getPublicCategories(): Promise<NewsCategory[]> {
                const response = await fetch(`/api/news/categories?isPublic=true`, {
                        headers: {
                                'Content-Type': 'application/json',
                        },
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result.data || result;
        },

        // Create a new category
        async createCategory(data: CreateCategoryData): Promise<NewsCategory> {
                const response = await fetch(`/api/news/categories`, {
                        method: 'POST',
                        headers: {
                                'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data),
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result.data || result;
        },

        // Update an existing category
        async updateCategory(id: number, data: UpdateCategoryData): Promise<NewsCategory> {
                const response = await fetch(`/api/news/categories/${id}`, {
                        method: 'PUT',
                        headers: {
                                'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data),
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result.data || result;
        },

        // Delete a category
        async deleteCategory(id: number): Promise<void> {
                const response = await fetch(`/api/news/categories/${id}`, {
                        method: 'DELETE',
                        headers: {
                                'Content-Type': 'application/json',
                        },
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }
        },

        // Toggle category status (active/inactive)
        async toggleCategoryStatus(id: number, isActive: boolean): Promise<NewsCategory> {
                const response = await fetch(`/api/news/categories/${id}`, {
                        method: 'PATCH',
                        headers: {
                                'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ isActive }),
                });

                if (!response.ok) {
                        const error = await response.json().catch(() => ({ message: 'Unknown error' }));
                        throw new Error(error.message || `HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                return result.data || result;
        },
};

// Re-export types for convenience
export type { CategoryFilters, CreateCategoryData, UpdateCategoryData };