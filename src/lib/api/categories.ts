import { apiClient } from './client';
import { NewsCategory, PaginatedResponse } from '@/lib/types/api';

// Types for category operations
export interface CategoryFilters {
        page?: number;
        limit?: number;
        search?: string;
        isActive?: boolean;
        isPublic?: boolean;
        sortBy?: 'name' | 'createdAt' | 'sortOrder';
        sortOrder?: 'asc' | 'desc';
}

export interface CreateCategoryData {
        name: string;
        slug?: string;
        description?: string;
        color?: string;
        isActive?: boolean;
        isPublic?: boolean;
        sortOrder?: number;
        parentId?: number;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> { }

// Categories API class
class CategoriesApi {
        private baseUrl = '/api/news/categories';

        // Get all categories with filters
        async getCategories(filters: CategoryFilters = {}): Promise<PaginatedResponse<NewsCategory>> {
                try {
                        const params = new URLSearchParams();

                        if (filters.page) params.append('page', filters.page.toString());
                        if (filters.limit) params.append('limit', filters.limit.toString());
                        if (filters.search) params.append('search', filters.search);
                        if (filters.isActive !== undefined) params.append('isActive', filters.isActive.toString());
                        if (filters.isPublic !== undefined) params.append('isPublic', filters.isPublic.toString());
                        if (filters.sortBy) params.append('sortBy', filters.sortBy);
                        if (filters.sortOrder) params.append('sortOrder', filters.sortOrder);

                        const queryString = params.toString();
                        const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

                        console.log('🔄 Fetching categories from:', url);

                        // Use fetch directly to avoid authentication issues for now
                        const response = await fetch(url, {
                                method: 'GET',
                                headers: {
                                        'Content-Type': 'application/json',
                                },
                        });

                        if (!response.ok) {
                                throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json() as NewsCategory[];
                        console.log('✅ Categories fetched successfully:', data.length, 'items');

                        // Create pagination structure for frontend compatibility
                        const page = filters.page || 1;
                        const limit = filters.limit || 20;
                        const totalItems = data.length;
                        const totalPages = Math.ceil(totalItems / limit);

                        // Apply client-side pagination if needed
                        const startIndex = (page - 1) * limit;
                        const endIndex = startIndex + limit;
                        const paginatedData = data.slice(startIndex, endIndex);

                        return {
                                data: paginatedData,
                                meta: {
                                        currentPage: page,
                                        totalPages,
                                        totalItems,
                                        limit
                                }
                        };
                } catch (error) {
                        console.error('❌ Error fetching categories:', error);
                        throw error;
                }
        }

        // Get category by ID
        async getCategoryById(id: number): Promise<NewsCategory> {
                try {
                        console.log('🔄 Fetching category by ID:', id);

                        // Try with authentication first (for admin access)
                        const response = await apiClient.get<NewsCategory>(`${this.baseUrl}/${id}`);
                        console.log('✅ Category fetched successfully:', response);
                        return response;
                } catch (error) {
                        console.error('❌ Error fetching category by ID:', error);
                        throw error;
                }
        }

        // Get public categories only
        async getPublicCategories(): Promise<NewsCategory[]> {
                const response = await this.getCategories({ isPublic: true, isActive: true });
                return response.data;
        }

        // Create new category
        async createCategory(data: CreateCategoryData): Promise<NewsCategory> {
                return apiClient.post<NewsCategory>(this.baseUrl, data);
        }

        // Update category
        async updateCategory(id: number, data: UpdateCategoryData): Promise<NewsCategory> {
                return apiClient.patch<NewsCategory>(`${this.baseUrl}/${id}`, data);
        }

        // Delete category
        async deleteCategory(id: number): Promise<void> {
                return apiClient.delete<void>(`${this.baseUrl}/${id}`);
        }

        // Toggle category status
        async toggleCategoryStatus(id: number, isActive: boolean): Promise<NewsCategory> {
                return apiClient.patch<NewsCategory>(`${this.baseUrl}/${id}`, { isActive });
        }

        // Reorder categories
        async reorderCategories(categoryIds: number[]): Promise<void> {
                return apiClient.post<void>(`${this.baseUrl}/reorder`, { categoryIds });
        }

        // Get category statistics
        async getCategoryStats(): Promise<{
                totalCategories: number;
                activeCategories: number;
                publicCategories: number;
                totalArticles: number;
        }> {
                return apiClient.get<any>(`${this.baseUrl}/stats`);
        }
}

// Export singleton instance
export const categoriesApi = new CategoriesApi();
export default categoriesApi;