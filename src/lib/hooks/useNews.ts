import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { newsApi, NewsFilters, CreateNewsData, UpdateNewsData } from '@/lib/api/news';
import { News } from '@/lib/types/api';
import { toast } from 'sonner';

// News Query Hooks
export const useNews = (filters: NewsFilters = {}) => {
        return useQuery({
                queryKey: ['news', filters],
                queryFn: () => newsApi.getNews(filters),
                staleTime: 5 * 60 * 1000, // 5 minutes
        });
};

export const useNewsById = (id: number, enabled: boolean = true) => {
        return useQuery({
                queryKey: ['news', id],
                queryFn: () => newsApi.getNewsById(id),
                enabled: !!id && enabled,
                staleTime: 10 * 60 * 1000, // 10 minutes
        });
};

export const usePublishedNews = (filters: Omit<NewsFilters, 'status'> = {}) => {
        return useQuery({
                queryKey: ['news', 'published', filters],
                queryFn: () => newsApi.getPublishedNews(filters),
                staleTime: 5 * 60 * 1000,
        });
};

export const useHotNews = (filters: Omit<NewsFilters, 'isFeatured'> = {}) => {
        return useQuery({
                queryKey: ['news', 'hot', filters],
                queryFn: () => newsApi.getHotNews(filters),
                staleTime: 5 * 60 * 1000,
        });
};

// News Mutation Hooks
export const useCreateNews = () => {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: (data: CreateNewsData) => newsApi.createNews(data),
                onSuccess: (newNews) => {
                        queryClient.invalidateQueries({ queryKey: ['news'] });
                        toast.success('News created successfully');
                },
                onError: (error: Error) => {
                        toast.error(`Failed to create news: ${error.message}`);
                },
        });
};

export const useUpdateNews = () => {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: ({ id, data }: { id: number; data: UpdateNewsData }) =>
                        newsApi.updateNews(id, data),
                onSuccess: (updatedNews) => {
                        queryClient.invalidateQueries({ queryKey: ['news'] });
                        queryClient.invalidateQueries({ queryKey: ['news', updatedNews.id] });
                        toast.success('News updated successfully');
                },
                onError: (error: Error) => {
                        toast.error(`Failed to update news: ${error.message}`);
                },
        });
};

export const useDeleteNews = () => {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: (id: number) => newsApi.deleteNews(id),
                onSuccess: () => {
                        queryClient.invalidateQueries({ queryKey: ['news'] });
                        toast.success('News deleted successfully');
                },
                onError: (error: Error) => {
                        toast.error(`Failed to delete news: ${error.message}`);
                },
        });
};

export const useToggleNewsStatus = () => {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: ({ id, isPublished }: { id: number; isPublished: boolean }) =>
                        newsApi.toggleNewsStatus(id, isPublished),
                onSuccess: (updatedNews) => {
                        queryClient.invalidateQueries({ queryKey: ['news'] });
                        queryClient.invalidateQueries({ queryKey: ['news', updatedNews.id] });
                        toast.success(`News ${updatedNews.isPublished ? 'published' : 'unpublished'} successfully`);
                },
                onError: (error: Error) => {
                        toast.error(`Failed to toggle news status: ${error.message}`);
                },
        });
};

export const useToggleHotStatus = () => {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: ({ id, isHot }: { id: number; isHot: boolean }) =>
                        newsApi.toggleHotStatus(id, isHot),
                onSuccess: (updatedNews) => {
                        queryClient.invalidateQueries({ queryKey: ['news'] });
                        queryClient.invalidateQueries({ queryKey: ['news', updatedNews.id] });
                        toast.success(`News ${updatedNews.isHot ? 'marked as hot' : 'unmarked as hot'} successfully`);
                },
                onError: (error: Error) => {
                        toast.error(`Failed to toggle hot status: ${error.message}`);
                },
        });
};
