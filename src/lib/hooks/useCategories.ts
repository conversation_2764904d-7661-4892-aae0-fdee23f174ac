import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { categoriesApi, CategoryFilters, CreateCategoryData, UpdateCategoryData } from '@/lib/api/categories';
import { NewsCategory } from '@/lib/types/api';
import { toast } from 'sonner';

// Query key factory for better cache management
const categoryKeys = {
        all: ['categories'] as const,
        lists: () => [...categoryKeys.all, 'list'] as const,
        list: (filters: CategoryFilters) => [...categoryKeys.lists(), filters] as const,
        details: () => [...categoryKeys.all, 'detail'] as const,
        detail: (id: number) => [...categoryKeys.details(), id] as const,
        public: () => [...categoryKeys.all, 'public'] as const,
};

// Hook to fetch categories
export function useCategories(filters: CategoryFilters = {}) {
        return useQuery({
                queryKey: categoryKeys.list(filters),
                queryFn: () => categoriesApi.getCategories(filters),
                staleTime: 5 * 60 * 1000, // 5 minutes
        });
}

// Hook to fetch a single category
export function useCategory(id: number) {
        return useQuery({
                queryKey: categoryKeys.detail(id),
                queryFn: () => categoriesApi.getCategoryById(id),
                enabled: !!id,
                staleTime: 10 * 60 * 1000, // 10 minutes
        });
}

// Hook to fetch public categories only
export function usePublicCategories() {
        return useQuery({
                queryKey: categoryKeys.public(),
                queryFn: () => categoriesApi.getPublicCategories(),
                staleTime: 15 * 60 * 1000, // 15 minutes
        });
}

// Hook to create a category
export function useCreateCategory() {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: (data: CreateCategoryData) => categoriesApi.createCategory(data),
                onSuccess: (newCategory) => {
                        // Invalidate all category queries
                        queryClient.invalidateQueries({ queryKey: categoryKeys.all });
                        toast.success(`Category "${newCategory.name}" created successfully`);
                },
                onError: (error: Error) => {
                        toast.error('Failed to create category: ' + error.message);
                },
        });
}

// Hook to update a category
export function useUpdateCategory() {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: ({ id, data }: { id: number; data: UpdateCategoryData }) =>
                        categoriesApi.updateCategory(id, data),
                onSuccess: (updatedCategory) => {
                        // Update the specific category in cache
                        queryClient.setQueryData(
                                categoryKeys.detail(updatedCategory.id),
                                updatedCategory
                        );
                        // Invalidate list queries to reflect changes
                        queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
                        queryClient.invalidateQueries({ queryKey: categoryKeys.public() });
                        toast.success(`Category "${updatedCategory.name}" updated successfully`);
                },
                onError: (error: Error) => {
                        toast.error('Failed to update category: ' + error.message);
                },
        });
}

// Hook to delete a category
export function useDeleteCategory() {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: (id: number) => categoriesApi.deleteCategory(id),
                onSuccess: (_, deletedId) => {
                        // Remove the category from cache
                        queryClient.removeQueries({ queryKey: categoryKeys.detail(deletedId) });
                        // Invalidate list queries
                        queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
                        queryClient.invalidateQueries({ queryKey: categoryKeys.public() });
                        toast.success('Category deleted successfully');
                },
                onError: (error: Error) => {
                        toast.error('Failed to delete category: ' + error.message);
                },
        });
}

// Hook to toggle category status
export function useToggleCategoryStatus() {
        const queryClient = useQueryClient();

        return useMutation({
                mutationFn: ({ id, isActive }: { id: number; isActive: boolean }) =>
                        categoriesApi.toggleCategoryStatus(id, isActive),
                onSuccess: (updatedCategory) => {
                        // Update the specific category in cache
                        queryClient.setQueryData(
                                categoryKeys.detail(updatedCategory.id),
                                updatedCategory
                        );
                        // Invalidate relevant queries
                        queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
                        queryClient.invalidateQueries({ queryKey: categoryKeys.public() });

                        const status = updatedCategory.isActive ? 'activated' : 'deactivated';
                        toast.success(`Category "${updatedCategory.name}" ${status} successfully`);
                },
                onError: (error: Error) => {
                        toast.error('Failed to toggle category status: ' + error.message);
                },
        });
}
