/* Rich Text Editor Custom Styles */

/* Toolbar Styling */
.rich-text-editor .ql-toolbar {
  border: 1px solid #e2e8f0 !important;
  border-bottom: none !important;
  border-radius: 6px 6px 0 0 !important;
  background: #f8fafc;
  padding: 8px 12px !important;
}

.rich-text-editor .ql-toolbar .ql-stroke {
  fill: none;
  stroke: #64748b;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-width: 2;
}

.rich-text-editor .ql-toolbar .ql-stroke.ql-fill {
  fill: #64748b;
}

.rich-text-editor .ql-toolbar .ql-fill,
.rich-text-editor .ql-toolbar .ql-stroke.ql-fill {
  fill: #64748b;
}

.rich-text-editor .ql-toolbar .ql-picker {
  color: #64748b;
}

.rich-text-editor .ql-toolbar button:hover,
.rich-text-editor .ql-toolbar button:focus {
  color: #3b82f6;
}

.rich-text-editor .ql-toolbar button:hover .ql-stroke {
  stroke: #3b82f6;
}

.rich-text-editor .ql-toolbar button:hover .ql-fill,
.rich-text-editor .ql-toolbar button:hover .ql-stroke.ql-fill {
  fill: #3b82f6;
}

.rich-text-editor .ql-toolbar button.ql-active {
  color: #3b82f6;
}

.rich-text-editor .ql-toolbar button.ql-active .ql-stroke {
  stroke: #3b82f6;
}

.rich-text-editor .ql-toolbar button.ql-active .ql-fill,
.rich-text-editor .ql-toolbar button.ql-active .ql-stroke.ql-fill {
  fill: #3b82f6;
}

/* Container Styling */
.rich-text-editor .ql-container {
  border: 1px solid #e2e8f0 !important;
  border-top: none !important;
  border-radius: 0 0 6px 6px !important;
  font-family: inherit !important;
  font-size: 14px !important;
}

/* Editor Content Styling */
.rich-text-editor .ql-editor {
  font-family: inherit !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  color: #1f2937 !important;
  padding: 16px !important;
}

.rich-text-editor .ql-editor.ql-blank::before {
  color: #9ca3af !important;
  font-style: normal !important;
  font-weight: normal !important;
}

.rich-text-editor .ql-editor p {
  margin-bottom: 12px;
}

.rich-text-editor .ql-editor h1,
.rich-text-editor .ql-editor h2,
.rich-text-editor .ql-editor h3,
.rich-text-editor .ql-editor h4,
.rich-text-editor .ql-editor h5,
.rich-text-editor .ql-editor h6 {
  font-weight: 600;
  margin-top: 24px;
  margin-bottom: 16px;
  line-height: 1.25;
}

.ql-editor h1 {
  font-size: 2em;
}

.ql-editor h2 {
  font-size: 1.5em;
}

.ql-editor h3 {
  font-size: 1.25em;
}

.ql-editor ul,
.ql-editor ol {
  margin-bottom: 16px;
  padding-left: 24px;
}

.ql-editor li {
  margin-bottom: 4px;
}

.ql-editor blockquote {
  border-left: 4px solid #e2e8f0;
  margin: 16px 0;
  padding-left: 16px;
  color: #6b7280;
  font-style: italic;
}

.ql-editor code {
  background-color: #f1f5f9;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.ql-editor pre {
  background-color: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
}

.ql-editor pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

.ql-editor a {
  color: #3b82f6;
  text-decoration: underline;
}

.ql-editor a:hover {
  color: #1d4ed8;
}

/* Error State */
.rich-text-editor.error .ql-toolbar,
.rich-text-editor.error .ql-container {
  border-color: #ef4444 !important;
}

/* Disabled State */
.rich-text-editor.disabled .ql-toolbar {
  background-color: #f9fafb !important;
  opacity: 0.6;
  pointer-events: none;
}

.rich-text-editor.disabled .ql-editor {
  background-color: #f9fafb !important;
  color: #6b7280 !important;
}

/* Dropdown Menus */
.ql-picker-options {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 4px 0;
}

.ql-picker-item {
  padding: 8px 12px;
  color: #374151;
}

.ql-picker-item:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.ql-picker-item.ql-selected {
  background-color: #3b82f6;
  color: white;
}

/* Tooltip */
.ql-tooltip {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  padding: 8px 12px;
}

.ql-tooltip input {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 13px;
}

.ql-tooltip a {
  background: #3b82f6;
  color: white;
  border-radius: 4px;
  padding: 4px 8px;
  text-decoration: none;
  font-size: 13px;
  margin-left: 8px;
}

.ql-tooltip a:hover {
  background: #1d4ed8;
}

/* Focus States */
.ql-container.ql-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 1px #3b82f6 !important;
}

/* Snow Theme Overrides */
.ql-snow .ql-tooltip[data-mode="link"]::before {
  content: "Enter link:";
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  content: "Save";
}

.ql-snow .ql-tooltip[data-mode="video"]::before {
  content: "Enter video:";
}

.ql-snow .ql-picker.ql-expanded .ql-picker-label {
  border-color: #3b82f6;
}

.ql-snow .ql-picker.ql-expanded .ql-picker-options {
  border-color: #3b82f6;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .ql-toolbar {
    padding: 6px 8px !important;
  }

  .ql-toolbar .ql-formats {
    margin-right: 8px !important;
  }

  .ql-editor {
    padding: 12px !important;
    font-size: 16px !important;
    /* Prevent zoom on iOS */
  }
}