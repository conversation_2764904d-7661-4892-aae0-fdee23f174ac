'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column } from '@/components/ui/data-table';
import { ConfirmModal } from '@/components/ui/modal';
import { ToggleSwitch } from '@/components/ui/toggle-switch';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useNews, useDeleteNews, useToggleNewsStatus, useToggleHotStatus } from '@/lib/hooks/useNews';
import { NewsFilters, newsApi } from '@/lib/api/news';
import { News } from '@/lib/types/api';
import { toast } from 'sonner';
import {
  Newspaper,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  User,
  Calendar,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';

export default function NewsPage() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { isEditor, isAdmin } = usePermissions();

  // State for filtering
  const [filters, setFilters] = useState<NewsFilters>({
    page: 1,
    limit: 20,
  });
  const [searchQuery, setSearchQuery] = useState('');

  // State for delete modal
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [newsToDelete, setNewsToDelete] = useState<News | null>(null);

  // Data fetching
  const {
    data: newsData,
    isLoading,
    error
  } = useNews(filters);

  // Mutations
  const deleteNewsMutation = useDeleteNews();
  const toggleStatusMutation = useToggleNewsStatus();
  const toggleHotMutation = useToggleHotStatus();

  // Event handlers
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setFilters(prev => ({
      ...prev,
      search: searchQuery.trim() || undefined,
      page: 1
    }));
  };

  const handleFilterChange = (key: keyof NewsFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1
    }));
  };

  const handleViewNews = (news: News) => {
    router.push(`/dashboard/news/${news.id}`);
  };

  const handleEditNews = (news: News) => {
    if (!isEditor() && !isAdmin()) {
      toast.error('You do not have permission to edit news');
      return;
    }
    router.push(`/dashboard/news/${news.id}/edit`);
  };

  const handleDeleteNews = (news: News) => {
    if (!isAdmin()) {
      toast.error('You do not have permission to delete news');
      return;
    }
    setNewsToDelete(news);
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    if (newsToDelete) {
      try {
        await deleteNewsMutation.mutateAsync(newsToDelete.id);
        setDeleteModalOpen(false);
        setNewsToDelete(null);
      } catch (error) {
        // Error handled by mutation
      }
    }
  };

  // Table columns
  const columns: Column<News>[] = [
    {
      key: 'title',
      title: 'Title',
      sortable: true,
      render: (value, row) => (
        <div className="max-w-md">
          <div className="font-medium text-gray-900 truncate">{value}</div>
          {row.summary && (
            <div className="text-sm text-gray-500 truncate">{row.summary}</div>
          )}
        </div>
      ),
    },
    {
      key: 'author',
      title: 'Author',
      sortable: true,
      filterable: true,
      render: (value) => (
        <div className="flex items-center">
          <User className="h-4 w-4 mr-2 text-gray-400" />
          <span className="text-sm text-gray-600">{value}</span>
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value, row) => {
        const isPublished = value === 'published';
        if (!isEditor() && !isAdmin()) {
          return (
            <Badge variant={isPublished ? 'default' : 'secondary'}>
              {value === 'published' ? 'Published' : value === 'draft' ? 'Draft' : 'Archived'}
            </Badge>
          );
        }

        return (
          <ToggleSwitch
            checked={isPublished}
            onCheckedChange={(checked) => {
              toggleStatusMutation.mutate({ id: row.id, isPublished: checked });
            }}
            label=""
            disabled={toggleStatusMutation.isLoading}
            size="sm"
            variant={isPublished ? 'success' : 'warning'}
          />
        );
      },
    },
    {
      key: 'isFeatured',
      title: 'Featured',
      sortable: false,
      render: (value, row) => {
        if (!isEditor() && !isAdmin()) {
          return value ? (
            <Badge variant="destructive" className="text-xs">
              Featured
            </Badge>
          ) : null;
        }

        return (
          <ToggleSwitch
            checked={value || false}
            onCheckedChange={(checked) => {
              toggleHotMutation.mutate({ id: row.id, isHot: checked });
            }}
            label=""
            disabled={toggleHotMutation.isLoading}
            size="sm"
            variant="danger"
          />
        );
      },
    },
    {
      key: 'publishDate',
      title: 'Publish Date',
      sortable: true,
      render: (value) => (
        <div className="flex items-center text-sm text-gray-600">
          <Calendar className="h-4 w-4 mr-2 text-gray-400" />
          {new Date(value).toLocaleDateString()}
        </div>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, row) => (
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="outline"
            title="View Details"
            onClick={() => handleViewNews(row)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          {isEditor() && (
            <Button
              size="sm"
              variant="outline"
              title="Edit News"
              onClick={() => handleEditNews(row)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {isAdmin() && (
            <Button
              size="sm"
              variant="outline"
              title="Delete News"
              onClick={() => handleDeleteNews(row)}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">News Management</h1>
        </div>
        <Card>
          <CardContent className="p-6">            <div className="text-center">
            <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-red-600 mb-2">Failed to load news</h3>
            <p className="text-gray-600 mb-4">{(error as Error)?.message}</p>
            <Button onClick={() => queryClient.invalidateQueries({ queryKey: ['news'] })}>
              Try Again
            </Button>
          </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Newspaper className="mr-3 h-8 w-8 text-blue-600" />
            News Management
          </h1>
          <p className="mt-2 text-gray-600">
            Manage news articles and announcements
          </p>
        </div>
        {isEditor() && (
          <Link href="/dashboard/news/create" passHref>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Plus className="mr-2 h-4 w-4" />
              Create News
            </Button>
          </Link>
        )}
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="mr-2 h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by title, content, or author..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Button type="submit" variant="outline">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
            {filters.search && (
              <Button
                type="button"
                variant="ghost"
                onClick={() => {
                  setSearchQuery('');
                  handleFilterChange('search', undefined);
                }}
              >
                Clear
              </Button>
            )}
          </form>

          <div className="flex flex-wrap gap-4">
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">Status:</label>
              <select
                value={filters.status || 'all'}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange('status',
                    value === 'all' ? undefined : value
                  );
                }}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="all">All</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
                <option value="archived">Archived</option>
              </select>
            </div>

            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium">Featured:</label>
              <select
                value={filters.isFeatured?.toString() || 'all'}
                onChange={(e) => {
                  const value = e.target.value;
                  handleFilterChange('isFeatured',
                    value === 'all' ? undefined : value === 'true'
                  );
                }}
                className="px-3 py-1 border rounded-md text-sm"
              >
                <option value="all">All</option>
                <option value="true">Featured Only</option>
                <option value="false">Regular</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardContent className="p-0">
          <DataTable
            columns={columns}
            data={newsData?.data || []}
            loading={isLoading}
            pagination={{
              page: filters.page || 1,
              limit: filters.limit || 20,
              total: newsData?.meta?.totalItems || 0,
              onPageChange: (page) => handleFilterChange('page', page),
              onLimitChange: (limit) => handleFilterChange('limit', limit),
            }}
            emptyMessage="No news found. Create your first news article!"
          />
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete News"
        message={`Are you sure you want to delete "${newsToDelete?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        loading={deleteNewsMutation.isLoading}
        variant="destructive"
      />
    </div>
  );
}
