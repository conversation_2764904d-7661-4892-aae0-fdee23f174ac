'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { InputField, SelectField, FormSection, FormActions } from '@/components/ui/form-field';
import { ToggleSwitch } from '@/components/ui/toggle-switch';
import { Skeleton } from '@/components/ui/skeleton';
import { useNewsById, useUpdateNews } from '@/lib/hooks/useNews';
import { usePublicCategories } from '@/lib/hooks/useCategories';
import { UpdateNewsData } from '@/lib/api/news';
import { ArrowLeft, Save, Newspaper, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface NewsFormData {
  title: string;
  content: string;
  excerpt: string;
  featuredImage: string;
  tags: string;
  categoryId: string;
  status: 'draft' | 'published' | 'archived';
  isFeatured: boolean;
  publishDate: string;
  publishTime: string;
  metaTitle: string;
  metaDescription: string;
}

export default function EditNewsPage() {
  const params = useParams();
  const router = useRouter();
  const newsId = parseInt(params.id as string);
  const [formData, setFormData] = useState<NewsFormData>({
    title: '',
    content: '',
    excerpt: '',
    featuredImage: '',
    tags: '',
    categoryId: '',
    status: 'draft',
    isFeatured: false,
    publishDate: '',
    publishTime: '',
    metaTitle: '',
    metaDescription: '',
  });

  const [errors, setErrors] = useState<Partial<NewsFormData>>({});
  // Data fetching
  const { data: news, isLoading: newsLoading, error } = useNewsById(newsId);
  const { data: categories = [], isLoading: isLoadingCategories } = usePublicCategories();

  // Mutations
  const updateNewsMutation = useUpdateNews();

  // Populate form when news data loads
  useEffect(() => {
    if (news) {
      const publishDate = new Date(news.publishedAt || news.createdAt);
      setFormData({
        title: news.title,
        content: news.content,
        excerpt: news.excerpt || '',
        featuredImage: news.featuredImage || '',
        tags: news.tags ? news.tags.join(', ') : '',
        categoryId: news.categoryId ? news.categoryId.toString() : '',
        status: news.status,
        isFeatured: news.isFeatured,
        publishDate: publishDate.toISOString().split('T')[0],
        publishTime: publishDate.toTimeString().slice(0, 5),
        metaTitle: news.metaTitle || '',
        metaDescription: news.metaDescription || '',
      });
    }
  }, [news]);

  const validateForm = (): boolean => {
    const newErrors: Partial<NewsFormData> = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.content.trim()) newErrors.content = 'Content is required';
    if (!formData.publishDate) newErrors.publishDate = 'Publish date is required';
    if (!formData.publishTime) newErrors.publishTime = 'Publish time is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    // Combine date and time
    const publishDateTime = new Date(`${formData.publishDate}T${formData.publishTime}`);

    // Parse tags
    const tagsArray = formData.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0);

    const submitData: UpdateNewsData = {
      title: formData.title,
      content: formData.content,
      excerpt: formData.excerpt || undefined,
      featuredImage: formData.featuredImage || undefined,
      tags: tagsArray.length > 0 ? tagsArray : undefined,
      categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,
      status: formData.status,
      publishedAt: formData.status === 'published' ? publishDateTime.toISOString() : undefined,
      isFeatured: formData.isFeatured,
      metaTitle: formData.metaTitle || undefined,
      metaDescription: formData.metaDescription || undefined,
    };

    try {
      await updateNewsMutation.mutateAsync({ id: newsId, data: submitData });
      router.push(`/dashboard/news/${newsId}`);
    } catch (error) {
      // Error handled by mutation
    }
  };

  const updateFormData = (field: keyof NewsFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (newsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-20" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-10" />
              <Skeleton className="h-10" />
              <Skeleton className="h-32" />
            </div>
            <div className="flex justify-end space-x-3">
              <Skeleton className="h-10 w-20" />
              <Skeleton className="h-10 w-32" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !news) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-medium text-red-600 mb-2">
                {error ? 'Failed to load news' : 'News not found'}
              </h3>              <p className="text-gray-600 mb-4">
                {(error as Error)?.message || 'The news article you are trying to edit does not exist.'}
              </p>
              <Button onClick={() => router.push('/dashboard/news')}>
                Return to News
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Newspaper className="mr-3 h-8 w-8 text-blue-600" />
            Edit News Article
          </h1>
          <p className="text-gray-600 mt-1">Update the article information</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>Edit Article: {news.title}</CardTitle>
          <CardDescription>
            Update the information below to modify the news article
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormSection title="Basic Information" description="Essential article details">
              <div className="space-y-4">
                <InputField
                  label="Title *"
                  placeholder="Enter article title"
                  required
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  error={errors.title}
                  description="The main headline of your news article"
                />

                <InputField
                  label="Excerpt"
                  placeholder="Brief excerpt of the article"
                  value={formData.excerpt}
                  onChange={(e) => updateFormData('excerpt', e.target.value)}
                  error={errors.excerpt}
                  description="A short description that appears in previews and lists"
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Content *</label>
                  <textarea
                    placeholder="Write your article content here..."
                    required
                    value={formData.content}
                    onChange={(e) => updateFormData('content', e.target.value)}
                    className="min-h-[300px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                  />
                  {errors.content && (
                    <p className="text-sm text-red-600">{errors.content}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Write the full content of your news article. HTML tags are supported.
                  </p>
                </div>
              </div>
            </FormSection>            <FormSection title="Category & Media" description="Article categorization and media assets">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <SelectField
                  label="Category"
                  placeholder="Select a category"
                  value={formData.categoryId}
                  onValueChange={(value) => updateFormData('categoryId', value)}
                  error={errors.categoryId}
                  options={categories.map((category: any) => ({
                    value: category.id.toString(),
                    label: category.name
                  }))}
                  disabled={isLoadingCategories}
                  description="Choose the news category"
                />

                <InputField
                  label="Featured Image URL"
                  placeholder="https://example.com/image.jpg"
                  value={formData.featuredImage}
                  onChange={(e) => updateFormData('featuredImage', e.target.value)}
                  error={errors.featuredImage}
                  description="Optional image to display with the article"
                />
              </div>

              <InputField
                label="Tags"
                placeholder="sport, football, news, breaking"
                value={formData.tags}
                onChange={(e) => updateFormData('tags', e.target.value)}
                description="Comma-separated tags for categorization"
              />
            </FormSection>

            <FormSection title="Publishing Settings" description="Publication date and visibility options">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Publish Date *"
                  type="date"
                  required
                  value={formData.publishDate}
                  onChange={(e) => updateFormData('publishDate', e.target.value)}
                  error={errors.publishDate}
                />

                <InputField
                  label="Publish Time *"
                  type="time"
                  required
                  value={formData.publishTime}
                  onChange={(e) => updateFormData('publishTime', e.target.value)}
                  error={errors.publishTime}
                />
              </div>

              <div className="space-y-4">
                <ToggleSwitch
                  checked={formData.status === 'published'}
                  onCheckedChange={(checked) => updateFormData('status', checked ? 'published' : 'draft')}
                  label="Publish Article"
                  description="Make this article visible to the public"
                />

                <ToggleSwitch
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) => updateFormData('isFeatured', checked)}
                  label="Featured Article"
                  description="Mark this as a featured/trending article"
                  variant="danger"
                />
              </div>
            </FormSection>

            <FormSection title="SEO Settings" description="Search engine optimization settings">
              <div className="space-y-4">
                <InputField
                  label="Meta Title"
                  placeholder="SEO-optimized title for search engines"
                  value={formData.metaTitle}
                  onChange={(e) => updateFormData('metaTitle', e.target.value)}
                  error={errors.metaTitle}
                  description="Title tag for search engines (max 200 characters)"
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Meta Description</label>
                  <textarea
                    placeholder="Brief description for search engine results"
                    value={formData.metaDescription}
                    onChange={(e) => updateFormData('metaDescription', e.target.value)}
                    className="min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                  />
                  {errors.metaDescription && (
                    <p className="text-sm text-red-600">{errors.metaDescription}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Description for search engine results (max 500 characters)
                  </p>
                </div>
              </div>
            </FormSection>

            <FormActions>              <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={updateNewsMutation.isLoading}
            >
              Cancel
            </Button>              <Button
              type="submit"
              disabled={updateNewsMutation.isLoading}
            >                <Save className="mr-2 h-4 w-4" />
                {updateNewsMutation.isLoading ? 'Updating...' : 'Update Article'}
              </Button>
            </FormActions>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
