'use client';

import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ConfirmModal } from '@/components/ui/modal';
import { ToggleSwitch } from '@/components/ui/toggle-switch';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useNewsById, useDeleteNews, useToggleNewsStatus, useToggleHotStatus } from '@/lib/hooks/useNews';
import { Skeleton } from '@/components/ui/skeleton';
import {
  ArrowLeft,
  Edit,
  Trash2,
  Calendar,
  User,
  TrendingUp,
  Eye,
  Globe,
  Image as ImageIcon,
  Tag,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

export default function NewsDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { isEditor, isAdmin } = usePermissions();
  const newsId = parseInt(params.id as string);

  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  // Data fetching
  const { data: news, isLoading, error } = useNewsById(newsId);

  // Mutations
  const deleteNewsMutation = useDeleteNews();
  const toggleStatusMutation = useToggleNewsStatus();
  const toggleHotMutation = useToggleHotStatus();

  // Event handlers
  const handleEdit = () => {
    if (!isEditor() && !isAdmin()) {
      toast.error('You do not have permission to edit news');
      return;
    }
    router.push(`/dashboard/news/${newsId}/edit`);
  };

  const handleDelete = () => {
    if (!isAdmin()) {
      toast.error('You do not have permission to delete news');
      return;
    }
    setDeleteModalOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await deleteNewsMutation.mutateAsync(newsId);
      setDeleteModalOpen(false);
      router.push('/dashboard/news');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const handleToggleStatus = (isPublished: boolean) => {
    toggleStatusMutation.mutate({ id: newsId, isPublished });
  };

  const handleToggleHot = (isHot: boolean) => {
    toggleHotMutation.mutate({ id: newsId, isHot });
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-20" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-40 w-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !news) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-lg font-medium text-red-600 mb-2">
                {error ? 'Failed to load news' : 'News not found'}
              </h3>              <p className="text-gray-600 mb-4">
                {(error as Error)?.message || 'The news article you are looking for does not exist or has been removed.'}
              </p>
              <Button onClick={() => router.push('/dashboard/news')}>
                Return to News
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{news.title}</h1>
            <p className="text-gray-600 mt-1">News Article Details</p>
          </div>
        </div>
        <div className="flex space-x-2">
          {isEditor() && (
            <Button variant="outline" onClick={handleEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
          )}
          {isAdmin() && (
            <Button variant="outline" onClick={handleDelete} className="text-red-600 hover:text-red-700">
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Article Content */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Badge variant={news.status === 'published' ? 'default' : 'secondary'}>
                    {news.status === 'published' ? 'Published' : news.status === 'draft' ? 'Draft' : 'Archived'}
                  </Badge>
                  {news.isFeatured && (
                    <Badge variant="destructive">
                      <TrendingUp className="w-3 h-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                </div>
                <div className="text-sm text-gray-500">
                  <Calendar className="inline w-4 h-4 mr-1" />
                  {new Date(news.publishedAt || news.createdAt).toLocaleDateString()}
                </div>
              </div>
              <CardTitle className="text-2xl">{news.title}</CardTitle>
              {news.excerpt && (
                <CardDescription className="text-lg">{news.excerpt}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {news.featuredImage && (
                <div className="mb-6">
                  <img
                    src={news.featuredImage}
                    alt={news.title}
                    className="w-full h-64 object-cover rounded-lg"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: news.content }} />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Article Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Article Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Author</p>
                  <p className="text-sm text-gray-600">Author {news.authorId}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Publish Date</p>
                  <p className="text-sm text-gray-600">
                    {news.publishedAt ? new Date(news.publishedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    }) : 'Not published yet'}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Globe className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Created</p>
                  <p className="text-sm text-gray-600">
                    {new Date(news.createdAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Eye className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm font-medium">Last Updated</p>
                  <p className="text-sm text-gray-600">
                    {new Date(news.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {news.featuredImage && (
                <div className="flex items-center space-x-3">
                  <ImageIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">Featured Image</p>
                    <p className="text-sm text-gray-600">Available</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tags */}
          {news.tags && news.tags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Tag className="mr-2 h-5 w-5" />
                  Tags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {news.tags.map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          {(isEditor() || isAdmin()) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Published Status</span>                    <ToggleSwitch
                      checked={news.status === 'published'}
                      onCheckedChange={handleToggleStatus}
                      disabled={toggleStatusMutation.isLoading}
                      size="sm"
                      label="Published Status"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Featured Article</span>                    <ToggleSwitch
                      checked={news.isFeatured}
                      onCheckedChange={handleToggleHot}
                      disabled={toggleHotMutation.isLoading}
                      size="sm"
                      variant="danger"
                      label="Featured Article"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="Delete News Article"
        message={`Are you sure you want to delete "${news.title}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        loading={deleteNewsMutation.isLoading}
        variant="destructive"
      />
    </div>
  );
}
