'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, Tag, FileText, Eye } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useCreateCategory } from '@/lib/hooks/useCategories';

const createCategorySchema = z.object({
        name: z.string().min(1, 'Category name is required').max(100, 'Name must be less than 100 characters'),
        slug: z.string().min(1, 'Slug is required').max(100, 'Slug must be less than 100 characters'),
        description: z.string().optional(),
        isActive: z.boolean(),
});

type CreateCategoryFormData = z.infer<typeof createCategorySchema>;

export default function CreateCategoryPage() {
        const router = useRouter();
        const { toast } = useToast();
        const { mutate: createCategory, isLoading: isCreating } = useCreateCategory();

        const form = useForm<CreateCategoryFormData>({
                resolver: zodResolver(createCategorySchema),
                defaultValues: {
                        name: '',
                        slug: '',
                        description: '',
                        isActive: true,
                },
        });

        // Auto-generate slug from name
        const watchedName = form.watch('name');

        const generateSlug = (name: string) => {
                return name
                        .toLowerCase()
                        .trim()
                        .replace(/[^\w\s-]/g, '') // Remove special characters
                        .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
                        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
        };

        // Update slug when name changes (if slug is empty or matches previous auto-generated slug)
        useState(() => {
                if (watchedName) {
                        const currentSlug = form.getValues('slug');
                        const newSlug = generateSlug(watchedName);

                        // Only update if slug is empty or looks auto-generated
                        if (!currentSlug || currentSlug === generateSlug(form.getValues('name'))) {
                                form.setValue('slug', newSlug);
                        }
                }
        });

        const onSubmit = (data: CreateCategoryFormData) => {
                createCategory(data, {
                        onSuccess: () => {
                                toast({
                                        title: 'Category created',
                                        description: 'News category has been successfully created.',
                                });
                                router.push('/dashboard/news/categories');
                        },
                        onError: (error: any) => {
                                toast({
                                        title: 'Error',
                                        description: error?.message || 'Failed to create category.',
                                        variant: 'destructive',
                                });
                        },
                });
        };

        const handleCancel = () => {
                router.push('/dashboard/news/categories');
        };

        return (
                <div className="container mx-auto py-6 space-y-6">
                        {/* Header */}
                        <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                        <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={handleCancel}
                                                className="h-8 w-8 p-0"
                                        >
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <h1 className="text-2xl font-bold">Create News Category</h1>
                                                <p className="text-muted-foreground">
                                                        Add a new category for organizing news articles
                                                </p>
                                        </div>
                                </div>
                        </div>

                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid gap-6 md:grid-cols-2">
                                        {/* Main Information */}
                                        <Card>
                                                <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                                <Tag className="h-5 w-5" />
                                                                Basic Information
                                                        </CardTitle>
                                                        <CardDescription>
                                                                Enter the basic details for the news category
                                                        </CardDescription>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                        {/* Category Name */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="name">Category Name *</Label>
                                                                <Input
                                                                        id="name"
                                                                        placeholder="e.g., Sports News, Technology, Politics"
                                                                        {...form.register('name')}
                                                                        className={form.formState.errors.name ? 'border-red-500' : ''}
                                                                />
                                                                {form.formState.errors.name && (
                                                                        <p className="text-sm text-red-500">
                                                                                {form.formState.errors.name.message}
                                                                        </p>
                                                                )}
                                                        </div>

                                                        {/* Slug */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="slug">URL Slug *</Label>
                                                                <Input
                                                                        id="slug"
                                                                        placeholder="e.g., sports-news, technology, politics"
                                                                        {...form.register('slug')}
                                                                        className={form.formState.errors.slug ? 'border-red-500' : ''}
                                                                />
                                                                {form.formState.errors.slug && (
                                                                        <p className="text-sm text-red-500">
                                                                                {form.formState.errors.slug.message}
                                                                        </p>
                                                                )}
                                                                <p className="text-xs text-muted-foreground">
                                                                        URL-friendly version of the category name. Will be auto-generated from name.
                                                                </p>
                                                        </div>

                                                        {/* Description */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="description">Description</Label>
                                                                <Textarea
                                                                        id="description"
                                                                        placeholder="Brief description of this category..."
                                                                        rows={4}
                                                                        {...form.register('description')}
                                                                />
                                                                <p className="text-xs text-muted-foreground">
                                                                        Optional description to help users understand this category
                                                                </p>
                                                        </div>
                                                </CardContent>
                                        </Card>

                                        {/* Settings */}
                                        <Card>
                                                <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                                <Eye className="h-5 w-5" />
                                                                Category Settings
                                                        </CardTitle>
                                                        <CardDescription>
                                                                Configure visibility and status settings
                                                        </CardDescription>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                        {/* Active Status */}
                                                        <div className="flex items-center justify-between">
                                                                <div className="space-y-0.5">
                                                                        <Label htmlFor="isActive">Active Status</Label>
                                                                        <p className="text-sm text-muted-foreground">
                                                                                Enable this category for use in news articles
                                                                        </p>
                                                                </div>
                                                                <Switch
                                                                        id="isActive"
                                                                        checked={form.watch('isActive')}
                                                                        onCheckedChange={(checked) => form.setValue('isActive', checked)}
                                                                />
                                                        </div>

                                                        <Separator />

                                                        {/* Preview Information */}
                                                        <div className="space-y-3">
                                                                <h4 className="text-sm font-medium">Preview</h4>
                                                                <div className="rounded-lg border p-3 space-y-2">
                                                                        <div className="flex items-center gap-2">
                                                                                <Tag className="h-4 w-4" />
                                                                                <span className="font-medium">
                                                                                        {form.watch('name') || 'Category Name'}
                                                                                </span>
                                                                                {!form.watch('isActive') && (
                                                                                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                                                                                Inactive
                                                                                        </span>
                                                                                )}
                                                                        </div>
                                                                        {form.watch('slug') && (
                                                                                <p className="text-xs text-muted-foreground">
                                                                                        URL: /news/category/{form.watch('slug')}
                                                                                </p>
                                                                        )}
                                                                        {form.watch('description') && (
                                                                                <p className="text-sm text-muted-foreground">
                                                                                        {form.watch('description')}
                                                                                </p>
                                                                        )}
                                                                </div>
                                                        </div>
                                                </CardContent>
                                        </Card>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center justify-end gap-4">
                                        <Button
                                                type="button"
                                                variant="outline"
                                                onClick={handleCancel}
                                                disabled={isCreating}
                                        >
                                                Cancel
                                        </Button>
                                        <Button
                                                type="submit"
                                                disabled={isCreating || !form.formState.isValid}
                                                className="flex items-center gap-2"
                                        >
                                                <Save className="h-4 w-4" />
                                                {isCreating ? 'Creating...' : 'Create Category'}
                                        </Button>
                                </div>
                        </form>
                </div>
        );
}