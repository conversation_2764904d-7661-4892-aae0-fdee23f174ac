'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column } from '@/components/ui/data-table';
import { ConfirmModal } from '@/components/ui/modal';
import { ToggleSwitch } from '@/components/ui/toggle-switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { useCategories, useDeleteCategory, useToggleCategoryStatus } from '@/lib/hooks/useCategories';
import { CategoryFilters } from '@/lib/types/api';
import { NewsCategory } from '@/lib/types/api';
import { toast } from 'sonner';
import {
        FolderOpen,
        Plus,
        Search,
        Filter,
        Eye,
        Edit,
        Trash2,
        ToggleLeft,
        ToggleRight,
        AlertTriangle,
        Tag,
        Calendar,
        FileText
} from 'lucide-react';
import Link from 'next/link';

export default function NewsCategoriesPage() {
        const router = useRouter();
        const queryClient = useQueryClient();
        const { isEditor, isAdmin } = usePermissions();

        // State for filtering
        const [filters, setFilters] = useState<CategoryFilters>({
                page: 1,
                limit: 20,
        });
        const [searchQuery, setSearchQuery] = useState('');

        // State for delete modal
        const [deleteModalOpen, setDeleteModalOpen] = useState(false);
        const [categoryToDelete, setCategoryToDelete] = useState<NewsCategory | null>(null);

        // Data fetching
        const {
                data: categoriesData,
                isLoading,
                error,
                refetch
        } = useCategories(filters);

        // Mutations
        const deleteCategory = useDeleteCategory();
        const toggleStatus = useToggleCategoryStatus();

        const handleSearch = () => {
                setFilters(prev => ({
                        ...prev,
                        search: searchQuery,
                        page: 1
                }));
        };

        const handleKeyPress = (e: React.KeyboardEvent) => {
                if (e.key === 'Enter') {
                        handleSearch();
                }
        };

        const handleStatusFilter = (value: string) => {
                const isActive = value === 'all' ? undefined : value === 'active';
                setFilters(prev => ({
                        ...prev,
                        isActive,
                        page: 1
                }));
        };

        const handlePublicFilter = (value: string) => {
                const isPublic = value === 'all' ? undefined : value === 'public';
                setFilters(prev => ({
                        ...prev,
                        isPublic,
                        page: 1
                }));
        };

        const handleDeleteClick = (category: NewsCategory) => {
                setCategoryToDelete(category);
                setDeleteModalOpen(true);
        };

        const handleDeleteConfirm = async () => {
                if (!categoryToDelete) return;

                try {
                        await deleteCategory.mutateAsync(categoryToDelete.id);
                        setDeleteModalOpen(false);
                        setCategoryToDelete(null);
                } catch (error) {
                        // Error is handled by the mutation hook
                }
        };

        const handleToggleStatus = async (category: NewsCategory) => {
                try {
                        await toggleStatus.mutateAsync({
                                id: category.id,
                                isActive: !category.isActive
                        });
                } catch (error) {
                        // Error is handled by the mutation hook
                }
        };

        const getStatusColor = (isActive: boolean) => {
                return isActive
                        ? 'bg-green-100 text-green-800 border-green-200'
                        : 'bg-red-100 text-red-800 border-red-200';
        };

        const columns: Column<NewsCategory>[] = [
                {
                        key: 'name',
                        title: 'Category',
                        render: (value, category) => (
                                <div className="flex items-center space-x-3">
                                        <div
                                                className="w-4 h-4 rounded-full flex-shrink-0"
                                                style={{ backgroundColor: category.color || '#6B7280' }}
                                        />
                                        <div>
                                                <div className="font-medium">{category.name}</div>
                                                <div className="text-sm text-gray-500">/{category.slug}</div>
                                        </div>
                                </div>
                        ),
                },
                {
                        key: 'description',
                        title: 'Description',
                        render: (value) => (
                                <div className="max-w-xs truncate text-gray-600">
                                        {value || 'No description'}
                                </div>
                        ),
                },
                {
                        key: 'articleCount',
                        title: 'Articles',
                        render: (value, category) => (
                                <div className="text-center">
                                        <div className="font-medium">{category.publishedArticleCount || 0}</div>
                                        <div className="text-xs text-gray-500">
                                                {category.articleCount || 0} total
                                        </div>
                                </div>
                        ),
                },
                {
                        key: 'isActive',
                        title: 'Status',
                        render: (value, category) => (
                                <div className="flex items-center space-x-2">
                                        <Badge variant="outline" className={getStatusColor(value)}>
                                                {value ? 'Active' : 'Inactive'}
                                        </Badge>
                                        {category.isPublic && (
                                                <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                                                        Public
                                                </Badge>
                                        )}
                                </div>
                        ),
                },
                {
                        key: 'sortOrder',
                        title: 'Order',
                        render: (value) => (
                                <div className="text-center font-mono text-sm">
                                        {value}
                                </div>
                        ),
                },
                {
                        key: 'actions',
                        title: 'Actions',
                        render: (_, category) => (
                                <div className="flex items-center space-x-2">
                                        <Link href={`/dashboard/news/categories/${category.id}`}>
                                                <Button variant="ghost" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                </Button>
                                        </Link>

                                        {(isEditor() || isAdmin()) && (
                                                <Link href={`/dashboard/news/categories/${category.id}/edit`}>
                                                        <Button variant="ghost" size="sm">
                                                                <Edit className="h-4 w-4" />
                                                        </Button>
                                                </Link>
                                        )}

                                        {(isEditor() || isAdmin()) && (
                                                <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleToggleStatus(category)}
                                                        disabled={toggleStatus.isLoading}
                                                >
                                                        {category.isActive ? (
                                                                <ToggleRight className="h-4 w-4 text-green-600" />
                                                        ) : (
                                                                <ToggleLeft className="h-4 w-4 text-gray-400" />
                                                        )}
                                                </Button>
                                        )}

                                        {isAdmin() && (
                                                <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleDeleteClick(category)}
                                                        disabled={deleteCategory.isLoading}
                                                        className="text-red-600 hover:text-red-700"
                                                >
                                                        <Trash2 className="h-4 w-4" />
                                                </Button>
                                        )}
                                </div>
                        ),
                },
        ];

        if (error) {
                return (
                        <div className="container mx-auto px-4 py-8">
                                <Card>
                                        <CardContent className="flex flex-col items-center justify-center py-12">
                                                <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
                                                <h3 className="text-lg font-semibold mb-2">Failed to load categories</h3>
                                                <p className="text-gray-600 mb-4">
                                                        {error instanceof Error ? error.message : 'An unexpected error occurred'}
                                                </p>
                                                <Button onClick={() => refetch()}>
                                                        Try Again
                                                </Button>
                                        </CardContent>
                                </Card>
                        </div>
                );
        }

        return (
                <div className="container mx-auto px-4 py-8">
                        {/* Header */}
                        <div className="flex justify-between items-center mb-8">
                                <div>
                                        <h1 className="text-3xl font-bold flex items-center gap-2">
                                                <FolderOpen className="h-8 w-8" />
                                                News Categories
                                        </h1>
                                        <p className="text-gray-600 mt-1">
                                                Manage news categories and their settings
                                        </p>
                                </div>
                                {(isEditor() || isAdmin()) && (
                                        <Link href="/dashboard/news/categories/create">
                                                <Button>
                                                        <Plus className="h-4 w-4 mr-2" />
                                                        Add Category
                                                </Button>
                                        </Link>
                                )}
                        </div>

                        {/* Statistics Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                                <Card>
                                        <CardContent className="flex items-center p-6">
                                                <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-blue-100 rounded-lg">
                                                                <Tag className="h-5 w-5 text-blue-600" />
                                                        </div>
                                                        <div>
                                                                <p className="text-sm font-medium text-gray-500">Total Categories</p>
                                                                <p className="text-2xl font-bold">{categoriesData?.meta?.totalItems || 0}</p>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>

                                <Card>
                                        <CardContent className="flex items-center p-6">
                                                <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-green-100 rounded-lg">
                                                                <ToggleRight className="h-5 w-5 text-green-600" />
                                                        </div>
                                                        <div>
                                                                <p className="text-sm font-medium text-gray-500">Active</p>
                                                                <p className="text-2xl font-bold">
                                                                        {categoriesData?.data?.filter((c: any) => c.isActive).length || 0}
                                                                </p>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>

                                <Card>
                                        <CardContent className="flex items-center p-6">
                                                <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-purple-100 rounded-lg">
                                                                <Eye className="h-5 w-5 text-purple-600" />
                                                        </div>
                                                        <div>
                                                                <p className="text-sm font-medium text-gray-500">Public</p>
                                                                <p className="text-2xl font-bold">
                                                                        {categoriesData?.data?.filter((c: any) => c.isPublic).length || 0}
                                                                </p>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>

                                <Card>
                                        <CardContent className="flex items-center p-6">
                                                <div className="flex items-center space-x-3">
                                                        <div className="p-2 bg-orange-100 rounded-lg">
                                                                <FileText className="h-5 w-5 text-orange-600" />
                                                        </div>
                                                        <div>
                                                                <p className="text-sm font-medium text-gray-500">Total Articles</p>
                                                                <p className="text-2xl font-bold">
                                                                        {categoriesData?.data?.reduce((sum: number, c: any) => sum + (c.articleCount || 0), 0) || 0}
                                                                </p>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>
                        </div>

                        {/* Filters */}
                        <Card className="mb-6">
                                <CardContent className="p-6">
                                        <div className="flex flex-col md:flex-row gap-4">
                                                <div className="flex-1">
                                                        <div className="relative">
                                                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                                                <Input
                                                                        placeholder="Search categories..."
                                                                        value={searchQuery}
                                                                        onChange={(e) => setSearchQuery(e.target.value)}
                                                                        onKeyPress={handleKeyPress}
                                                                        className="pl-10"
                                                                />
                                                        </div>
                                                </div>

                                                <Select onValueChange={handleStatusFilter}>
                                                        <SelectTrigger className="w-full md:w-48">
                                                                <SelectValue placeholder="Filter by status" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                                <SelectItem value="all">All Status</SelectItem>
                                                                <SelectItem value="active">Active</SelectItem>
                                                                <SelectItem value="inactive">Inactive</SelectItem>
                                                        </SelectContent>
                                                </Select>

                                                <Select onValueChange={handlePublicFilter}>
                                                        <SelectTrigger className="w-full md:w-48">
                                                                <SelectValue placeholder="Filter by visibility" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                                <SelectItem value="all">All Visibility</SelectItem>
                                                                <SelectItem value="public">Public</SelectItem>
                                                                <SelectItem value="private">Private</SelectItem>
                                                        </SelectContent>
                                                </Select>

                                                <Button onClick={handleSearch} className="w-full md:w-auto">
                                                        <Filter className="h-4 w-4 mr-2" />
                                                        Search
                                                </Button>
                                        </div>
                                </CardContent>
                        </Card>

                        {/* Data Table */}
                        <Card>
                                <CardContent className="p-0">
                                        <DataTable
                                                data={categoriesData?.data || []}
                                                columns={columns}
                                                loading={isLoading}
                                                pagination={{
                                                        page: filters.page || 1,
                                                        limit: filters.limit || 20,
                                                        total: categoriesData?.meta?.totalItems || 0,
                                                        onPageChange: (page) => setFilters(prev => ({ ...prev, page })),
                                                        onLimitChange: (limit) => setFilters(prev => ({ ...prev, limit, page: 1 })),
                                                }}
                                        />
                                </CardContent>
                        </Card>

                        {/* Delete Confirmation Modal */}
                        <ConfirmModal
                                isOpen={deleteModalOpen}
                                onClose={() => {
                                        setDeleteModalOpen(false);
                                        setCategoryToDelete(null);
                                }}
                                onConfirm={handleDeleteConfirm}
                                title="Delete Category"
                                message={
                                        categoryToDelete
                                                ? `Are you sure you want to delete "${categoryToDelete.name}"? This action cannot be undone and may affect associated articles.`
                                                : ''
                                }
                                confirmText="Delete"
                                cancelText="Cancel"
                                loading={deleteCategory.isLoading}
                                variant="destructive"
                        />
                </div>
        );
}