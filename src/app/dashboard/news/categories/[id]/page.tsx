'use client';

import { useParams, useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, Tag, Calendar, Eye, EyeOff, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useCategory, useDeleteCategory } from '@/lib/hooks/useCategories';
import { ConfirmModal } from '@/components/ui/modal';
import { useState } from 'react';

// Helper function to format dates
const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
        });
};

export default function CategoryDetailPage() {
        const params = useParams();
        const router = useRouter();
        const { toast } = useToast();
        const categoryId = params.id as string;

        const { data: category, isLoading, error } = useCategory(Number(categoryId));
        const { mutate: deleteCategory, isLoading: isDeleting } = useDeleteCategory();

        const [showDeleteModal, setShowDeleteModal] = useState(false);

        const handleEdit = () => {
                router.push(`/dashboard/news/categories/${categoryId}/edit`);
        };

        const handleDelete = () => {
                deleteCategory(Number(categoryId), {
                        onSuccess: () => {
                                toast({
                                        title: 'Category deleted',
                                        description: 'News category has been successfully deleted.',
                                });
                                router.push('/dashboard/news/categories');
                        },
                        onError: (error: any) => {
                                toast({
                                        title: 'Error',
                                        description: error?.message || 'Failed to delete category.',
                                        variant: 'destructive',
                                });
                        },
                });
        };

        const handleBack = () => {
                router.push('/dashboard/news/categories');
        };

        if (isLoading) {
                return (
                        <div className="container mx-auto py-6">
                                <div className="flex items-center gap-4 mb-6">
                                        <Button variant="ghost" size="sm" onClick={handleBack} className="h-8 w-8 p-0">
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
                                                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse mt-2" />
                                        </div>
                                </div>
                                <div className="grid gap-6 md:grid-cols-2">
                                        <Card>
                                                <CardHeader>
                                                        <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
                                                        <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                        <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                                                        <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                                                        <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse" />
                                                </CardContent>
                                        </Card>
                                </div>
                        </div>
                );
        }

        if (error || !category) {
                return (
                        <div className="container mx-auto py-6">
                                <div className="flex items-center gap-4 mb-6">
                                        <Button variant="ghost" size="sm" onClick={handleBack} className="h-8 w-8 p-0">
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <h1 className="text-2xl font-bold">Category Not Found</h1>
                                                <p className="text-muted-foreground">The requested category could not be found.</p>
                                        </div>
                                </div>
                                <Card>
                                        <CardContent className="py-8">
                                                <div className="text-center">
                                                        <Tag className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                                                        <h3 className="text-lg font-medium mb-2">Category Not Found</h3>
                                                        <p className="text-muted-foreground mb-4">
                                                                The category you're looking for doesn't exist or may have been deleted.
                                                        </p>
                                                        <Button onClick={handleBack}>Go Back to Categories</Button>
                                                </div>
                                        </CardContent>
                                </Card>
                        </div>
                );
        }

        return (
                <div className="container mx-auto py-6 space-y-6">
                        {/* Header */}
                        <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                        <Button variant="ghost" size="sm" onClick={handleBack} className="h-8 w-8 p-0">
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <div className="flex items-center gap-3">
                                                        <h1 className="text-2xl font-bold">{category.name}</h1>
                                                        <Badge variant={category.isActive ? 'default' : 'secondary'}>
                                                                {category.isActive ? (
                                                                        <>
                                                                                <Eye className="h-3 w-3 mr-1" />
                                                                                Active
                                                                        </>
                                                                ) : (
                                                                        <>
                                                                                <EyeOff className="h-3 w-3 mr-1" />
                                                                                Inactive
                                                                        </>
                                                                )}
                                                        </Badge>
                                                </div>
                                                <p className="text-muted-foreground">Category details and management</p>
                                        </div>
                                </div>
                                <div className="flex items-center gap-2">
                                        <Button variant="outline" onClick={handleEdit}>
                                                <Edit className="h-4 w-4 mr-2" />
                                                Edit
                                        </Button>
                                        <Button
                                                variant="destructive"
                                                onClick={() => setShowDeleteModal(true)}
                                                disabled={isDeleting}
                                        >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Delete
                                        </Button>
                                </div>
                        </div>

                        <div className="grid gap-6 md:grid-cols-2">
                                {/* Basic Information */}
                                <Card>
                                        <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                        <Tag className="h-5 w-5" />
                                                        Basic Information
                                                </CardTitle>
                                                <CardDescription>Category details and settings</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                                <div className="grid gap-3">
                                                        <div>
                                                                <Label className="text-sm font-medium text-gray-600">Name</Label>
                                                                <p className="text-sm font-medium">{category.name}</p>
                                                        </div>

                                                        <div>
                                                                <Label className="text-sm font-medium text-gray-600">Slug</Label>
                                                                <p className="text-sm font-mono bg-gray-50 px-2 py-1 rounded">
                                                                        {category.slug}
                                                                </p>
                                                        </div>

                                                        {category.description && (
                                                                <div>
                                                                        <Label className="text-sm font-medium text-gray-600">Description</Label>
                                                                        <p className="text-sm text-gray-700">{category.description}</p>
                                                                </div>
                                                        )}

                                                        <div>
                                                                <Label className="text-sm font-medium text-gray-600">Status</Label>
                                                                <div className="flex items-center gap-2">
                                                                        {category.isActive ? (
                                                                                <Badge variant="default" className="text-xs">
                                                                                        <Eye className="h-3 w-3 mr-1" />
                                                                                        Active
                                                                                </Badge>
                                                                        ) : (
                                                                                <Badge variant="secondary" className="text-xs">
                                                                                        <EyeOff className="h-3 w-3 mr-1" />
                                                                                        Inactive
                                                                                </Badge>
                                                                        )}
                                                                </div>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>

                                {/* Meta Information */}
                                <Card>
                                        <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                        <Calendar className="h-5 w-5" />
                                                        Meta Information
                                                </CardTitle>
                                                <CardDescription>Creation and modification details</CardDescription>
                                        </CardHeader>
                                        <CardContent className="space-y-4">
                                                <div className="grid gap-3">
                                                        <div>
                                                                <Label className="text-sm font-medium text-gray-600">ID</Label>
                                                                <p className="text-sm font-mono">#{category.id}</p>
                                                        </div>

                                                        {category.createdAt && (
                                                                <div>
                                                                        <Label className="text-sm font-medium text-gray-600">Created</Label>
                                                                        <p className="text-sm">{formatDate(category.createdAt)}</p>
                                                                </div>
                                                        )}

                                                        {category.updatedAt && (
                                                                <div>
                                                                        <Label className="text-sm font-medium text-gray-600">Last Modified</Label>
                                                                        <p className="text-sm">{formatDate(category.updatedAt)}</p>
                                                                </div>
                                                        )}

                                                        <Separator />

                                                        <div>
                                                                <Label className="text-sm font-medium text-gray-600">URL Preview</Label>
                                                                <p className="text-xs text-muted-foreground font-mono bg-gray-50 px-2 py-1 rounded">
                                                                        /news/category/{category.slug}
                                                                </p>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>

                                {/* Statistics */}
                                <Card className="md:col-span-2">
                                        <CardHeader>
                                                <CardTitle className="flex items-center gap-2">
                                                        <FileText className="h-5 w-5" />
                                                        Usage Statistics
                                                </CardTitle>
                                                <CardDescription>Articles and usage information for this category</CardDescription>
                                        </CardHeader>
                                        <CardContent>
                                                <div className="grid gap-4 md:grid-cols-3">
                                                        <div className="text-center p-4 border rounded-lg">
                                                                <div className="text-2xl font-bold text-blue-600">
                                                                        {category.articleCount || 0}
                                                                </div>
                                                                <p className="text-sm text-muted-foreground">Articles</p>
                                                        </div>
                                                        <div className="text-center p-4 border rounded-lg">
                                                                <div className="text-2xl font-bold text-green-600">
                                                                        {category.isActive ? 'Yes' : 'No'}
                                                                </div>
                                                                <p className="text-sm text-muted-foreground">Available for New Articles</p>
                                                        </div>
                                                        <div className="text-center p-4 border rounded-lg">
                                                                <div className="text-2xl font-bold text-purple-600">
                                                                        {category.slug.length}
                                                                </div>
                                                                <p className="text-sm text-muted-foreground">Slug Characters</p>
                                                        </div>
                                                </div>
                                        </CardContent>
                                </Card>
                        </div>

                        {/* Delete Confirmation Modal */}
                        <ConfirmModal
                                isOpen={showDeleteModal}
                                onClose={() => setShowDeleteModal(false)}
                                onConfirm={handleDelete}
                                title="Delete Category"
                                message={`Are you sure you want to delete "${category.name}"? This action cannot be undone.`}
                                confirmText="Delete"
                                variant="destructive"
                                loading={isDeleting}
                        />
                </div>
        );
}

function Label({ children, className }: { children: React.ReactNode; className?: string }) {
        return <label className={className}>{children}</label>;
}