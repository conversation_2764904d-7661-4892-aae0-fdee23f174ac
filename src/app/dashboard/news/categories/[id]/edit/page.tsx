'use client';

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft, Save, Tag, FileText, Eye, Palette, Hash, Globe, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useCategory, useUpdateCategory } from '@/lib/hooks/useCategories';

const updateCategorySchema = z.object({
        name: z.string().min(1, 'Category name is required').max(100, 'Name must be less than 100 characters'),
        slug: z.string().min(1, 'Slug is required').max(100, 'Slug must be less than 100 characters'),
        description: z.string().optional(),
        icon: z.string().optional(),
        color: z.string().optional(),
        sortOrder: z.number().min(0, 'Sort order must be 0 or greater').optional(),
        isActive: z.boolean(),
        isPublic: z.boolean(),
        metaTitle: z.string().max(200, 'Meta title must be less than 200 characters').optional(),
        metaDescription: z.string().max(500, 'Meta description must be less than 500 characters').optional(),
});

type UpdateCategoryFormData = z.infer<typeof updateCategorySchema>;

export default function EditCategoryPage() {
        const params = useParams();
        const router = useRouter();
        const { toast } = useToast();
        const categoryId = Number(params.id as string);

        const { data: category, isLoading, error } = useCategory(categoryId);
        const { mutate: updateCategory, isLoading: isUpdating } = useUpdateCategory();

        const [isFormDirty, setIsFormDirty] = useState(false);

        const form = useForm<UpdateCategoryFormData>({
                resolver: zodResolver(updateCategorySchema),
                defaultValues: {
                        name: '',
                        slug: '',
                        description: '',
                        icon: '',
                        color: '',
                        sortOrder: 0,
                        isActive: true,
                        isPublic: true,
                        metaTitle: '',
                        metaDescription: '',
                },
        });

        // Update form when category data is loaded
        useEffect(() => {
                if (category) {
                        form.reset({
                                name: category.name || '',
                                slug: category.slug || '',
                                description: category.description || '',
                                icon: category.icon || '',
                                color: category.color || '',
                                sortOrder: category.sortOrder || 0,
                                isActive: category.isActive,
                                isPublic: category.isPublic,
                                metaTitle: category.metaTitle || '',
                                metaDescription: category.metaDescription || '',
                        });
                }
        }, [category, form]);

        // Track form changes
        useEffect(() => {
                const subscription = form.watch(() => {
                        setIsFormDirty(true);
                });
                return () => subscription.unsubscribe();
        }, [form]);

        // Auto-generate slug from name
        const watchedName = form.watch('name');

        const generateSlug = (name: string) => {
                return name
                        .toLowerCase()
                        .trim()
                        .replace(/[^\w\s-]/g, '') // Remove special characters
                        .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
                        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
        };

        const onSubmit = (data: UpdateCategoryFormData) => {
                // Remove slug from update data as backend doesn't allow slug updates
                const { slug, ...updateData } = data;

                updateCategory(
                        {
                                id: categoryId,
                                data: updateData,
                        },
                        {
                                onSuccess: () => {
                                        toast({
                                                title: 'Category updated',
                                                description: 'News category has been successfully updated.',
                                        });
                                        setIsFormDirty(false);
                                        router.push(`/dashboard/news/categories/${categoryId}`);
                                },
                                onError: (error: any) => {
                                        toast({
                                                title: 'Error',
                                                description: error?.message || 'Failed to update category.',
                                                variant: 'destructive',
                                        });
                                },
                        }
                );
        };

        const handleCancel = () => {
                if (isFormDirty) {
                        const confirmed = window.confirm('You have unsaved changes. Are you sure you want to leave?');
                        if (!confirmed) return;
                }
                router.push(`/dashboard/news/categories/${categoryId}`);
        };

        if (isLoading) {
                return (
                        <div className="container mx-auto py-6">
                                <div className="flex items-center gap-4 mb-6">
                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
                                                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse mt-2" />
                                        </div>
                                </div>
                                <div className="grid gap-6 md:grid-cols-2">
                                        <Card>
                                                <CardHeader>
                                                        <div className="h-6 w-32 bg-gray-200 rounded animate-pulse" />
                                                        <div className="h-4 w-48 bg-gray-200 rounded animate-pulse" />
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                        <div className="h-4 w-full bg-gray-200 rounded animate-pulse" />
                                                        <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                                                        <div className="h-4 w-1/2 bg-gray-200 rounded animate-pulse" />
                                                </CardContent>
                                        </Card>
                                </div>
                        </div>
                );
        }

        if (error || !category) {
                return (
                        <div className="container mx-auto py-6">
                                <div className="flex items-center gap-4 mb-6">
                                        <Button variant="ghost" size="sm" onClick={handleCancel} className="h-8 w-8 p-0">
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <h1 className="text-2xl font-bold">Category Not Found</h1>
                                                <p className="text-muted-foreground">The requested category could not be found.</p>
                                        </div>
                                </div>
                                <Card>
                                        <CardContent className="py-8">
                                                <div className="text-center">
                                                        <Tag className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                                                        <h3 className="text-lg font-medium mb-2">Category Not Found</h3>
                                                        <p className="text-muted-foreground mb-4">
                                                                The category you're looking for doesn't exist or may have been deleted.
                                                        </p>
                                                        <Button onClick={() => router.push('/dashboard/news/categories')}>
                                                                Go Back to Categories
                                                        </Button>
                                                </div>
                                        </CardContent>
                                </Card>
                        </div>
                );
        }

        return (
                <div className="container mx-auto py-6 space-y-6">
                        {/* Header */}
                        <div className="flex items-center justify-between">
                                <div className="flex items-center gap-4">
                                        <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={handleCancel}
                                                className="h-8 w-8 p-0"
                                        >
                                                <ArrowLeft className="h-4 w-4" />
                                        </Button>
                                        <div>
                                                <h1 className="text-2xl font-bold">Edit Category</h1>
                                                <p className="text-muted-foreground">
                                                        Update details for "{category.name}"
                                                </p>
                                        </div>
                                </div>
                        </div>

                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <div className="grid gap-6 md:grid-cols-2">
                                        {/* Main Information */}
                                        <Card>
                                                <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                                <Tag className="h-5 w-5" />
                                                                Basic Information
                                                        </CardTitle>
                                                        <CardDescription>
                                                                Update the basic details for the news category
                                                        </CardDescription>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                        {/* Category Name */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="name">Category Name *</Label>
                                                                <Input
                                                                        id="name"
                                                                        placeholder="e.g., Sports News, Technology, Politics"
                                                                        {...form.register('name')}
                                                                        className={form.formState.errors.name ? 'border-red-500' : ''}
                                                                />
                                                                {form.formState.errors.name && (
                                                                        <p className="text-sm text-red-500">
                                                                                {form.formState.errors.name.message}
                                                                        </p>
                                                                )}
                                                        </div>

                                                        {/* Slug */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="slug">URL Slug (Read-only)</Label>
                                                                <Input
                                                                        id="slug"
                                                                        placeholder="e.g., sports-news, technology, politics"
                                                                        {...form.register('slug')}
                                                                        className="bg-gray-50 text-gray-600"
                                                                        readOnly
                                                                        disabled
                                                                />
                                                                <p className="text-xs text-muted-foreground">
                                                                        URL slug cannot be changed after creation to maintain link integrity
                                                                </p>
                                                        </div>

                                                        {/* Description */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="description">Description</Label>
                                                                <Textarea
                                                                        id="description"
                                                                        placeholder="Brief description of this category..."
                                                                        rows={4}
                                                                        {...form.register('description')}
                                                                />
                                                                <p className="text-xs text-muted-foreground">
                                                                        Optional description to help users understand this category
                                                                </p>
                                                        </div>

                                                        {/* Icon */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="icon">Icon</Label>
                                                                <Input
                                                                        id="icon"
                                                                        placeholder="e.g., sports, news, tech"
                                                                        {...form.register('icon')}
                                                                />
                                                                <p className="text-xs text-muted-foreground">
                                                                        Icon identifier for this category
                                                                </p>
                                                        </div>

                                                        {/* Color */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="color">Color</Label>
                                                                <div className="flex gap-2">
                                                                        <Input
                                                                                id="color"
                                                                                type="color"
                                                                                className="w-16 h-10 p-1 border rounded"
                                                                                {...form.register('color')}
                                                                        />
                                                                        <Input
                                                                                placeholder="#FF0000"
                                                                                className="flex-1"
                                                                                {...form.register('color')}
                                                                        />
                                                                </div>
                                                                <p className="text-xs text-muted-foreground">
                                                                        Theme color for this category
                                                                </p>
                                                        </div>

                                                        {/* Sort Order */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="sortOrder">Sort Order</Label>
                                                                <Input
                                                                        id="sortOrder"
                                                                        type="number"
                                                                        min="0"
                                                                        placeholder="0"
                                                                        {...form.register('sortOrder', { valueAsNumber: true })}
                                                                        className={form.formState.errors.sortOrder ? 'border-red-500' : ''}
                                                                />
                                                                {form.formState.errors.sortOrder && (
                                                                        <p className="text-sm text-red-500">
                                                                                {form.formState.errors.sortOrder.message}
                                                                        </p>
                                                                )}
                                                                <p className="text-xs text-muted-foreground">
                                                                        Display order (lower numbers appear first)
                                                                </p>
                                                        </div>
                                                </CardContent>
                                        </Card>

                                        {/* Settings */}
                                        <Card>
                                                <CardHeader>
                                                        <CardTitle className="flex items-center gap-2">
                                                                <Eye className="h-5 w-5" />
                                                                Category Settings
                                                        </CardTitle>
                                                        <CardDescription>
                                                                Configure visibility and status settings
                                                        </CardDescription>
                                                </CardHeader>
                                                <CardContent className="space-y-4">
                                                        {/* Active Status */}
                                                        <div className="flex items-center justify-between">
                                                                <div className="space-y-0.5">
                                                                        <Label htmlFor="isActive">Active Status</Label>
                                                                        <p className="text-sm text-muted-foreground">
                                                                                Enable this category for use in news articles
                                                                        </p>
                                                                </div>
                                                                <Switch
                                                                        id="isActive"
                                                                        checked={form.watch('isActive')}
                                                                        onCheckedChange={(checked) => form.setValue('isActive', checked)}
                                                                />
                                                        </div>

                                                        <Separator />

                                                        {/* Public Status */}
                                                        <div className="flex items-center justify-between">
                                                                <div className="space-y-0.5">
                                                                        <Label htmlFor="isPublic">Public Visibility</Label>
                                                                        <p className="text-sm text-muted-foreground">
                                                                                Make this category visible to public users
                                                                        </p>
                                                                </div>
                                                                <Switch
                                                                        id="isPublic"
                                                                        checked={form.watch('isPublic')}
                                                                        onCheckedChange={(checked) => form.setValue('isPublic', checked)}
                                                                />
                                                        </div>

                                                        <Separator />

                                                        {/* Meta Title */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="metaTitle">Meta Title (SEO)</Label>
                                                                <Input
                                                                        id="metaTitle"
                                                                        placeholder="SEO-optimized title for search engines"
                                                                        {...form.register('metaTitle')}
                                                                        className={form.formState.errors.metaTitle ? 'border-red-500' : ''}
                                                                />
                                                                {form.formState.errors.metaTitle && (
                                                                        <p className="text-sm text-red-500">
                                                                                {form.formState.errors.metaTitle.message}
                                                                        </p>
                                                                )}
                                                                <p className="text-xs text-muted-foreground">
                                                                        Title tag for search engines (max 200 characters)
                                                                </p>
                                                        </div>

                                                        {/* Meta Description */}
                                                        <div className="space-y-2">
                                                                <Label htmlFor="metaDescription">Meta Description (SEO)</Label>
                                                                <Textarea
                                                                        id="metaDescription"
                                                                        placeholder="Brief description for search engine results"
                                                                        rows={3}
                                                                        {...form.register('metaDescription')}
                                                                        className={form.formState.errors.metaDescription ? 'border-red-500' : ''}
                                                                />
                                                                {form.formState.errors.metaDescription && (
                                                                        <p className="text-sm text-red-500">
                                                                                {form.formState.errors.metaDescription.message}
                                                                        </p>
                                                                )}
                                                                <p className="text-xs text-muted-foreground">
                                                                        Description for search engine results (max 500 characters)
                                                                </p>
                                                        </div>

                                                        <Separator />

                                                        {/* Preview Information */}
                                                        <div className="space-y-3">
                                                                <h4 className="text-sm font-medium">Preview</h4>
                                                                <div className="rounded-lg border p-3 space-y-2">
                                                                        <div className="flex items-center gap-2">
                                                                                <div
                                                                                        className="w-4 h-4 rounded border"
                                                                                        style={{ backgroundColor: form.watch('color') || '#6B7280' }}
                                                                                />
                                                                                <Tag className="h-4 w-4" />
                                                                                <span className="font-medium">
                                                                                        {form.watch('name') || 'Category Name'}
                                                                                </span>
                                                                                {form.watch('icon') && (
                                                                                        <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                                                                                                {form.watch('icon')}
                                                                                        </span>
                                                                                )}
                                                                                {!form.watch('isActive') && (
                                                                                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                                                                                Inactive
                                                                                        </span>
                                                                                )}
                                                                                {!form.watch('isPublic') && (
                                                                                        <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded">
                                                                                                Private
                                                                                        </span>
                                                                                )}
                                                                        </div>
                                                                        {form.watch('slug') && (
                                                                                <p className="text-xs text-muted-foreground">
                                                                                        URL: /news/category/{form.watch('slug')}
                                                                                </p>
                                                                        )}
                                                                        {form.watch('description') && (
                                                                                <p className="text-sm text-muted-foreground">
                                                                                        {form.watch('description')}
                                                                                </p>
                                                                        )}
                                                                        {(form.watch('sortOrder') !== undefined && form.watch('sortOrder') !== 0) && (
                                                                                <p className="text-xs text-muted-foreground">
                                                                                        Sort Order: {form.watch('sortOrder')}
                                                                                </p>
                                                                        )}
                                                                        {form.watch('metaTitle') && (
                                                                                <div className="text-xs">
                                                                                        <span className="font-medium">SEO Title:</span> {form.watch('metaTitle')}
                                                                                </div>
                                                                        )}
                                                                </div>
                                                        </div>

                                                        {/* Status Indicator */}
                                                        {isFormDirty && (
                                                                <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                                                                        ⚠️ You have unsaved changes
                                                                </div>
                                                        )}
                                                </CardContent>
                                        </Card>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center justify-end gap-4">
                                        <Button
                                                type="button"
                                                variant="outline"
                                                onClick={handleCancel}
                                                disabled={isUpdating}
                                        >
                                                Cancel
                                        </Button>
                                        <Button
                                                type="submit"
                                                disabled={isUpdating || !form.formState.isValid}
                                                className="flex items-center gap-2"
                                        >
                                                <Save className="h-4 w-4" />
                                                {isUpdating ? 'Saving...' : 'Save Changes'}
                                        </Button>
                                </div>
                        </form>
                </div>
        );
}