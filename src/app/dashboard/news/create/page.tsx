'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { InputField, RadioField, FormSection, FormActions } from '@/components/ui/form-field';
import { ToggleSwitch } from '@/components/ui/toggle-switch';
import { useCreateNews } from '@/lib/hooks/useNews';
import { usePublicCategories } from '@/lib/hooks/useCategories';
import { CreateNewsData } from '@/lib/api/news';
import { ArrowLeft, Save, Newspaper } from 'lucide-react';
import { toast } from 'sonner';
import RichTextEditor from '@/components/ui/rich-text-editor';

interface NewsFormData {
  title: string;
  content: string;
  excerpt: string;
  featuredImage: string;
  tags: string;
  categoryId: string;
  status: 'draft' | 'published';
  isFeatured: boolean;
  publishDate: string;
  publishTime: string;
  metaTitle: string;
  metaDescription: string;
}

export default function CreateNewsPage() {
  const router = useRouter();
  const createNewsMutation = useCreateNews();
  const { data: categories = [], isLoading: isLoadingCategories } = usePublicCategories();

  const [formData, setFormData] = useState<NewsFormData>({
    title: '',
    content: '',
    excerpt: '',
    featuredImage: '',
    tags: '',
    categoryId: '',
    status: 'draft',
    isFeatured: false,
    publishDate: new Date().toISOString().split('T')[0],
    publishTime: new Date().toTimeString().slice(0, 5),
    metaTitle: '',
    metaDescription: '',
  });

  const [errors, setErrors] = useState<Partial<NewsFormData>>({});
  const validateForm = (): boolean => {
    const newErrors: Partial<NewsFormData> = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.content.trim()) newErrors.content = 'Content is required';
    if (!formData.categoryId) newErrors.categoryId = 'Category is required';
    if (!formData.publishDate) newErrors.publishDate = 'Publish date is required';
    if (!formData.publishTime) newErrors.publishTime = 'Publish time is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the form errors');
      return;
    }

    // Combine date and time
    const publishDateTime = new Date(`${formData.publishDate}T${formData.publishTime}`);

    // Parse tags
    const tagsArray = formData.tags
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0); const submitData: CreateNewsData = {
        title: formData.title,
        content: formData.content,
        categoryId: Number(formData.categoryId),
        excerpt: formData.excerpt || undefined,
        featuredImage: formData.featuredImage || undefined,
        tags: tagsArray.length > 0 ? tagsArray : undefined,
        status: formData.status,
        publishedAt: formData.status === 'published' ? publishDateTime.toISOString() : undefined,
        isFeatured: formData.isFeatured,
        metaTitle: formData.metaTitle || undefined,
        metaDescription: formData.metaDescription || undefined,
      };

    try {
      await createNewsMutation.mutateAsync(submitData);
      router.push('/dashboard/news');
    } catch (error) {
      // Error handled by mutation
    }
  };

  const updateFormData = (field: keyof NewsFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Newspaper className="mr-3 h-8 w-8 text-blue-600" />
            Create News Article
          </h1>
          <p className="text-gray-600 mt-1">Write and publish a new news article</p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle>News Article Details</CardTitle>
          <CardDescription>
            Fill in the information below to create a new news article
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <FormSection title="Basic Information" description="Essential article details">
              <div className="space-y-4">
                <InputField
                  label="Title *"
                  placeholder="Enter article title"
                  required
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  error={errors.title}
                  description="The main headline of your news article"
                />

                <InputField
                  label="Excerpt"
                  placeholder="Brief excerpt of the article"
                  value={formData.excerpt}
                  onChange={(e) => updateFormData('excerpt', e.target.value)}
                  error={errors.excerpt}
                  description="A short description that appears in previews and lists"
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Content *</label>
                  <RichTextEditor
                    value={formData.content}
                    onChange={(value) => updateFormData('content', value)}
                    placeholder="Write your article content here..."
                    error={errors.content}
                    minHeight={300}
                    className="w-full"
                  />
                  <p className="text-sm text-gray-500">
                    Write the full content of your news article. Use the toolbar above for formatting.
                  </p>
                </div>
              </div>
            </FormSection>            <FormSection title="Category & Media" description="Article categorization and media">
              <RadioField
                label="Category *"
                value={formData.categoryId}
                onValueChange={(value) => updateFormData('categoryId', value)}
                error={errors.categoryId}
                required
                options={categories.map((category: any) => ({
                  value: category.id.toString(),
                  label: category.name,
                  disabled: isLoadingCategories
                }))}
                orientation="vertical"
                description="Choose the appropriate category for this article"
              />

              <InputField
                label="Featured Image URL"
                placeholder="https://example.com/image.jpg"
                value={formData.featuredImage}
                onChange={(e) => updateFormData('featuredImage', e.target.value)}
                error={errors.featuredImage}
                description="Optional image to display with the article"
              />

              <InputField
                label="Tags"
                placeholder="sport, football, news, breaking"
                value={formData.tags}
                onChange={(e) => updateFormData('tags', e.target.value)}
                description="Comma-separated tags for categorization"
              />
            </FormSection>

            <FormSection title="Publishing Settings" description="Publication date and visibility options">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InputField
                  label="Publish Date *"
                  type="date"
                  required
                  value={formData.publishDate}
                  onChange={(e) => updateFormData('publishDate', e.target.value)}
                  error={errors.publishDate}
                />

                <InputField
                  label="Publish Time *"
                  type="time"
                  required
                  value={formData.publishTime}
                  onChange={(e) => updateFormData('publishTime', e.target.value)}
                  error={errors.publishTime}
                />
              </div>

              <div className="space-y-4">
                <ToggleSwitch
                  checked={formData.status === 'published'}
                  onCheckedChange={(checked) => updateFormData('status', checked ? 'published' : 'draft')}
                  label="Publish Article"
                  description="Make this article visible to the public"
                />

                <ToggleSwitch
                  checked={formData.isFeatured}
                  onCheckedChange={(checked) => updateFormData('isFeatured', checked)}
                  label="Featured Article"
                  description="Mark this as a featured/trending article"
                  variant="danger"
                />
              </div>
            </FormSection>

            <FormSection title="SEO Settings" description="Search engine optimization settings">
              <div className="space-y-4">
                <InputField
                  label="Meta Title"
                  placeholder="SEO-optimized title for search engines"
                  value={formData.metaTitle}
                  onChange={(e) => updateFormData('metaTitle', e.target.value)}
                  error={errors.metaTitle}
                  description="Title tag for search engines (max 200 characters)"
                />

                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Meta Description</label>
                  <textarea
                    placeholder="Brief description for search engine results"
                    value={formData.metaDescription}
                    onChange={(e) => updateFormData('metaDescription', e.target.value)}
                    className="min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                  />
                  {errors.metaDescription && (
                    <p className="text-sm text-red-600">{errors.metaDescription}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Description for search engine results (max 500 characters)
                  </p>
                </div>
              </div>
            </FormSection>

            <FormActions>              <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={createNewsMutation.isLoading}
            >
              Cancel
            </Button>
              <Button
                type="submit"
                disabled={createNewsMutation.isLoading}
              >
                <Save className="mr-2 h-4 w-4" />
                {createNewsMutation.isLoading ? 'Creating...' : 'Create Article'}
              </Button>
            </FormActions>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
