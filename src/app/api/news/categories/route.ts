import { NextResponse } from 'next/server';
import { NextRequest } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
        try {
                console.info('Fetching news categories from backend');

                const url = new URL(request.url);
                const params = url.searchParams.toString();

                // Check if this is an admin request based on authorization header
                const authHeader = request.headers.get('authorization');
                const endpoint = authHeader
                        ? `/admin/news/categories${params ? '?' + params : ''}`
                        : `/news/categories${params ? '?' + params : ''}`;

                const fullBackendUrl = `${API_BASE_URL}${endpoint}`;

                const response = await fetch(fullBackendUrl, {
                        method: 'GET',
                        headers: {
                                'Content-Type': 'application/json',
                                ...(authHeader && {
                                        'Authorization': authHeader
                                })
                        },
                });

                if (!response.ok) {
                        const errorText = await response.text();
                        console.error('Backend error:', errorText);
                        throw new Error(`Backend responded with status ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                return NextResponse.json(data);
        } catch (error) {
                console.error('Error fetching news categories:', error);
                return NextResponse.json({
                        message: 'Failed to fetch news categories',
                        error: error instanceof Error ? error.message : 'Unknown error'
                }, { status: 500 });
        }
}

export async function POST(request: NextRequest) {
        try {
                const body = await request.json();
                console.info('Creating new news category', body);

                const authHeader = request.headers.get('authorization');
                if (!authHeader) {
                        return NextResponse.json({
                                message: 'Authorization required for category creation'
                        }, { status: 401 });
                }

                const fullBackendUrl = `${API_BASE_URL}/admin/news/categories`;
                const response = await fetch(fullBackendUrl, {
                        method: 'POST',
                        headers: {
                                'Content-Type': 'application/json',
                                'Authorization': authHeader
                        },
                        body: JSON.stringify(body),
                });

                if (!response.ok) {
                        const errorText = await response.text();
                        console.error('Backend error:', errorText);
                        throw new Error(`Backend responded with status ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                return NextResponse.json(data, { status: response.status });
        } catch (error) {
                console.error('Error creating news category:', error);
                return NextResponse.json({
                        message: 'Failed to create news category',
                        error: error instanceof Error ? error.message : 'Unknown error'
                }, { status: 500 });
        }
}
