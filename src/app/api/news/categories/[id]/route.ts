import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
        request: NextRequest,
        { params }: { params: { id: string } }
) {
        try {
                const { id } = params;
                const authHeader = request.headers.get('authorization');

                console.log('🔄 Fetching category with ID:', id, 'Auth:', !!authHeader);

                // Admin endpoint uses ID, public endpoint might use slug
                // Try admin endpoint first if authenticated
                if (authHeader) {
                        const adminResponse = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {
                                method: 'GET',
                                headers: {
                                        'Content-Type': 'application/json',
                                        'Authorization': authHeader,
                                },
                        });

                        if (adminResponse.ok) {
                                const data = await adminResponse.json();
                                console.log('✅ Category fetched from admin endpoint');
                                return NextResponse.json(data);
                        }

                        console.log('❌ Admin endpoint failed:', adminResponse.status);
                }

                // For public endpoint, we need to find the slug first
                // Get all categories and find the one with matching ID
                const allCategoriesResponse = await fetch(`${API_BASE_URL}/news/categories`, {
                        method: 'GET',
                        headers: {
                                'Content-Type': 'application/json',
                        },
                });

                if (allCategoriesResponse.ok) {
                        const allCategories = await allCategoriesResponse.json();
                        const targetCategory = allCategories.find((cat: any) => cat.id.toString() === id);

                        if (targetCategory) {
                                console.log('✅ Category found in public list:', targetCategory.slug);
                                return NextResponse.json(targetCategory);
                        }
                }

                console.log('❌ Category not found in any endpoint');
                return NextResponse.json(
                        { error: 'Category not found' },
                        { status: 404 }
                );
        } catch (error) {
                console.error('Error fetching category:', error);
                return NextResponse.json(
                        { error: 'Internal server error' },
                        { status: 500 }
                );
        }
}

export async function PUT(
        request: NextRequest,
        { params }: { params: { id: string } }
) {
        try {
                const { id } = params;
                const authHeader = request.headers.get('authorization');

                if (!authHeader) {
                        return NextResponse.json(
                                { error: 'Authorization required for category update' },
                                { status: 401 }
                        );
                }

                const body = await request.json();

                const response = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {
                        method: 'PATCH',
                        headers: {
                                'Content-Type': 'application/json',
                                'Authorization': authHeader,
                        },
                        body: JSON.stringify(body),
                });

                if (!response.ok) {
                        return NextResponse.json(
                                { error: 'Failed to update category' },
                                { status: response.status }
                        );
                }

                const data = await response.json();
                return NextResponse.json(data);
        } catch (error) {
                console.error('Error updating category:', error);
                return NextResponse.json(
                        { error: 'Internal server error' },
                        { status: 500 }
                );
        }
}

export async function PATCH(
        request: NextRequest,
        { params }: { params: { id: string } }
) {
        try {
                const { id } = params;
                const authHeader = request.headers.get('authorization');

                if (!authHeader) {
                        return NextResponse.json(
                                { error: 'Authorization required for category patch' },
                                { status: 401 }
                        );
                }

                const body = await request.json();

                const response = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {
                        method: 'PATCH',
                        headers: {
                                'Content-Type': 'application/json',
                                'Authorization': authHeader,
                        },
                        body: JSON.stringify(body),
                });

                if (!response.ok) {
                        return NextResponse.json(
                                { error: 'Failed to patch category' },
                                { status: response.status }
                        );
                }

                const data = await response.json();
                return NextResponse.json(data);
        } catch (error) {
                console.error('Error patching category:', error);
                return NextResponse.json(
                        { error: 'Internal server error' },
                        { status: 500 }
                );
        }
}

export async function DELETE(
        request: NextRequest,
        { params }: { params: { id: string } }
) {
        try {
                const { id } = params;
                const authHeader = request.headers.get('authorization');

                if (!authHeader) {
                        return NextResponse.json(
                                { error: 'Authorization required for category deletion' },
                                { status: 401 }
                        );
                }

                const response = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {
                        method: 'DELETE',
                        headers: {
                                'Content-Type': 'application/json',
                                'Authorization': authHeader,
                        },
                });

                if (!response.ok) {
                        return NextResponse.json(
                                { error: 'Failed to delete category' },
                                { status: response.status }
                        );
                }

                const data = await response.json();
                return NextResponse.json(data);
        } catch (error) {
                console.error('Error deleting category:', error);
                return NextResponse.json(
                        { error: 'Internal server error' },
                        { status: 500 }
                );
        }
}
