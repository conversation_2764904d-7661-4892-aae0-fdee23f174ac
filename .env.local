# =============================================================================
# FECMS Sport - Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# NextJS Application Port Configuration
# -----------------------------------------------------------------------------
# This is the port where your NextJS application will run
PORT=4000

# -----------------------------------------------------------------------------
# Third-party API Configuration (External Backend Services)
# -----------------------------------------------------------------------------
# These URLs point to external API services (NOT NextJS ports)
# Backend API Server - Football data provider
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000

# -----------------------------------------------------------------------------
# CDN Configuration (Image/Static Assets Server)
# -----------------------------------------------------------------------------
# Server-side CDN URL (used in API routes for image proxy)
DOMAIN_CDN_PICTURE=http://*************

# Client-side CDN URL (used in React components for direct image loading)
NEXT_PUBLIC_DOMAIN_CDN_PICTURE=http://*************

# -----------------------------------------------------------------------------
# Environment Mode
# production/devloper
# -----------------------------------------------------------------------------
NODE_ENV=development

# -----------------------------------------------------------------------------
# Production Server Configuration
# -----------------------------------------------------------------------------
# Backend API endpoint (adjust for your production backend)
BACKEND_URL=http://localhost:3000
