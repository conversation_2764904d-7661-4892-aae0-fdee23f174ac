"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/news/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewsPage() {\n    var _filters_isFeatured, _newsData_meta;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_10__.usePermissions)();\n    // State for filtering\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for delete modal\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newsToDelete, setNewsToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Data fetching\n    const { data: newsData, isLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useNews)(filters);\n    // Mutations\n    const deleteNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useDeleteNews)();\n    const toggleStatusMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleNewsStatus)();\n    const toggleHotMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleHotStatus)();\n    // Event handlers\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setFilters((prev)=>({\n                ...prev,\n                search: searchQuery.trim() || undefined,\n                page: 1\n            }));\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value,\n                page: 1\n            }));\n    };\n    const handleViewNews = (news)=>{\n        router.push(\"/dashboard/news/\".concat(news.id));\n    };\n    const handleEditNews = (news)=>{\n        if (!isEditor() && !isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"You do not have permission to edit news\");\n            return;\n        }\n        router.push(\"/dashboard/news/\".concat(news.id, \"/edit\"));\n    };\n    const handleDeleteNews = (news)=>{\n        if (!isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"You do not have permission to delete news\");\n            return;\n        }\n        setNewsToDelete(news);\n        setDeleteModalOpen(true);\n    };\n    const confirmDelete = async ()=>{\n        if (newsToDelete) {\n            try {\n                await deleteNewsMutation.mutateAsync(newsToDelete.id);\n                setDeleteModalOpen(false);\n                setNewsToDelete(null);\n            } catch (error) {\n            // Error handled by mutation\n            }\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            key: \"title\",\n            title: \"Title\",\n            sortable: true,\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-gray-900 truncate\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        row.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 truncate\",\n                            children: row.summary\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"author\",\n            title: \"Author\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"isPublished\",\n            title: \"Status\",\n            sortable: true,\n            render: (value, row)=>{\n                if (!isEditor() && !isAdmin()) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: value ? \"default\" : \"secondary\",\n                        children: value ? \"Published\" : \"Draft\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_9__.ToggleSwitch, {\n                    checked: value || false,\n                    onCheckedChange: (checked)=>{\n                        toggleStatusMutation.mutate({\n                            id: row.id,\n                            isPublished: checked\n                        });\n                    },\n                    label: \"\",\n                    disabled: toggleStatusMutation.isLoading,\n                    size: \"sm\",\n                    variant: value ? \"success\" : \"warning\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"isHot\",\n            title: \"Hot\",\n            sortable: false,\n            render: (value, row)=>{\n                if (!isEditor() && !isAdmin()) {\n                    return value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: \"destructive\",\n                        className: \"text-xs\",\n                        children: \"Hot\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this) : null;\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_9__.ToggleSwitch, {\n                    checked: value || false,\n                    onCheckedChange: (checked)=>{\n                        toggleHotMutation.mutate({\n                            id: row.id,\n                            isHot: checked\n                        });\n                    },\n                    label: \"\",\n                    disabled: toggleHotMutation.isLoading,\n                    size: \"sm\",\n                    variant: \"danger\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"publishDate\",\n            title: \"Publish Date\",\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, this),\n                        new Date(value).toLocaleDateString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewNews(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit News\",\n                            onClick: ()=>handleEditNews(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete News\",\n                            onClick: ()=>handleDeleteNews(row),\n                            className: \"text-red-600 hover:text-red-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 208,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"News Management\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            \"            \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-red-600 mb-2\",\n                                        children: \"Failed to load news\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: error === null || error === void 0 ? void 0 : error.message\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>queryClient.invalidateQueries({\n                                                queryKey: [\n                                                    \"news\"\n                                                ]\n                                            }),\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 52\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 249,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n            lineNumber: 245,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"News Management\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-600\",\n                                children: \"Manage news articles and announcements\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 9\n                    }, this),\n                    isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        href: \"/dashboard/news/create\",\n                        passHref: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create News\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 267,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 13\n                                }, this),\n                                \"Filters & Search\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"Search by title, content, or author...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Search\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    filters.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: \"ghost\",\n                                        onClick: ()=>{\n                                            setSearchQuery(\"\");\n                                            handleFilterChange(\"search\", undefined);\n                                        },\n                                        children: \"Clear\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.status || \"all\",\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    handleFilterChange(\"status\", value === \"all\" ? undefined : value);\n                                                },\n                                                className: \"px-3 py-1 border rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"published\",\n                                                        children: \"Published\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"draft\",\n                                                        children: \"Draft\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"archived\",\n                                                        children: \"Archived\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Featured:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: ((_filters_isFeatured = filters.isFeatured) === null || _filters_isFeatured === void 0 ? void 0 : _filters_isFeatured.toString()) || \"all\",\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    handleFilterChange(\"isFeatured\", value === \"all\" ? undefined : value === \"true\");\n                                                },\n                                                className: \"px-3 py-1 border rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"true\",\n                                                        children: \"Featured Only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"false\",\n                                                        children: \"Regular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 288,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                        columns: columns,\n                        data: (newsData === null || newsData === void 0 ? void 0 : newsData.data) || [],\n                        loading: isLoading,\n                        pagination: {\n                            page: filters.page || 1,\n                            limit: filters.limit || 20,\n                            total: (newsData === null || newsData === void 0 ? void 0 : (_newsData_meta = newsData.meta) === null || _newsData_meta === void 0 ? void 0 : _newsData_meta.totalItems) || 0,\n                            onPageChange: (page)=>handleFilterChange(\"page\", page),\n                            onLimitChange: (limit)=>handleFilterChange(\"limit\", limit)\n                        },\n                        emptyMessage: \"No news found. Create your first news article!\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 366,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 365,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_8__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete News\",\n                message: 'Are you sure you want to delete \"'.concat(newsToDelete === null || newsToDelete === void 0 ? void 0 : newsToDelete.title, '\"? This action cannot be undone.'),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                loading: deleteNewsMutation.isLoading,\n                variant: \"destructive\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 383,\n                columnNumber: 46\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n        lineNumber: 265,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsPage, \"ImqZuSATBgnQ7mloaQR3EifG0u0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_10__.usePermissions,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useNews,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useDeleteNews,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleNewsStatus,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleHotStatus\n    ];\n});\n_c = NewsPage;\nvar _c;\n$RefreshReg$(_c, \"NewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/page.tsx\n"));

/***/ })

});