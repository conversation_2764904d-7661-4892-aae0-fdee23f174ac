"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/page",{

/***/ "(app-pages-browser)/./src/lib/api/news.ts":
/*!*****************************!*\
  !*** ./src/lib/api/news.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   newsApi: function() { return /* binding */ newsApi; }\n/* harmony export */ });\n// Helper function to get auth token\nconst getAuthToken = ()=>{\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                return ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken) || null;\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        return localStorage.getItem(\"accessToken\");\n    }\n    return null;\n};\nconst newsApi = {\n    getNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(key, v.toString()));\n                } else {\n                    params.append(key, value.toString());\n                }\n            }\n        });\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news?\".concat(params.toString()), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news\");\n        }\n        return await response.json();\n    },\n    // Get single news item (includes auth if available)\n    getNewsById: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news \".concat(id));\n        }\n        return await response.json();\n    },\n    // Editor+ access required\n    createNews: async (data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to create news\");\n        }\n        return await response.json();\n    },\n    // Editor+ access required\n    updateNews: async (id, data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"PATCH\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to update news \".concat(id));\n        }\n        return await response.json();\n    },\n    // Admin access required\n    deleteNews: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to delete news \".concat(id));\n        }\n    },\n    // Helper methods for common operations\n    getPublishedNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            isPublished: true\n        });\n    },\n    getHotNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            isHot: true\n        });\n    },\n    getNewsByAuthor: async function(author) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return newsApi.getNews({\n            ...filters,\n            author\n        });\n    },\n    toggleNewsStatus: async (id, isPublished)=>{\n        return newsApi.updateNews(id, {\n            isPublished\n        });\n    },\n    toggleHotStatus: async (id, isHot)=>{\n        return newsApi.updateNews(id, {\n            isHot\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/news.ts\n"));

/***/ })

});