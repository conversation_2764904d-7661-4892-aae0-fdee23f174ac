"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/page",{

/***/ "(app-pages-browser)/./src/lib/api/news.ts":
/*!*****************************!*\
  !*** ./src/lib/api/news.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   newsApi: function() { return /* binding */ newsApi; }\n/* harmony export */ });\n/* harmony import */ var _lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/news-transform */ \"(app-pages-browser)/./src/lib/utils/news-transform.ts\");\n\n// Helper function to get auth token\nconst getAuthToken = ()=>{\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                return ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken) || null;\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        return localStorage.getItem(\"accessToken\");\n    }\n    return null;\n};\nconst newsApi = {\n    getNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const transformedFilters = (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsFilters)(filters);\n        const params = new URLSearchParams();\n        Object.entries(transformedFilters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(key, v.toString()));\n                } else {\n                    params.append(key, value.toString());\n                }\n            }\n        });\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news?\".concat(params.toString()), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news\");\n        }\n        const data = await response.json();\n        // Transform the news data\n        if (data.data) {\n            data.data = data.data.map(_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsData);\n        }\n        return data;\n    },\n    // Get single news item (includes auth if available)\n    getNewsById: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news \".concat(id));\n        }\n        const data = await response.json();\n        return (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsData)(data);\n    },\n    // Editor+ access required\n    createNews: async (data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const transformedData = (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformCreateNewsData)(data);\n        const response = await fetch(\"/api/news\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(transformedData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to create news\");\n        }\n        const responseData = await response.json();\n        return (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsData)(responseData);\n    },\n    // Editor+ access required\n    updateNews: async (id, data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const transformedData = (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformUpdateNewsData)(data);\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"PATCH\",\n            headers,\n            body: JSON.stringify(transformedData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to update news \".concat(id));\n        }\n        const responseData = await response.json();\n        return (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsData)(responseData);\n    },\n    // Admin access required\n    deleteNews: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to delete news \".concat(id));\n        }\n    },\n    // Helper methods for common operations\n    getPublishedNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            status: \"published\"\n        });\n    },\n    getHotNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            isFeatured: true\n        });\n    },\n    getNewsByAuthor: async function(author) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return newsApi.getNews({\n            ...filters,\n            author\n        });\n    },\n    toggleNewsStatus: async (id, isPublished)=>{\n        return newsApi.updateNews(id, {\n            status: isPublished ? \"published\" : \"draft\"\n        });\n    },\n    toggleHotStatus: async (id, isHot)=>{\n        return newsApi.updateNews(id, {\n            isFeatured: isHot\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/news.ts\n"));

/***/ })

});