"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/dashboard/news/page.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/funnel.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Calendar,Edit,Eye,Filter,Newspaper,Plus,Search,Trash2,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewsPage() {\n    var _filters_isFeatured, _newsData_meta;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_10__.usePermissions)();\n    // State for filtering\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // State for delete modal\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newsToDelete, setNewsToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Data fetching\n    const { data: newsData, isLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useNews)(filters);\n    // Mutations\n    const deleteNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useDeleteNews)();\n    const toggleStatusMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleNewsStatus)();\n    const toggleHotMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleHotStatus)();\n    // Event handlers\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        setFilters((prev)=>({\n                ...prev,\n                search: searchQuery.trim() || undefined,\n                page: 1\n            }));\n    };\n    const handleFilterChange = (key, value)=>{\n        setFilters((prev)=>({\n                ...prev,\n                [key]: value,\n                page: 1\n            }));\n    };\n    const handleViewNews = (news)=>{\n        router.push(\"/dashboard/news/\".concat(news.id));\n    };\n    const handleEditNews = (news)=>{\n        if (!isEditor() && !isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"You do not have permission to edit news\");\n            return;\n        }\n        router.push(\"/dashboard/news/\".concat(news.id, \"/edit\"));\n    };\n    const handleDeleteNews = (news)=>{\n        if (!isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_12__.toast.error(\"You do not have permission to delete news\");\n            return;\n        }\n        setNewsToDelete(news);\n        setDeleteModalOpen(true);\n    };\n    const confirmDelete = async ()=>{\n        if (newsToDelete) {\n            try {\n                await deleteNewsMutation.mutateAsync(newsToDelete.id);\n                setDeleteModalOpen(false);\n                setNewsToDelete(null);\n            } catch (error) {\n            // Error handled by mutation\n            }\n        }\n    };\n    // Table columns\n    const columns = [\n        {\n            key: \"title\",\n            title: \"Title\",\n            sortable: true,\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-gray-900 truncate\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        row.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 truncate\",\n                            children: row.summary\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"author\",\n            title: \"Author\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-gray-600\",\n                            children: value\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            render: (value, row)=>{\n                const isPublished = value === \"published\";\n                if (!isEditor() && !isAdmin()) {\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: isPublished ? \"default\" : \"secondary\",\n                        children: value === \"published\" ? \"Published\" : value === \"draft\" ? \"Draft\" : \"Archived\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this);\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_9__.ToggleSwitch, {\n                    checked: isPublished,\n                    onCheckedChange: (checked)=>{\n                        toggleStatusMutation.mutate({\n                            id: row.id,\n                            isPublished: checked\n                        });\n                    },\n                    label: \"\",\n                    disabled: toggleStatusMutation.isLoading,\n                    size: \"sm\",\n                    variant: isPublished ? \"success\" : \"warning\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"isHot\",\n            title: \"Hot\",\n            sortable: false,\n            render: (value, row)=>{\n                if (!isEditor() && !isAdmin()) {\n                    return value ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                        variant: \"destructive\",\n                        className: \"text-xs\",\n                        children: \"Hot\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 13\n                    }, this) : null;\n                }\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_9__.ToggleSwitch, {\n                    checked: value || false,\n                    onCheckedChange: (checked)=>{\n                        toggleHotMutation.mutate({\n                            id: row.id,\n                            isHot: checked\n                        });\n                    },\n                    label: \"\",\n                    disabled: toggleHotMutation.isLoading,\n                    size: \"sm\",\n                    variant: \"danger\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"publishDate\",\n            title: \"Publish Date\",\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            className: \"h-4 w-4 mr-2 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this),\n                        new Date(value).toLocaleDateString()\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewNews(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit News\",\n                            onClick: ()=>handleEditNews(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete News\",\n                            onClick: ()=>handleDeleteNews(row),\n                            className: \"text-red-600 hover:text-red-700\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900\",\n                        children: \"News Management\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: [\n                            \"            \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-red-600 mb-2\",\n                                        children: \"Failed to load news\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: error === null || error === void 0 ? void 0 : error.message\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>queryClient.invalidateQueries({\n                                                queryKey: [\n                                                    \"news\"\n                                                ]\n                                            }),\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 52\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 251,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"News Management\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-gray-600\",\n                                children: \"Manage news articles and announcements\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, this),\n                    isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        href: \"/dashboard/news/create\",\n                        passHref: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            className: \"bg-blue-600 hover:bg-blue-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 15\n                                }, this),\n                                \"Create News\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 268,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"mr-2 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                \"Filters & Search\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"flex gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                            placeholder: \"Search by title, content, or author...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Calendar_Edit_Eye_Filter_Newspaper_Plus_Search_Trash2_User_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Search\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    filters.search && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"button\",\n                                        variant: \"ghost\",\n                                        onClick: ()=>{\n                                            setSearchQuery(\"\");\n                                            handleFilterChange(\"search\", undefined);\n                                        },\n                                        children: \"Clear\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Status:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.status || \"all\",\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    handleFilterChange(\"status\", value === \"all\" ? undefined : value);\n                                                },\n                                                className: \"px-3 py-1 border rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"published\",\n                                                        children: \"Published\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"draft\",\n                                                        children: \"Draft\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"archived\",\n                                                        children: \"Archived\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Featured:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: ((_filters_isFeatured = filters.isFeatured) === null || _filters_isFeatured === void 0 ? void 0 : _filters_isFeatured.toString()) || \"all\",\n                                                onChange: (e)=>{\n                                                    const value = e.target.value;\n                                                    handleFilterChange(\"isFeatured\", value === \"all\" ? undefined : value === \"true\");\n                                                },\n                                                className: \"px-3 py-1 border rounded-md text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"true\",\n                                                        children: \"Featured Only\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"false\",\n                                                        children: \"Regular\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 289,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_7__.DataTable, {\n                        columns: columns,\n                        data: (newsData === null || newsData === void 0 ? void 0 : newsData.data) || [],\n                        loading: isLoading,\n                        pagination: {\n                            page: filters.page || 1,\n                            limit: filters.limit || 20,\n                            total: (newsData === null || newsData === void 0 ? void 0 : (_newsData_meta = newsData.meta) === null || _newsData_meta === void 0 ? void 0 : _newsData_meta.totalItems) || 0,\n                            onPageChange: (page)=>handleFilterChange(\"page\", page),\n                            onLimitChange: (limit)=>handleFilterChange(\"limit\", limit)\n                        },\n                        emptyMessage: \"No news found. Create your first news article!\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 366,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_8__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete News\",\n                message: 'Are you sure you want to delete \"'.concat(newsToDelete === null || newsToDelete === void 0 ? void 0 : newsToDelete.title, '\"? This action cannot be undone.'),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                loading: deleteNewsMutation.isLoading,\n                variant: \"destructive\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n                lineNumber: 384,\n                columnNumber: 46\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/page.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsPage, \"ImqZuSATBgnQ7mloaQR3EifG0u0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_10__.usePermissions,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useNews,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useDeleteNews,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleNewsStatus,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_11__.useToggleHotStatus\n    ];\n});\n_c = NewsPage;\nvar _c;\n$RefreshReg$(_c, \"NewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/page.tsx\n"));

/***/ })

});