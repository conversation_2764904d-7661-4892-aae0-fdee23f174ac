"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/categories/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/api/categories.ts":
/*!***********************************!*\
  !*** ./src/lib/api/categories.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categoriesApi: function() { return /* binding */ categoriesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\n// Categories API class\nclass CategoriesApi {\n    // Get all categories with filters\n    async getCategories() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        try {\n            const params = new URLSearchParams();\n            if (filters.page) params.append(\"page\", filters.page.toString());\n            if (filters.limit) params.append(\"limit\", filters.limit.toString());\n            if (filters.search) params.append(\"search\", filters.search);\n            if (filters.isActive !== undefined) params.append(\"isActive\", filters.isActive.toString());\n            if (filters.isPublic !== undefined) params.append(\"isPublic\", filters.isPublic.toString());\n            if (filters.sortBy) params.append(\"sortBy\", filters.sortBy);\n            if (filters.sortOrder) params.append(\"sortOrder\", filters.sortOrder);\n            const queryString = params.toString();\n            const url = queryString ? \"\".concat(this.baseUrl, \"?\").concat(queryString) : this.baseUrl;\n            console.log(\"\\uD83D\\uDD04 Fetching categories from:\", url);\n            // Use apiClient to handle authentication properly\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(url);\n            console.log(\"✅ Categories fetched successfully:\", response);\n            // Check if response has pagination structure (admin endpoint) or is array (public endpoint)\n            if (response.data && Array.isArray(response.data)) {\n                // Admin endpoint format: { data: [...], meta: {...} }\n                return response;\n            } else if (Array.isArray(response)) {\n                // Public endpoint format: [...]\n                const data = response;\n                // Create pagination structure for frontend compatibility\n                const page = filters.page || 1;\n                const limit = filters.limit || 20;\n                const totalItems = data.length;\n                const totalPages = Math.ceil(totalItems / limit);\n                // Apply client-side pagination if needed\n                const startIndex = (page - 1) * limit;\n                const endIndex = startIndex + limit;\n                const paginatedData = data.slice(startIndex, endIndex);\n                return {\n                    data: paginatedData,\n                    meta: {\n                        currentPage: page,\n                        totalPages,\n                        totalItems,\n                        limit\n                    }\n                };\n            } else {\n                throw new Error(\"Unexpected response format\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error fetching categories:\", error);\n            throw error;\n        }\n    }\n    // Get category by ID\n    async getCategoryById(id) {\n        try {\n            console.log(\"\\uD83D\\uDD04 Fetching category by ID:\", id);\n            // Try with authentication first (for admin access)\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/\").concat(id));\n            console.log(\"✅ Category fetched successfully:\", response);\n            return response;\n        } catch (error) {\n            console.error(\"❌ Error fetching category by ID:\", error);\n            throw error;\n        }\n    }\n    // Get public categories only\n    async getPublicCategories() {\n        const response = await this.getCategories({\n            isPublic: true,\n            isActive: true\n        });\n        return response.data;\n    }\n    // Create new category\n    async createCategory(data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(this.baseUrl, data);\n    }\n    // Update category\n    async updateCategory(id, data) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"\".concat(this.baseUrl, \"/\").concat(id), data);\n    }\n    // Delete category\n    async deleteCategory(id) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.delete(\"\".concat(this.baseUrl, \"/\").concat(id));\n    }\n    // Toggle category status\n    async toggleCategoryStatus(id, isActive) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"\".concat(this.baseUrl, \"/\").concat(id), {\n            isActive\n        });\n    }\n    // Reorder categories\n    async reorderCategories(categoryIds) {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"\".concat(this.baseUrl, \"/reorder\"), {\n            categoryIds\n        });\n    }\n    // Get category statistics\n    async getCategoryStats() {\n        return _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"\".concat(this.baseUrl, \"/stats\"));\n    }\n    constructor(){\n        this.baseUrl = \"/api/news/categories\";\n    }\n}\n// Export singleton instance\nconst categoriesApi = new CategoriesApi();\n/* harmony default export */ __webpack_exports__[\"default\"] = (categoriesApi);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/categories.ts\n"));

/***/ })

});