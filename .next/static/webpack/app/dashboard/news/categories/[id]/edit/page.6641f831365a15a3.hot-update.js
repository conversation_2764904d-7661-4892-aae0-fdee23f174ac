"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/categories/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/categories/[id]/edit/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/dashboard/news/categories/[id]/edit/page.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditCategoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst updateCategorySchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Category name is required\").max(100, \"Name must be less than 100 characters\"),\n    slug: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Slug is required\").max(100, \"Slug must be less than 100 characters\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    icon: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    color: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().min(0, \"Sort order must be 0 or greater\").optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean(),\n    isPublic: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean(),\n    metaTitle: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().max(200, \"Meta title must be less than 200 characters\").optional(),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().max(500, \"Meta description must be less than 500 characters\").optional()\n});\nfunction EditCategoryPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const categoryId = Number(params.id);\n    const { data: category, isLoading, error } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategory)(categoryId);\n    const { mutate: updateCategory, isLoading: isUpdating } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useUpdateCategory)();\n    const [isFormDirty, setIsFormDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(updateCategorySchema),\n        defaultValues: {\n            name: \"\",\n            slug: \"\",\n            description: \"\",\n            icon: \"\",\n            color: \"\",\n            sortOrder: 0,\n            isActive: true,\n            isPublic: true,\n            metaTitle: \"\",\n            metaDescription: \"\"\n        }\n    });\n    // Update form when category data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (category) {\n            form.reset({\n                name: category.name || \"\",\n                slug: category.slug || \"\",\n                description: category.description || \"\",\n                icon: category.icon || \"\",\n                color: category.color || \"\",\n                sortOrder: category.sortOrder || 0,\n                isActive: category.isActive,\n                isPublic: category.isPublic,\n                metaTitle: category.metaTitle || \"\",\n                metaDescription: category.metaDescription || \"\"\n            });\n        }\n    }, [\n        category,\n        form\n    ]);\n    // Track form changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch(()=>{\n            setIsFormDirty(true);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form\n    ]);\n    // Auto-generate slug from name\n    const watchedName = form.watch(\"name\");\n    const generateSlug = (name)=>{\n        return name.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // Remove special characters\n        .replace(/[\\s_-]+/g, \"-\") // Replace spaces and underscores with hyphens\n        .replace(/^-+|-+$/g, \"\"); // Remove leading/trailing hyphens\n    };\n    const onSubmit = (data)=>{\n        updateCategory({\n            id: categoryId,\n            data\n        }, {\n            onSuccess: ()=>{\n                toast({\n                    title: \"Category updated\",\n                    description: \"News category has been successfully updated.\"\n                });\n                setIsFormDirty(false);\n                router.push(\"/dashboard/news/categories/\".concat(categoryId));\n            },\n            onError: (error)=>{\n                toast({\n                    title: \"Error\",\n                    description: (error === null || error === void 0 ? void 0 : error.message) || \"Failed to update category.\",\n                    variant: \"destructive\"\n                });\n            }\n        });\n    };\n    const handleCancel = ()=>{\n        if (isFormDirty) {\n            const confirmed = window.confirm(\"You have unsaved changes. Are you sure you want to leave?\");\n            if (!confirmed) return;\n        }\n        router.push(\"/dashboard/news/categories/\".concat(categoryId));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-48 bg-gray-200 rounded animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 33\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-32 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-48 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 57\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 49\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-full bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-3/4 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-1/2 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 57\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 49\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 41\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 33\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n            lineNumber: 135,\n            columnNumber: 25\n        }, this);\n    }\n    if (error || !category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"Category Not Found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"The requested category could not be found.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 33\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Category Not Found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-4\",\n                                    children: \"The category you're looking for doesn't exist or may have been deleted.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news/categories\"),\n                                    children: \"Go Back to Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 57\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 49\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 41\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 33\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n            lineNumber: 164,\n            columnNumber: 25\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"Edit Category\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        'Update details for \"',\n                                        category.name,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 33\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                lineNumber: 195,\n                columnNumber: 25\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: form.handleSubmit(onSubmit),\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \"Basic Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Update the basic details for the news category\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 49\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Category Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"name\",\n                                                        placeholder: \"e.g., Sports News, Technology, Politics\",\n                                                        ...form.register(\"name\"),\n                                                        className: form.formState.errors.name ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 73\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"slug\",\n                                                        children: \"URL Slug *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"slug\",\n                                                        placeholder: \"e.g., sports-news, technology, politics\",\n                                                        ...form.register(\"slug\"),\n                                                        className: form.formState.errors.slug ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.slug.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"URL-friendly version of the category name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"description\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"description\",\n                                                        placeholder: \"Brief description of this category...\",\n                                                        rows: 4,\n                                                        ...form.register(\"description\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Optional description to help users understand this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"icon\",\n                                                        children: \"Icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"icon\",\n                                                        placeholder: \"e.g., sports, news, tech\",\n                                                        ...form.register(\"icon\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Icon identifier for this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"color\",\n                                                        children: \"Color\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"color\",\n                                                                type: \"color\",\n                                                                className: \"w-16 h-10 p-1 border rounded\",\n                                                                ...form.register(\"color\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                placeholder: \"#FF0000\",\n                                                                className: \"flex-1\",\n                                                                ...form.register(\"color\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Theme color for this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"sortOrder\",\n                                                        children: \"Sort Order\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"sortOrder\",\n                                                        type: \"number\",\n                                                        min: \"0\",\n                                                        placeholder: \"0\",\n                                                        ...form.register(\"sortOrder\", {\n                                                            valueAsNumber: true\n                                                        }),\n                                                        className: form.formState.errors.sortOrder ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.sortOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.sortOrder.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Display order (lower numbers appear first)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 41\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \"Category Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Configure visibility and status settings\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 49\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-0.5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"isActive\",\n                                                                children: \"Active Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 349,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Enable this category for use in news articles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 350,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: form.watch(\"isActive\"),\n                                                        onCheckedChange: (checked)=>form.setValue(\"isActive\", checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-0.5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"isPublic\",\n                                                                children: \"Public Visibility\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Make this category visible to public users\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                        id: \"isPublic\",\n                                                        checked: form.watch(\"isPublic\"),\n                                                        onCheckedChange: (checked)=>form.setValue(\"isPublic\", checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"metaTitle\",\n                                                        children: \"Meta Title (SEO)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"metaTitle\",\n                                                        placeholder: \"SEO-optimized title for search engines\",\n                                                        ...form.register(\"metaTitle\"),\n                                                        className: form.formState.errors.metaTitle ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.metaTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.metaTitle.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Title tag for search engines (max 200 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"metaDescription\",\n                                                        children: \"Meta Description (SEO)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"metaDescription\",\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        rows: 3,\n                                                        ...form.register(\"metaDescription\"),\n                                                        className: form.formState.errors.metaDescription ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.metaDescription.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg border p-3 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: form.watch(\"name\") || \"Category Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    !form.watch(\"isActive\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\",\n                                                                        children: \"Inactive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 431,\n                                                                        columnNumber: 89\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            form.watch(\"slug\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"URL: /news/category/\",\n                                                                    form.watch(\"slug\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 81\n                                                            }, this),\n                                                            form.watch(\"description\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: form.watch(\"description\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 442,\n                                                                columnNumber: 81\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 57\n                                            }, this),\n                                            isFormDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200\",\n                                                children: \"⚠️ You have unsaved changes\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 65\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 345,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 41\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 33\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: handleCancel,\n                                disabled: isUpdating,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 461,\n                                columnNumber: 41\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"submit\",\n                                disabled: isUpdating || !form.formState.isValid,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 49\n                                    }, this),\n                                    isUpdating ? \"Saving...\" : \"Save Changes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 41\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 33\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                lineNumber: 214,\n                columnNumber: 25\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n        lineNumber: 193,\n        columnNumber: 17\n    }, this);\n}\n_s(EditCategoryPage, \"7YkpasoFUQf7zlhT8XUlStyh2hY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategory,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useUpdateCategory,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm\n    ];\n});\n_c = EditCategoryPage;\nvar _c;\n$RefreshReg$(_c, \"EditCategoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/categories/[id]/edit/page.tsx\n"));

/***/ })

});