"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/categories/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/categories/[id]/edit/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/dashboard/news/categories/[id]/edit/page.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditCategoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst updateCategorySchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Category name is required\").max(100, \"Name must be less than 100 characters\"),\n    slug: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Slug is required\").max(100, \"Slug must be less than 100 characters\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    icon: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    color: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().min(0, \"Sort order must be 0 or greater\").optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean(),\n    isPublic: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean(),\n    metaTitle: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().max(200, \"Meta title must be less than 200 characters\").optional(),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().max(500, \"Meta description must be less than 500 characters\").optional()\n});\nfunction EditCategoryPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const categoryId = Number(params.id);\n    const { data: category, isLoading, error } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategory)(categoryId);\n    const { mutate: updateCategory, isLoading: isUpdating } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useUpdateCategory)();\n    const [isFormDirty, setIsFormDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(updateCategorySchema),\n        defaultValues: {\n            name: \"\",\n            slug: \"\",\n            description: \"\",\n            isActive: true\n        }\n    });\n    // Update form when category data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (category) {\n            form.reset({\n                name: category.name || \"\",\n                slug: category.slug || \"\",\n                description: category.description || \"\",\n                isActive: category.isActive\n            });\n        }\n    }, [\n        category,\n        form\n    ]);\n    // Track form changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch(()=>{\n            setIsFormDirty(true);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form\n    ]);\n    // Auto-generate slug from name\n    const watchedName = form.watch(\"name\");\n    const generateSlug = (name)=>{\n        return name.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // Remove special characters\n        .replace(/[\\s_-]+/g, \"-\") // Replace spaces and underscores with hyphens\n        .replace(/^-+|-+$/g, \"\"); // Remove leading/trailing hyphens\n    };\n    const onSubmit = (data)=>{\n        updateCategory({\n            id: categoryId,\n            data\n        }, {\n            onSuccess: ()=>{\n                toast({\n                    title: \"Category updated\",\n                    description: \"News category has been successfully updated.\"\n                });\n                setIsFormDirty(false);\n                router.push(\"/dashboard/news/categories/\".concat(categoryId));\n            },\n            onError: (error)=>{\n                toast({\n                    title: \"Error\",\n                    description: (error === null || error === void 0 ? void 0 : error.message) || \"Failed to update category.\",\n                    variant: \"destructive\"\n                });\n            }\n        });\n    };\n    const handleCancel = ()=>{\n        if (isFormDirty) {\n            const confirmed = window.confirm(\"You have unsaved changes. Are you sure you want to leave?\");\n            if (!confirmed) return;\n        }\n        router.push(\"/dashboard/news/categories/\".concat(categoryId));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-48 bg-gray-200 rounded animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 33\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-32 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-48 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 57\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 49\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-full bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-3/4 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-1/2 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 57\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 49\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 41\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 33\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n            lineNumber: 123,\n            columnNumber: 25\n        }, this);\n    }\n    if (error || !category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"Category Not Found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"The requested category could not be found.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 33\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Category Not Found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-4\",\n                                    children: \"The category you're looking for doesn't exist or may have been deleted.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news/categories\"),\n                                    children: \"Go Back to Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 57\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 49\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 41\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 33\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n            lineNumber: 152,\n            columnNumber: 25\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"Edit Category\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        'Update details for \"',\n                                        category.name,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 33\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                lineNumber: 183,\n                columnNumber: 25\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: form.handleSubmit(onSubmit),\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \"Basic Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Update the basic details for the news category\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 49\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Category Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"name\",\n                                                        placeholder: \"e.g., Sports News, Technology, Politics\",\n                                                        ...form.register(\"name\"),\n                                                        className: form.formState.errors.name ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 73\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"slug\",\n                                                        children: \"URL Slug *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"slug\",\n                                                        placeholder: \"e.g., sports-news, technology, politics\",\n                                                        ...form.register(\"slug\"),\n                                                        className: form.formState.errors.slug ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.slug.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"URL-friendly version of the category name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"description\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"description\",\n                                                        placeholder: \"Brief description of this category...\",\n                                                        rows: 4,\n                                                        ...form.register(\"description\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Optional description to help users understand this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 41\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \"Category Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Configure visibility and status settings\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 49\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-0.5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"isActive\",\n                                                                children: \"Active Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Enable this category for use in news articles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: form.watch(\"isActive\"),\n                                                        onCheckedChange: (checked)=>form.setValue(\"isActive\", checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg border p-3 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: form.watch(\"name\") || \"Category Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 302,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    !form.watch(\"isActive\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\",\n                                                                        children: \"Inactive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 89\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            form.watch(\"slug\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"URL: /news/category/\",\n                                                                    form.watch(\"slug\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 81\n                                                            }, this),\n                                                            form.watch(\"description\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: form.watch(\"description\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 81\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 299,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 57\n                                            }, this),\n                                            isFormDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200\",\n                                                children: \"⚠️ You have unsaved changes\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 65\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 41\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 33\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: handleCancel,\n                                disabled: isUpdating,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 41\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"submit\",\n                                disabled: isUpdating || !form.formState.isValid,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 49\n                                    }, this),\n                                    isUpdating ? \"Saving...\" : \"Save Changes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 41\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 33\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                lineNumber: 202,\n                columnNumber: 25\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n        lineNumber: 181,\n        columnNumber: 17\n    }, this);\n}\n_s(EditCategoryPage, \"7YkpasoFUQf7zlhT8XUlStyh2hY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategory,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useUpdateCategory,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm\n    ];\n});\n_c = EditCategoryPage;\nvar _c;\n$RefreshReg$(_c, \"EditCategoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/categories/[id]/edit/page.tsx\n"));

/***/ })

});