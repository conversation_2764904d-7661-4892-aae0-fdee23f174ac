"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/categories/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/categories/[id]/edit/page.tsx":
/*!**************************************************************!*\
  !*** ./src/app/dashboard/news/categories/[id]/edit/page.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditCategoryPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,Save,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst updateCategorySchema = zod__WEBPACK_IMPORTED_MODULE_4__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Category name is required\").max(100, \"Name must be less than 100 characters\"),\n    slug: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().min(1, \"Slug is required\").max(100, \"Slug must be less than 100 characters\"),\n    description: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    icon: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    color: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().optional(),\n    sortOrder: zod__WEBPACK_IMPORTED_MODULE_4__.z.number().min(0, \"Sort order must be 0 or greater\").optional(),\n    isActive: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean(),\n    isPublic: zod__WEBPACK_IMPORTED_MODULE_4__.z.boolean(),\n    metaTitle: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().max(200, \"Meta title must be less than 200 characters\").optional(),\n    metaDescription: zod__WEBPACK_IMPORTED_MODULE_4__.z.string().max(500, \"Meta description must be less than 500 characters\").optional()\n});\nfunction EditCategoryPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const categoryId = Number(params.id);\n    const { data: category, isLoading, error } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategory)(categoryId);\n    const { mutate: updateCategory, isLoading: isUpdating } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useUpdateCategory)();\n    const [isFormDirty, setIsFormDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(updateCategorySchema),\n        defaultValues: {\n            name: \"\",\n            slug: \"\",\n            description: \"\",\n            icon: \"\",\n            color: \"\",\n            sortOrder: 0,\n            isActive: true,\n            isPublic: true,\n            metaTitle: \"\",\n            metaDescription: \"\"\n        }\n    });\n    // Update form when category data is loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (category) {\n            form.reset({\n                name: category.name || \"\",\n                slug: category.slug || \"\",\n                description: category.description || \"\",\n                icon: category.icon || \"\",\n                color: category.color || \"\",\n                sortOrder: category.sortOrder || 0,\n                isActive: category.isActive,\n                isPublic: category.isPublic,\n                metaTitle: category.metaTitle || \"\",\n                metaDescription: category.metaDescription || \"\"\n            });\n        }\n    }, [\n        category,\n        form\n    ]);\n    // Track form changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const subscription = form.watch(()=>{\n            setIsFormDirty(true);\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        form\n    ]);\n    // Auto-generate slug from name\n    const watchedName = form.watch(\"name\");\n    const generateSlug = (name)=>{\n        return name.toLowerCase().trim().replace(/[^\\w\\s-]/g, \"\") // Remove special characters\n        .replace(/[\\s_-]+/g, \"-\") // Replace spaces and underscores with hyphens\n        .replace(/^-+|-+$/g, \"\"); // Remove leading/trailing hyphens\n    };\n    const onSubmit = (data)=>{\n        // Remove slug from update data as backend doesn't allow slug updates\n        const { slug, ...updateData } = data;\n        updateCategory({\n            id: categoryId,\n            data: updateData\n        }, {\n            onSuccess: ()=>{\n                toast({\n                    title: \"Category updated\",\n                    description: \"News category has been successfully updated.\"\n                });\n                setIsFormDirty(false);\n                router.push(\"/dashboard/news/categories/\".concat(categoryId));\n            },\n            onError: (error)=>{\n                toast({\n                    title: \"Error\",\n                    description: (error === null || error === void 0 ? void 0 : error.message) || \"Failed to update category.\",\n                    variant: \"destructive\"\n                });\n            }\n        });\n    };\n    const handleCancel = ()=>{\n        if (isFormDirty) {\n            const confirmed = window.confirm(\"You have unsaved changes. Are you sure you want to leave?\");\n            if (!confirmed) return;\n        }\n        router.push(\"/dashboard/news/categories/\".concat(categoryId));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 w-48 bg-gray-200 rounded animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-32 bg-gray-200 rounded animate-pulse mt-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 33\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-6 w-32 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-48 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 57\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 49\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-full bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-3/4 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 57\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 w-1/2 bg-gray-200 rounded animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 57\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 49\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 41\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 33\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n            lineNumber: 138,\n            columnNumber: 25\n        }, this);\n    }\n    if (error || !category) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"Category Not Found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"The requested category could not be found.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 33\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"h-16 w-16 mx-auto text-gray-400 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Category Not Found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground mb-4\",\n                                    children: \"The category you're looking for doesn't exist or may have been deleted.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 57\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news/categories\"),\n                                    children: \"Go Back to Categories\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 57\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 49\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 41\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 33\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n            lineNumber: 167,\n            columnNumber: 25\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"h-8 w-8 p-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 49\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 41\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: \"Edit Category\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 49\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: [\n                                        'Update details for \"',\n                                        category.name,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 49\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 41\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 33\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                lineNumber: 198,\n                columnNumber: 25\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: form.handleSubmit(onSubmit),\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid gap-6 md:grid-cols-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \"Basic Information\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Update the basic details for the news category\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 49\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Category Name *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"name\",\n                                                        placeholder: \"e.g., Sports News, Technology, Politics\",\n                                                        ...form.register(\"name\"),\n                                                        className: form.formState.errors.name ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.name.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 73\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"slug\",\n                                                        children: \"URL Slug *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"slug\",\n                                                        placeholder: \"e.g., sports-news, technology, politics\",\n                                                        ...form.register(\"slug\"),\n                                                        className: form.formState.errors.slug ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.slug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.slug.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"URL-friendly version of the category name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"description\",\n                                                        children: \"Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"description\",\n                                                        placeholder: \"Brief description of this category...\",\n                                                        rows: 4,\n                                                        ...form.register(\"description\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Optional description to help users understand this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"icon\",\n                                                        children: \"Icon\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"icon\",\n                                                        placeholder: \"e.g., sports, news, tech\",\n                                                        ...form.register(\"icon\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Icon identifier for this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"color\",\n                                                        children: \"Color\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                id: \"color\",\n                                                                type: \"color\",\n                                                                className: \"w-16 h-10 p-1 border rounded\",\n                                                                ...form.register(\"color\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                placeholder: \"#FF0000\",\n                                                                className: \"flex-1\",\n                                                                ...form.register(\"color\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 303,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Theme color for this category\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"sortOrder\",\n                                                        children: \"Sort Order\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"sortOrder\",\n                                                        type: \"number\",\n                                                        min: \"0\",\n                                                        placeholder: \"0\",\n                                                        ...form.register(\"sortOrder\", {\n                                                            valueAsNumber: true\n                                                        }),\n                                                        className: form.formState.errors.sortOrder ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.sortOrder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.sortOrder.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Display order (lower numbers appear first)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 41\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    \"Category Settings\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                                children: \"Configure visibility and status settings\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 57\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 49\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-0.5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"isActive\",\n                                                                children: \"Active Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 352,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Enable this category for use in news articles\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                        id: \"isActive\",\n                                                        checked: form.watch(\"isActive\"),\n                                                        onCheckedChange: (checked)=>form.setValue(\"isActive\", checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-0.5\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                htmlFor: \"isPublic\",\n                                                                children: \"Public Visibility\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Make this category visible to public users\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 73\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_10__.Switch, {\n                                                        id: \"isPublic\",\n                                                        checked: form.watch(\"isPublic\"),\n                                                        onCheckedChange: (checked)=>form.setValue(\"isPublic\", checked)\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"metaTitle\",\n                                                        children: \"Meta Title (SEO)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"metaTitle\",\n                                                        placeholder: \"SEO-optimized title for search engines\",\n                                                        ...form.register(\"metaTitle\"),\n                                                        className: form.formState.errors.metaTitle ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.metaTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.metaTitle.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Title tag for search engines (max 200 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"metaDescription\",\n                                                        children: \"Meta Description (SEO)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"metaDescription\",\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        rows: 3,\n                                                        ...form.register(\"metaDescription\"),\n                                                        className: form.formState.errors.metaDescription ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    form.formState.errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-500\",\n                                                        children: form.formState.errors.metaDescription.message\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 73\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_11__.Separator, {}, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 57\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Preview\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 426,\n                                                        columnNumber: 65\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"rounded-lg border p-3 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-4 h-4 rounded border\",\n                                                                        style: {\n                                                                            backgroundColor: form.watch(\"color\") || \"#6B7280\"\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 433,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: form.watch(\"name\") || \"Category Name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 434,\n                                                                        columnNumber: 81\n                                                                    }, this),\n                                                                    form.watch(\"icon\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded\",\n                                                                        children: form.watch(\"icon\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 89\n                                                                    }, this),\n                                                                    !form.watch(\"isActive\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\",\n                                                                        children: \"Inactive\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 443,\n                                                                        columnNumber: 89\n                                                                    }, this),\n                                                                    !form.watch(\"isPublic\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded\",\n                                                                        children: \"Private\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 89\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 73\n                                                            }, this),\n                                                            form.watch(\"slug\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"URL: /news/category/\",\n                                                                    form.watch(\"slug\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 81\n                                                            }, this),\n                                                            form.watch(\"description\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: form.watch(\"description\")\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 459,\n                                                                columnNumber: 81\n                                                            }, this),\n                                                            form.watch(\"sortOrder\") !== undefined && form.watch(\"sortOrder\") !== 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: [\n                                                                    \"Sort Order: \",\n                                                                    form.watch(\"sortOrder\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 464,\n                                                                columnNumber: 81\n                                                            }, this),\n                                                            form.watch(\"metaTitle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"SEO Title:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                        lineNumber: 470,\n                                                                        columnNumber: 89\n                                                                    }, this),\n                                                                    \" \",\n                                                                    form.watch(\"metaTitle\")\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 81\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 65\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 57\n                                            }, this),\n                                            isFormDirty && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200\",\n                                                children: \"⚠️ You have unsaved changes\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 65\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 41\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 33\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-end gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"button\",\n                                variant: \"outline\",\n                                onClick: handleCancel,\n                                disabled: isUpdating,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 41\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"submit\",\n                                disabled: isUpdating || !form.formState.isValid,\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_Save_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 49\n                                    }, this),\n                                    isUpdating ? \"Saving...\" : \"Save Changes\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 41\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 33\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n                lineNumber: 217,\n                columnNumber: 25\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/categories/[id]/edit/page.tsx\",\n        lineNumber: 196,\n        columnNumber: 17\n    }, this);\n}\n_s(EditCategoryPage, \"7YkpasoFUQf7zlhT8XUlStyh2hY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useCategory,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_13__.useUpdateCategory,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_14__.useForm\n    ];\n});\n_c = EditCategoryPage;\nvar _c;\n$RefreshReg$(_c, \"EditCategoryPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/categories/[id]/edit/page.tsx\n"));

/***/ })

});