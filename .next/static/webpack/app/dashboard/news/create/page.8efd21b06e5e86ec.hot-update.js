"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/create/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/create/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/news/create/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/rich-text-editor */ \"(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateNewsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const createNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_7__.useCreateNews)();\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_8__.usePublicCategories)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: new Date().toISOString().split(\"T\")[0],\n        publishTime: new Date().toTimeString().slice(0, 5),\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.categoryId) newErrors.categoryId = \"Category is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            categoryId: Number(formData.categoryId),\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await createNewsMutation.mutateAsync(submitData);\n            router.push(\"/dashboard/news\");\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Create News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Write and publish a new news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"News Article Details\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Fill in the information below to create a new news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Excerpt\",\n                                                placeholder: \"Brief excerpt of the article\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>updateFormData(\"excerpt\", e.target.value),\n                                                error: errors.excerpt,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        value: formData.content,\n                                                        onChange: (value)=>updateFormData(\"content\", value),\n                                                        placeholder: \"Write your article content here...\",\n                                                        error: errors.content,\n                                                        minHeight: 300,\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. Use the toolbar above for formatting.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Category & Media\",\n                                    description: \"Article categorization and media\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                label: \"Category *\",\n                                                value: formData.categoryId,\n                                                onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                                error: errors.categoryId,\n                                                disabled: isLoadingCategories,\n                                                placeholder: isLoadingCategories ? \"Loading categories...\" : \"Select a category\",\n                                                options: categories.map((category)=>({\n                                                        value: category.id.toString(),\n                                                        label: category.name\n                                                    })),\n                                                description: \"Choose the appropriate category for this article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Featured Image URL\",\n                                            placeholder: \"https://example.com/image.jpg\",\n                                            value: formData.featuredImage,\n                                            onChange: (e)=>updateFormData(\"featuredImage\", e.target.value),\n                                            error: errors.featuredImage,\n                                            description: \"Optional image to display with the article\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.status === \"published\",\n                                                    onCheckedChange: (checked)=>updateFormData(\"status\", checked ? \"published\" : \"draft\"),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isFeatured,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isFeatured\", checked),\n                                                    label: \"Featured Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"SEO Settings\",\n                                    description: \"Search engine optimization settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Meta Title\",\n                                                placeholder: \"SEO-optimized title for search engines\",\n                                                value: formData.metaTitle,\n                                                onChange: (e)=>updateFormData(\"metaTitle\", e.target.value),\n                                                error: errors.metaTitle,\n                                                description: \"Title tag for search engines (max 200 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Meta Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>updateFormData(\"metaDescription\", e.target.value),\n                                                        className: \"min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.metaDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: createNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 40\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: createNewsMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, this),\n                                                createNewsMutation.isLoading ? \"Creating...\" : \"Create Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateNewsPage, \"EcT60OALEW30WwC2zV5eDJNM7Zk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_7__.useCreateNews,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_8__.usePublicCategories\n    ];\n});\n_c = CreateNewsPage;\nvar _c;\n$RefreshReg$(_c, \"CreateNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/create/page.tsx\n"));

/***/ })

});