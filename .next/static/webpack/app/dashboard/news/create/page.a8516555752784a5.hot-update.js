"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/create/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcz9iNzUwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLWR5bmFtaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css":
/*!******************************************************!*\
  !*** ./node_modules/react-quill/dist/quill.snow.css ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"c1314ebde954\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWlsbC9kaXN0L3F1aWxsLnNub3cuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVpbGwvZGlzdC9xdWlsbC5zbm93LmNzcz82ZGYxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzEzMTRlYmRlOTU0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    const loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    return (0, _loadable.default)({\n        ...loadableOptions,\n        ...options\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoicURBRWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILGdEQUErQztJQUMzQ0ksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLGdCQUFnQkMsbUJBQU9BLENBQUMsZ0hBQWtCO0FBQ2hELFNBQVNGLGFBQWFHLEtBQUs7SUFDdkIsSUFBSSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHRjtJQUMzQixJQUFJLE9BQU9HLFdBQVcsYUFBYTtRQUMvQixNQUFNLElBQUlMLGNBQWNNLGlCQUFpQixDQUFDSDtJQUM5QztJQUNBLE9BQU9DO0FBQ1gsRUFFQSxrREFBa0Q7S0FSekNMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcz9mNjkxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5cInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkJhaWxvdXRUb0NTUlwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gQmFpbG91dFRvQ1NSO1xuICAgIH1cbn0pO1xuY29uc3QgX2JhaWxvdXR0b2NzciA9IHJlcXVpcmUoXCIuL2JhaWxvdXQtdG8tY3NyXCIpO1xuZnVuY3Rpb24gQmFpbG91dFRvQ1NSKHBhcmFtKSB7XG4gICAgbGV0IHsgcmVhc29uLCBjaGlsZHJlbiB9ID0gcGFyYW07XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgdGhyb3cgbmV3IF9iYWlsb3V0dG9jc3IuQmFpbG91dFRvQ1NSRXJyb3IocmVhc29uKTtcbiAgICB9XG4gICAgcmV0dXJuIGNoaWxkcmVuO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1keW5hbWljLWJhaWxvdXQtdG8tY3NyLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJCYWlsb3V0VG9DU1IiLCJfYmFpbG91dHRvY3NyIiwicmVxdWlyZSIsInBhcmFtIiwicmVhc29uIiwiY2hpbGRyZW4iLCJ3aW5kb3ciLCJCYWlsb3V0VG9DU1JFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    var _mod_default;\n    return {\n        default: (_mod_default = mod == null ? void 0 : mod.default) != null ? _mod_default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n            ...props\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/news/create/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/news/create/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CreateNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/rich-text-editor */ \"(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction CreateNewsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const createNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_7__.useCreateNews)();\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_8__.usePublicCategories)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: new Date().toISOString().split(\"T\")[0],\n        publishTime: new Date().toTimeString().slice(0, 5),\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.categoryId) newErrors.categoryId = \"Category is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            categoryId: Number(formData.categoryId),\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await createNewsMutation.mutateAsync(submitData);\n            router.push(\"/dashboard/news\");\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Create News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Write and publish a new news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"News Article Details\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Fill in the information below to create a new news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Excerpt\",\n                                                placeholder: \"Brief excerpt of the article\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>updateFormData(\"excerpt\", e.target.value),\n                                                error: errors.excerpt,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        value: formData.content,\n                                                        onChange: (value)=>updateFormData(\"content\", value),\n                                                        placeholder: \"Write your article content here...\",\n                                                        error: errors.content,\n                                                        minHeight: 300,\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. Use the toolbar above for formatting.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Category & Media\",\n                                    description: \"Article categorization and media\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                label: \"Category *\",\n                                                value: formData.categoryId,\n                                                onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                                error: errors.categoryId,\n                                                disabled: isLoadingCategories,\n                                                options: [\n                                                    {\n                                                        value: \"\",\n                                                        label: isLoadingCategories ? \"Loading categories...\" : \"Select a category\"\n                                                    },\n                                                    ...categories.map((category)=>({\n                                                            value: category.id.toString(),\n                                                            label: category.name\n                                                        }))\n                                                ],\n                                                description: \"Choose the appropriate category for this article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Featured Image URL\",\n                                            placeholder: \"https://example.com/image.jpg\",\n                                            value: formData.featuredImage,\n                                            onChange: (e)=>updateFormData(\"featuredImage\", e.target.value),\n                                            error: errors.featuredImage,\n                                            description: \"Optional image to display with the article\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.status === \"published\",\n                                                    onCheckedChange: (checked)=>updateFormData(\"status\", checked ? \"published\" : \"draft\"),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isFeatured,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isFeatured\", checked),\n                                                    label: \"Featured Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"SEO Settings\",\n                                    description: \"Search engine optimization settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Meta Title\",\n                                                placeholder: \"SEO-optimized title for search engines\",\n                                                value: formData.metaTitle,\n                                                onChange: (e)=>updateFormData(\"metaTitle\", e.target.value),\n                                                error: errors.metaTitle,\n                                                description: \"Title tag for search engines (max 200 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Meta Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>updateFormData(\"metaDescription\", e.target.value),\n                                                        className: \"min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.metaDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: createNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 40\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: createNewsMutation.isLoading,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                createNewsMutation.isLoading ? \"Creating...\" : \"Create Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/create/page.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n_s(CreateNewsPage, \"EcT60OALEW30WwC2zV5eDJNM7Zk=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_7__.useCreateNews,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_8__.usePublicCategories\n    ];\n});\n_c = CreateNewsPage;\nvar _c;\n$RefreshReg$(_c, \"CreateNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/create/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/rich-text-editor.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/rich-text-editor.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Dynamically import ReactQuill to avoid SSR issues\nconst ReactQuill = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-quill_lib_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"(app-pages-browser)/./node_modules/react-quill/lib/index.js\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-[200px] w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-5/6\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n});\n_c = ReactQuill;\n// Import Quill styles\n\nconst RichTextEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value = \"\", onChange, placeholder = \"Start writing...\", className, error, disabled = false, minHeight = 200 } = param;\n    _s();\n    const quillRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            focus: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.focus();\n                }\n            },\n            blur: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.blur();\n                }\n            },\n            getEditor: ()=>{\n                var _quillRef_current;\n                return (_quillRef_current = quillRef.current) === null || _quillRef_current === void 0 ? void 0 : _quillRef_current.getEditor();\n            }\n        }));\n    // Quill modules configuration\n    const modules = {\n        toolbar: [\n            [\n                {\n                    \"header\": [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6,\n                        false\n                    ]\n                }\n            ],\n            [\n                {\n                    \"font\": []\n                }\n            ],\n            [\n                {\n                    \"size\": [\n                        \"small\",\n                        false,\n                        \"large\",\n                        \"huge\"\n                    ]\n                }\n            ],\n            [\n                \"bold\",\n                \"italic\",\n                \"underline\",\n                \"strike\"\n            ],\n            [\n                {\n                    \"color\": []\n                },\n                {\n                    \"background\": []\n                }\n            ],\n            [\n                {\n                    \"script\": \"sub\"\n                },\n                {\n                    \"script\": \"super\"\n                }\n            ],\n            [\n                {\n                    \"list\": \"ordered\"\n                },\n                {\n                    \"list\": \"bullet\"\n                }\n            ],\n            [\n                {\n                    \"indent\": \"-1\"\n                },\n                {\n                    \"indent\": \"+1\"\n                }\n            ],\n            [\n                {\n                    \"direction\": \"rtl\"\n                }\n            ],\n            [\n                {\n                    \"align\": []\n                }\n            ],\n            [\n                \"blockquote\",\n                \"code-block\"\n            ],\n            [\n                \"link\",\n                \"image\",\n                \"video\"\n            ],\n            [\n                \"clean\"\n            ]\n        ],\n        clipboard: {\n            matchVisual: false\n        }\n    };\n    // Quill formats\n    const formats = [\n        \"header\",\n        \"font\",\n        \"size\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"color\",\n        \"background\",\n        \"script\",\n        \"list\",\n        \"bullet\",\n        \"indent\",\n        \"direction\",\n        \"align\",\n        \"blockquote\",\n        \"code-block\",\n        \"link\",\n        \"image\",\n        \"video\"\n    ];\n    // Custom styles for the editor\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Add custom CSS for the editor\n        const style = document.createElement(\"style\");\n        style.textContent = \"\\n      .ql-editor {\\n        min-height: \".concat(minHeight, \"px;\\n        font-family: inherit;\\n        font-size: 14px;\\n        line-height: 1.6;\\n      }\\n      .ql-toolbar {\\n        border-top: 1px solid #e2e8f0;\\n        border-left: 1px solid #e2e8f0;\\n        border-right: 1px solid #e2e8f0;\\n        border-bottom: none;\\n        border-radius: 6px 6px 0 0;\\n      }\\n      .ql-container {\\n        border-bottom: 1px solid #e2e8f0;\\n        border-left: 1px solid #e2e8f0;\\n        border-right: 1px solid #e2e8f0;\\n        border-top: none;\\n        border-radius: 0 0 6px 6px;\\n        font-family: inherit;\\n      }\\n      .ql-editor.ql-blank::before {\\n        color: #9ca3af;\\n        font-style: normal;\\n      }\\n      \").concat(error ? \"\\n        .ql-toolbar,\\n        .ql-container {\\n          border-color: #ef4444;\\n        }\\n      \" : \"\", \"\\n      \").concat(disabled ? \"\\n        .ql-toolbar {\\n          pointer-events: none;\\n          opacity: 0.6;\\n        }\\n        .ql-editor {\\n          background-color: #f9fafb;\\n          color: #6b7280;\\n        }\\n      \" : \"\", \"\\n    \");\n        document.head.appendChild(style);\n        return ()=>{\n            document.head.removeChild(style);\n        };\n    }, [\n        minHeight,\n        error,\n        disabled\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQuill, {\n                ref: quillRef,\n                theme: \"snow\",\n                value: value,\n                onChange: onChange,\n                placeholder: placeholder,\n                modules: modules,\n                formats: formats,\n                readOnly: disabled,\n                style: {\n                    backgroundColor: disabled ? \"#f9fafb\" : \"white\"\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n}, \"B5iL0NdUNZPneueEEztTCcVNiMA=\")), \"B5iL0NdUNZPneueEEztTCcVNiMA=\");\n_c2 = RichTextEditor;\nRichTextEditor.displayName = \"RichTextEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (RichTextEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ReactQuill\");\n$RefreshReg$(_c1, \"RichTextEditor$forwardRef\");\n$RefreshReg$(_c2, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\n"));

/***/ })

});