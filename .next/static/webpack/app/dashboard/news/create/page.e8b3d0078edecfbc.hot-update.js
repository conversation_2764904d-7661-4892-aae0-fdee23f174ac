"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/create/page",{

/***/ "(app-pages-browser)/./src/lib/api/news.ts":
/*!*****************************!*\
  !*** ./src/lib/api/news.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   newsApi: function() { return /* binding */ newsApi; }\n/* harmony export */ });\n/* harmony import */ var _lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/news-transform */ \"(app-pages-browser)/./src/lib/utils/news-transform.ts\");\n\n// Helper function to get auth token\nconst getAuthToken = ()=>{\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                return ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken) || null;\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        return localStorage.getItem(\"accessToken\");\n    }\n    return null;\n};\nconst newsApi = {\n    getNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const transformedFilters = (0,_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsFilters)(filters);\n        const params = new URLSearchParams();\n        Object.entries(transformedFilters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(key, v.toString()));\n                } else {\n                    params.append(key, value.toString());\n                }\n            }\n        });\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news?\".concat(params.toString()), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news\");\n        }\n        const data = await response.json();\n        // Transform the news data\n        if (data.data) {\n            data.data = data.data.map(_lib_utils_news_transform__WEBPACK_IMPORTED_MODULE_0__.transformNewsData);\n        }\n        return data;\n    },\n    // Get single news item (includes auth if available)\n    getNewsById: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news \".concat(id));\n        }\n        return await response.json();\n    },\n    // Editor+ access required\n    createNews: async (data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to create news\");\n        }\n        return await response.json();\n    },\n    // Editor+ access required\n    updateNews: async (id, data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"PATCH\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to update news \".concat(id));\n        }\n        return await response.json();\n    },\n    // Admin access required\n    deleteNews: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to delete news \".concat(id));\n        }\n    },\n    // Helper methods for common operations\n    getPublishedNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            status: \"published\"\n        });\n    },\n    getHotNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            isFeatured: true\n        });\n    },\n    getNewsByAuthor: async function(author) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return newsApi.getNews({\n            ...filters,\n            author\n        });\n    },\n    toggleNewsStatus: async (id, isPublished)=>{\n        return newsApi.updateNews(id, {\n            status: isPublished ? \"published\" : \"draft\",\n            publishedAt: isPublished ? new Date().toISOString() : undefined\n        });\n    },\n    toggleHotStatus: async (id, isHot)=>{\n        return newsApi.updateNews(id, {\n            isFeatured: isHot\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/news.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/utils/news-transform.ts":
/*!*****************************************!*\
  !*** ./src/lib/utils/news-transform.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transformCreateNewsData: function() { return /* binding */ transformCreateNewsData; },\n/* harmony export */   transformNewsData: function() { return /* binding */ transformNewsData; },\n/* harmony export */   transformNewsFilters: function() { return /* binding */ transformNewsFilters; },\n/* harmony export */   transformUpdateNewsData: function() { return /* binding */ transformUpdateNewsData; }\n/* harmony export */ });\n/**\n * Transform backend news data to frontend format with computed properties\n */ function transformNewsData(backendNews) {\n    return {\n        ...backendNews,\n        // Computed properties for compatibility\n        author: \"Author \".concat(backendNews.authorId),\n        summary: backendNews.excerpt,\n        imageUrl: backendNews.featuredImage,\n        isPublished: backendNews.status === \"published\",\n        isHot: backendNews.isFeatured,\n        publishDate: backendNews.publishedAt || backendNews.createdAt\n    };\n}\n/**\n * Transform frontend form data to backend format\n */ function transformCreateNewsData(frontendData) {\n    return {\n        title: frontendData.title,\n        content: frontendData.content,\n        categoryId: frontendData.categoryId,\n        excerpt: frontendData.summary,\n        featuredImage: frontendData.imageUrl,\n        tags: frontendData.tags,\n        status: frontendData.isPublished ? \"published\" : \"draft\",\n        publishedAt: frontendData.isPublished ? frontendData.publishDate : undefined,\n        isFeatured: frontendData.isHot,\n        metaTitle: frontendData.metaTitle,\n        metaDescription: frontendData.metaDescription,\n        priority: frontendData.isHot ? 1 : 0\n    };\n}\n/**\n * Transform frontend update data to backend format\n */ function transformUpdateNewsData(frontendData) {\n    const updateData = {};\n    if (frontendData.title !== undefined) updateData.title = frontendData.title;\n    if (frontendData.content !== undefined) updateData.content = frontendData.content;\n    if (frontendData.categoryId !== undefined) updateData.categoryId = frontendData.categoryId;\n    if (frontendData.summary !== undefined) updateData.excerpt = frontendData.summary;\n    if (frontendData.imageUrl !== undefined) updateData.featuredImage = frontendData.imageUrl;\n    if (frontendData.tags !== undefined) updateData.tags = frontendData.tags;\n    if (frontendData.metaTitle !== undefined) updateData.metaTitle = frontendData.metaTitle;\n    if (frontendData.metaDescription !== undefined) updateData.metaDescription = frontendData.metaDescription;\n    // Handle status changes\n    if (frontendData.isPublished !== undefined) {\n        updateData.status = frontendData.isPublished ? \"published\" : \"draft\";\n        if (frontendData.isPublished && frontendData.publishDate) {\n            updateData.publishedAt = frontendData.publishDate;\n        }\n    }\n    // Handle featured status\n    if (frontendData.isHot !== undefined) {\n        updateData.isFeatured = frontendData.isHot;\n        updateData.priority = frontendData.isHot ? 1 : 0;\n    }\n    return updateData;\n}\n/**\n * Transform filters from frontend to backend format\n */ function transformNewsFilters(frontendFilters) {\n    const backendFilters = {\n        ...frontendFilters\n    };\n    // Transform legacy filters\n    if (frontendFilters.isPublished !== undefined) {\n        backendFilters.status = frontendFilters.isPublished ? \"published\" : \"draft\";\n        delete backendFilters.isPublished;\n    }\n    if (frontendFilters.isHot !== undefined) {\n        backendFilters.isFeatured = frontendFilters.isHot;\n        delete backendFilters.isHot;\n    }\n    return backendFilters;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/news-transform.ts\n"));

/***/ })

});