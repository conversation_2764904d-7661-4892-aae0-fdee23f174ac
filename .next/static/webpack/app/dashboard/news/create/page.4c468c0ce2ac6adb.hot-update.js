"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/create/page",{

/***/ "(app-pages-browser)/./src/lib/api/news.ts":
/*!*****************************!*\
  !*** ./src/lib/api/news.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   newsApi: function() { return /* binding */ newsApi; }\n/* harmony export */ });\n// Helper function to get auth token\nconst getAuthToken = ()=>{\n    if (true) {\n        try {\n            const authStorage = localStorage.getItem(\"auth-storage\");\n            if (authStorage) {\n                var _parsed_state;\n                const parsed = JSON.parse(authStorage);\n                return ((_parsed_state = parsed.state) === null || _parsed_state === void 0 ? void 0 : _parsed_state.accessToken) || null;\n            }\n        } catch (error) {\n            console.warn(\"Failed to parse auth storage:\", error);\n        }\n        return localStorage.getItem(\"accessToken\");\n    }\n    return null;\n};\nconst newsApi = {\n    getNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                if (Array.isArray(value)) {\n                    value.forEach((v)=>params.append(key, v.toString()));\n                } else {\n                    params.append(key, value.toString());\n                }\n            }\n        });\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news?\".concat(params.toString()), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news\");\n        }\n        return await response.json();\n    },\n    // Get single news item (includes auth if available)\n    getNewsById: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch news \".concat(id));\n        }\n        return await response.json();\n    },\n    // Editor+ access required\n    createNews: async (data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news\", {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to create news\");\n        }\n        return await response.json();\n    },\n    // Editor+ access required\n    updateNews: async (id, data)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"PATCH\",\n            headers,\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to update news \".concat(id));\n        }\n        return await response.json();\n    },\n    // Admin access required\n    deleteNews: async (id)=>{\n        const token = getAuthToken();\n        const headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (token) {\n            headers[\"Authorization\"] = \"Bearer \".concat(token);\n        }\n        const response = await fetch(\"/api/news/\".concat(id), {\n            method: \"DELETE\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to delete news \".concat(id));\n        }\n    },\n    // Helper methods for common operations\n    getPublishedNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            status: \"published\"\n        });\n    },\n    getHotNews: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        return newsApi.getNews({\n            ...filters,\n            isFeatured: true\n        });\n    },\n    getNewsByAuthor: async function(author) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return newsApi.getNews({\n            ...filters,\n            author\n        });\n    },\n    toggleNewsStatus: async (id, isPublished)=>{\n        return newsApi.updateNews(id, {\n            status: isPublished ? \"published\" : \"draft\",\n            publishedAt: isPublished ? new Date().toISOString() : undefined\n        });\n    },\n    toggleHotStatus: async (id, isHot)=>{\n        return newsApi.updateNews(id, {\n            isFeatured: isHot\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/news.ts\n"));

/***/ })

});