"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/folder.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Folder; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.511.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z\",\n      key: \"1kt360\"\n    }\n  ]\n];\nconst Folder = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"folder\", __iconNode);\n\n\n//# sourceMappingURL=folder.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZm9sZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXREO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZ0VBQWdCOztBQUVVO0FBQ3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZm9sZGVyLmpzPzIwZDciXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNTExLjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMjAgMjBhMiAyIDAgMCAwIDItMlY4YTIgMiAwIDAgMC0yLTJoLTcuOWEyIDIgMCAwIDEtMS42OS0uOUw5LjYgMy45QTIgMiAwIDAgMCA3LjkzIDNINGEyIDIgMCAwIDAtMiAydjEzYTIgMiAwIDAgMCAyIDJaXCIsXG4gICAgICBrZXk6IFwiMWt0MzYwXCJcbiAgICB9XG4gIF1cbl07XG5jb25zdCBGb2xkZXIgPSBjcmVhdGVMdWNpZGVJY29uKFwiZm9sZGVyXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBGb2xkZXIgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Zm9sZGVyLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/news/[id]/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewsDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Folder,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewsDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions)();\n    const newsId = parseInt(params.id);\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Data fetching\n    const { data: news, isLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useNewsById)(newsId);\n    // Mutations\n    const deleteNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useDeleteNews)();\n    const toggleStatusMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleNewsStatus)();\n    const toggleHotMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleHotStatus)();\n    // Event handlers\n    const handleEdit = ()=>{\n        if (!isEditor() && !isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"You do not have permission to edit news\");\n            return;\n        }\n        router.push(\"/dashboard/news/\".concat(newsId, \"/edit\"));\n    };\n    const handleDelete = ()=>{\n        if (!isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"You do not have permission to delete news\");\n            return;\n        }\n        setDeleteModalOpen(true);\n    };\n    const confirmDelete = async ()=>{\n        try {\n            await deleteNewsMutation.mutateAsync(newsId);\n            setDeleteModalOpen(false);\n            router.push(\"/dashboard/news\");\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const handleToggleStatus = (isPublished)=>{\n        toggleStatusMutation.mutate({\n            id: newsId,\n            isPublished\n        });\n    };\n    const handleToggleHot = (isHot)=>{\n        toggleHotMutation.mutate({\n            id: newsId,\n            isHot\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-40 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-4 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-4 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-4 w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are looking for does not exist or has been removed.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n            lineNumber: 110,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"News Article Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: news.status === \"published\" ? \"default\" : \"secondary\",\n                                                            children: news.status === \"published\" ? \"Published\" : news.status === \"draft\" ? \"Draft\" : \"Archived\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        news.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"destructive\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                    lineNumber: 179,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Featured\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"inline w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        new Date(news.publishedAt || news.createdAt).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-2xl\",\n                                            children: news.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        news.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: news.excerpt\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        news.featuredImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: news.featuredImage,\n                                                alt: news.title,\n                                                className: \"w-full h-64 object-cover rounded-lg\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                dangerouslySetInnerHTML: {\n                                                    __html: news.content\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Article Information\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Author\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    \"Author \",\n                                                                    news.authorId\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 226,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            news.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Category\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 234,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    style: {\n                                                                        borderColor: news.category.color || \"#6b7280\",\n                                                                        color: news.category.color || \"#6b7280\"\n                                                                    },\n                                                                    children: news.category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                    lineNumber: 236,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Publish Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: news.publishedAt ? new Date(news.publishedAt).toLocaleDateString(\"en-US\", {\n                                                                    year: \"numeric\",\n                                                                    month: \"long\",\n                                                                    day: \"numeric\",\n                                                                    hour: \"2-digit\",\n                                                                    minute: \"2-digit\"\n                                                                }) : \"Not published yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(news.createdAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Last Updated\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(news.updatedAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            news.featuredImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Featured Image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            news.tags && news.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Folder_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Tags\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: news.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"outline\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this),\n                            (isEditor() || isAdmin()) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Published Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"                    \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__.ToggleSwitch, {\n                                                            checked: news.status === \"published\",\n                                                            onCheckedChange: handleToggleStatus,\n                                                            disabled: toggleStatusMutation.isLoading,\n                                                            size: \"sm\",\n                                                            label: \"Published Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 102\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Featured Article\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"                    \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__.ToggleSwitch, {\n                                                            checked: news.isFeatured,\n                                                            onCheckedChange: handleToggleHot,\n                                                            disabled: toggleHotMutation.isLoading,\n                                                            size: \"sm\",\n                                                            variant: \"danger\",\n                                                            label: \"Featured Article\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 102\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete News Article\",\n                message: 'Are you sure you want to delete \"'.concat(news.title, '\"? This action cannot be undone.'),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                loading: deleteNewsMutation.isLoading,\n                variant: \"destructive\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                lineNumber: 353,\n                columnNumber: 46\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n        lineNumber: 137,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsDetailPage, \"rcEfmwiDtXzqD+4X7K5yePaxo98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useNewsById,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useDeleteNews,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleNewsStatus,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleHotStatus\n    ];\n});\n_c = NewsDetailPage;\nvar _c;\n$RefreshReg$(_c, \"NewsDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/page.tsx\n"));

/***/ })

});