"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/news/[id]/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewsDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Edit,Eye,Globe,Image,Tag,Trash2,TrendingUp,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NewsDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions)();\n    const newsId = parseInt(params.id);\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Data fetching\n    const { data: news, isLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useNewsById)(newsId);\n    // Mutations\n    const deleteNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useDeleteNews)();\n    const toggleStatusMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleNewsStatus)();\n    const toggleHotMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleHotStatus)();\n    // Event handlers\n    const handleEdit = ()=>{\n        if (!isEditor() && !isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"You do not have permission to edit news\");\n            return;\n        }\n        router.push(\"/dashboard/news/\".concat(newsId, \"/edit\"));\n    };\n    const handleDelete = ()=>{\n        if (!isAdmin()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"You do not have permission to delete news\");\n            return;\n        }\n        setDeleteModalOpen(true);\n    };\n    const confirmDelete = async ()=>{\n        try {\n            await deleteNewsMutation.mutateAsync(newsId);\n            setDeleteModalOpen(false);\n            router.push(\"/dashboard/news\");\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const handleToggleStatus = (isPublished)=>{\n        toggleStatusMutation.mutate({\n            id: newsId,\n            isPublished\n        });\n    };\n    const handleToggleHot = (isHot)=>{\n        toggleHotMutation.mutate({\n            id: newsId,\n            isHot\n        });\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                    className: \"h-40 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-4 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-4 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_10__.Skeleton, {\n                                            className: \"h-4 w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are looking for does not exist or has been removed.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: news.title\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"News Article Details\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-2\",\n                        children: [\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleEdit,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Edit\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleDelete,\n                                className: \"text-red-600 hover:text-red-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: news.status === \"published\" ? \"default\" : \"secondary\",\n                                                            children: news.status === \"published\" ? \"Published\" : news.status === \"draft\" ? \"Draft\" : \"Archived\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        news.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"destructive\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                    lineNumber: 178,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Featured\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 177,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-500\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"inline w-4 h-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        new Date(news.publishDate).toLocaleDateString()\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-2xl\",\n                                            children: news.title\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 15\n                                        }, this),\n                                        news.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            className: \"text-lg\",\n                                            children: news.summary\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: [\n                                        news.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: news.imageUrl,\n                                                alt: news.title,\n                                                className: \"w-full h-64 object-cover rounded-lg\",\n                                                onError: (e)=>{\n                                                    e.currentTarget.style.display = \"none\";\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                dangerouslySetInnerHTML: {\n                                                    __html: news.content\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Article Information\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Author\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: news.author\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Publish Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(news.publishDate).toLocaleDateString(\"en-US\", {\n                                                                    year: \"numeric\",\n                                                                    month: \"long\",\n                                                                    day: \"numeric\",\n                                                                    hour: \"2-digit\",\n                                                                    minute: \"2-digit\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(news.createdAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Last Updated\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(news.updatedAt).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            news.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: \"Featured Image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"Available\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this),\n                            news.tags && news.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Edit_Eye_Globe_Image_Tag_Trash2_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Tags\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: news.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"outline\",\n                                                    children: tag\n                                                }, index, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this),\n                            (isEditor() || isAdmin()) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Published Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"                    \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__.ToggleSwitch, {\n                                                            checked: news.isPublished,\n                                                            onCheckedChange: handleToggleStatus,\n                                                            disabled: toggleStatusMutation.isLoading,\n                                                            size: \"sm\",\n                                                            label: \"Published Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 102\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Hot Article\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"                    \",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__.ToggleSwitch, {\n                                                            checked: news.isHot,\n                                                            onCheckedChange: handleToggleHot,\n                                                            disabled: toggleHotMutation.isLoading,\n                                                            size: \"sm\",\n                                                            variant: \"danger\",\n                                                            label: \"Hot Article\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 97\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            \"      \",\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete News Article\",\n                message: 'Are you sure you want to delete \"'.concat(news.title, '\"? This action cannot be undone.'),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                loading: deleteNewsMutation.isLoading,\n                variant: \"destructive\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n                lineNumber: 332,\n                columnNumber: 46\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/page.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, this);\n}\n_s(NewsDetailPage, \"rcEfmwiDtXzqD+4X7K5yePaxo98=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_8__.usePermissions,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useNewsById,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useDeleteNews,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleNewsStatus,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useToggleHotStatus\n    ];\n});\n_c = NewsDetailPage;\nvar _c;\n$RefreshReg$(_c, \"NewsDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/page.tsx\n"));

/***/ })

});