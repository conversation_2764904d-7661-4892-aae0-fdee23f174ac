"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/utils/news-transform.ts":
/*!*****************************************!*\
  !*** ./src/lib/utils/news-transform.ts ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   transformCreateNewsData: function() { return /* binding */ transformCreateNewsData; },\n/* harmony export */   transformNewsData: function() { return /* binding */ transformNewsData; },\n/* harmony export */   transformNewsFilters: function() { return /* binding */ transformNewsFilters; },\n/* harmony export */   transformUpdateNewsData: function() { return /* binding */ transformUpdateNewsData; }\n/* harmony export */ });\n/**\n * Transform backend news data to frontend format with computed properties\n */ function transformNewsData(backendNews) {\n    return {\n        ...backendNews,\n        // Computed properties for compatibility\n        author: \"Author \".concat(backendNews.authorId),\n        summary: backendNews.excerpt,\n        imageUrl: backendNews.featuredImage,\n        isPublished: backendNews.status === \"published\",\n        isHot: backendNews.isFeatured,\n        publishDate: backendNews.publishedAt || backendNews.createdAt\n    };\n}\n/**\n * Transform frontend form data to backend format\n */ function transformCreateNewsData(frontendData) {\n    return {\n        title: frontendData.title,\n        content: frontendData.content,\n        categoryId: frontendData.categoryId,\n        excerpt: frontendData.summary,\n        featuredImage: frontendData.imageUrl,\n        tags: frontendData.tags,\n        status: frontendData.isPublished ? \"published\" : \"draft\",\n        publishedAt: frontendData.isPublished ? frontendData.publishDate : undefined,\n        isFeatured: frontendData.isHot,\n        metaTitle: frontendData.metaTitle,\n        metaDescription: frontendData.metaDescription,\n        priority: frontendData.isHot ? 1 : 0\n    };\n}\n/**\n * Transform frontend update data to backend format\n */ function transformUpdateNewsData(frontendData) {\n    const updateData = {};\n    // Basic fields\n    if (frontendData.title !== undefined) updateData.title = frontendData.title;\n    if (frontendData.content !== undefined) updateData.content = frontendData.content;\n    if (frontendData.categoryId !== undefined) {\n        // Ensure categoryId is a number\n        updateData.categoryId = typeof frontendData.categoryId === \"string\" ? parseInt(frontendData.categoryId) : frontendData.categoryId;\n    }\n    if (frontendData.tags !== undefined) updateData.tags = frontendData.tags;\n    if (frontendData.metaTitle !== undefined) updateData.metaTitle = frontendData.metaTitle;\n    if (frontendData.metaDescription !== undefined) updateData.metaDescription = frontendData.metaDescription;\n    // Handle new field names\n    if (frontendData.excerpt !== undefined) updateData.excerpt = frontendData.excerpt;\n    if (frontendData.featuredImage !== undefined) updateData.featuredImage = frontendData.featuredImage;\n    if (frontendData.status !== undefined) updateData.status = frontendData.status;\n    if (frontendData.isFeatured !== undefined) updateData.isFeatured = frontendData.isFeatured;\n    // Handle legacy field names for backward compatibility\n    if (frontendData.summary !== undefined) updateData.excerpt = frontendData.summary;\n    if (frontendData.imageUrl !== undefined) updateData.featuredImage = frontendData.imageUrl;\n    // Handle legacy status changes\n    if (frontendData.isPublished !== undefined) {\n        updateData.status = frontendData.isPublished ? \"published\" : \"draft\";\n    }\n    // Handle legacy featured status\n    if (frontendData.isHot !== undefined) {\n        updateData.isFeatured = frontendData.isHot;\n    }\n    // Debug log\n    console.log(\"Transform Update Data:\", {\n        frontendData,\n        updateData\n    });\n    return updateData;\n}\n/**\n * Transform filters from frontend to backend format\n */ function transformNewsFilters(frontendFilters) {\n    const backendFilters = {\n        ...frontendFilters\n    };\n    // Transform legacy filters\n    if (frontendFilters.isPublished !== undefined) {\n        backendFilters.status = frontendFilters.isPublished ? \"published\" : \"draft\";\n        delete backendFilters.isPublished;\n    }\n    if (frontendFilters.isHot !== undefined) {\n        backendFilters.isFeatured = frontendFilters.isHot;\n        delete backendFilters.isHot;\n    }\n    return backendFilters;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/news-transform.ts\n"));

/***/ })

});