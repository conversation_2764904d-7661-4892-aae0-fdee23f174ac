"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/news/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction EditNewsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const newsId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: \"\",\n        publishTime: \"\",\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Data fetching\n    const { data: news, isLoading: newsLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById)(newsId);\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories)();\n    // Mutations\n    const updateNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews)();\n    // Populate form when news data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (news) {\n            const publishDate = new Date(news.publishedAt || news.createdAt);\n            setFormData({\n                title: news.title,\n                content: news.content,\n                excerpt: news.excerpt || \"\",\n                featuredImage: news.featuredImage || \"\",\n                tags: news.tags ? news.tags.join(\", \") : \"\",\n                categoryId: news.categoryId ? news.categoryId.toString() : \"\",\n                status: news.status,\n                isFeatured: news.isFeatured,\n                publishDate: publishDate.toISOString().split(\"T\")[0],\n                publishTime: publishDate.toTimeString().slice(0, 5),\n                metaTitle: news.metaTitle || \"\",\n                metaDescription: news.metaDescription || \"\"\n            });\n        }\n    }, [\n        news\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await updateNewsMutation.mutateAsync({\n                id: newsId,\n                data: submitData\n            });\n            router.push(\"/dashboard/news/\".concat(newsId));\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    if (newsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are trying to edit does not exist.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update the article information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Edit Article: \",\n                                    news.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the information below to modify the news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Summary\",\n                                                placeholder: \"Brief summary of the article\",\n                                                value: formData.summary,\n                                                onChange: (e)=>updateFormData(\"summary\", e.target.value),\n                                                error: errors.summary,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Write your article content here...\",\n                                                        required: true,\n                                                        value: formData.content,\n                                                        onChange: (e)=>updateFormData(\"content\", e.target.value),\n                                                        className: \"min-h-[300px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. HTML tags are supported.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Author & Media\",\n                                    description: \"Author information and media assets\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Author *\",\n                                                    placeholder: \"Author name\",\n                                                    required: true,\n                                                    value: formData.author,\n                                                    onChange: (e)=>updateFormData(\"author\", e.target.value),\n                                                    error: errors.author\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Category\",\n                                                    placeholder: \"Select a category\",\n                                                    value: formData.categoryId,\n                                                    onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                                    error: errors.categoryId,\n                                                    options: categories.map((category)=>({\n                                                            value: category.id.toString(),\n                                                            label: category.name\n                                                        })),\n                                                    disabled: isLoadingCategories,\n                                                    description: \"Choose the news category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Featured Image URL\",\n                                                    placeholder: \"https://example.com/image.jpg\",\n                                                    value: formData.imageUrl,\n                                                    onChange: (e)=>updateFormData(\"imageUrl\", e.target.value),\n                                                    error: errors.imageUrl,\n                                                    description: \"Optional image to display with the article\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isPublished,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isPublished\", checked),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isHot,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isHot\", checked),\n                                                    label: \"Hot Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 40\n                                        }, this),\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: [\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 30\n                                                }, this),\n                                                updateNewsMutation.isLoading ? \"Updating...\" : \"Update Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(EditNewsPage, \"l/CaPmMML2VztxFzqV5C817tgTQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews\n    ];\n});\n_c = EditNewsPage;\nvar _c;\n$RefreshReg$(_c, \"EditNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx\n"));

/***/ })

});