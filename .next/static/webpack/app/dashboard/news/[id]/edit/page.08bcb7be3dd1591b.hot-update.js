"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/news/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/rich-text-editor */ \"(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditNewsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const newsId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: \"\",\n        publishTime: \"\",\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Data fetching\n    const { data: news, isLoading: newsLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useNewsById)(newsId);\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_10__.usePublicCategories)();\n    // Mutations\n    const updateNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useUpdateNews)();\n    // Populate form when news data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (news) {\n            const publishDate = new Date(news.publishedAt || news.createdAt);\n            setFormData({\n                title: news.title,\n                content: news.content,\n                excerpt: news.excerpt || \"\",\n                featuredImage: news.featuredImage || \"\",\n                tags: news.tags ? news.tags.join(\", \") : \"\",\n                categoryId: news.categoryId ? news.categoryId.toString() : \"\",\n                status: news.status,\n                isFeatured: news.isFeatured,\n                publishDate: publishDate.toISOString().split(\"T\")[0],\n                publishTime: publishDate.toTimeString().slice(0, 5),\n                metaTitle: news.metaTitle || \"\",\n                metaDescription: news.metaDescription || \"\"\n            });\n        }\n    }, [\n        news\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_11__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await updateNewsMutation.mutateAsync({\n                id: newsId,\n                data: submitData\n            });\n            router.push(\"/dashboard/news/\".concat(newsId));\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    if (newsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are trying to edit does not exist.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update the article information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Edit Article: \",\n                                    news.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the information below to modify the news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                                label: \"Excerpt\",\n                                                placeholder: \"Brief excerpt of the article\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>updateFormData(\"excerpt\", e.target.value),\n                                                error: errors.excerpt,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        value: formData.content,\n                                                        onChange: (value)=>updateFormData(\"content\", value),\n                                                        placeholder: \"Write your article content here...\",\n                                                        error: errors.content,\n                                                        minHeight: 300,\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. Use the toolbar above for formatting.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.FormSection, {\n                                    title: \"Category & Media\",\n                                    description: \"Article categorization and media assets\",\n                                    children: [\n                                        (news === null || news === void 0 ? void 0 : news.category) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800 mb-2\",\n                                                    children: \"Current Category:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"outline\",\n                                                    style: {\n                                                        borderColor: news.category.color || \"#3b82f6\",\n                                                        color: news.category.color || \"#3b82f6\",\n                                                        backgroundColor: \"\".concat(news.category.color || \"#3b82f6\", \"10\")\n                                                    },\n                                                    children: news.category.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.RadioField, {\n                                            label: \"Category\",\n                                            value: formData.categoryId,\n                                            onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                            error: errors.categoryId,\n                                            options: categories.map((category)=>({\n                                                    value: category.id.toString(),\n                                                    label: category.name,\n                                                    disabled: isLoadingCategories\n                                                })),\n                                            orientation: \"vertical\",\n                                            description: \"Choose the news category\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 280,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                            label: \"Featured Image URL\",\n                                            placeholder: \"https://example.com/image.jpg\",\n                                            value: formData.featuredImage,\n                                            onChange: (e)=>updateFormData(\"featuredImage\", e.target.value),\n                                            error: errors.featuredImage,\n                                            description: \"Optional image to display with the article\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__.ToggleSwitch, {\n                                                    checked: formData.status === \"published\",\n                                                    onCheckedChange: (checked)=>updateFormData(\"status\", checked ? \"published\" : \"draft\"),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_7__.ToggleSwitch, {\n                                                    checked: formData.isFeatured,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isFeatured\", checked),\n                                                    label: \"Featured Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 341,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.FormSection, {\n                                    title: \"SEO Settings\",\n                                    description: \"Search engine optimization settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.InputField, {\n                                                label: \"Meta Title\",\n                                                placeholder: \"SEO-optimized title for search engines\",\n                                                value: formData.metaTitle,\n                                                onChange: (e)=>updateFormData(\"metaTitle\", e.target.value),\n                                                error: errors.metaTitle,\n                                                description: \"Title tag for search engines (max 200 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Meta Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 363,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>updateFormData(\"metaDescription\", e.target.value),\n                                                        className: \"min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.metaDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_6__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 40\n                                        }, this),\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: [\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 30\n                                                }, this),\n                                                updateNewsMutation.isLoading ? \"Updating...\" : \"Update Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(EditNewsPage, \"l/CaPmMML2VztxFzqV5C817tgTQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useNewsById,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_10__.usePublicCategories,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_9__.useUpdateNews\n    ];\n});\n_c = EditNewsPage;\nvar _c;\n$RefreshReg$(_c, \"EditNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: function() { return /* binding */ Badge; },\n/* harmony export */   badgeVariants: function() { return /* binding */ badgeVariants; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge(param) {\n    let { className, variant, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/badge.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c = Badge;\n\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ })

});