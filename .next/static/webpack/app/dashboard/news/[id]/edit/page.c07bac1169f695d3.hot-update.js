"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/news/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction EditNewsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const newsId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: \"\",\n        publishTime: \"\",\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Data fetching\n    const { data: news, isLoading: newsLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById)(newsId);\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories)();\n    // Mutations\n    const updateNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews)();\n    // Populate form when news data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (news) {\n            const publishDate = new Date(news.publishedAt || news.createdAt);\n            setFormData({\n                title: news.title,\n                content: news.content,\n                excerpt: news.excerpt || \"\",\n                featuredImage: news.featuredImage || \"\",\n                tags: news.tags ? news.tags.join(\", \") : \"\",\n                categoryId: news.categoryId ? news.categoryId.toString() : \"\",\n                status: news.status,\n                isFeatured: news.isFeatured,\n                publishDate: publishDate.toISOString().split(\"T\")[0],\n                publishTime: publishDate.toTimeString().slice(0, 5),\n                metaTitle: news.metaTitle || \"\",\n                metaDescription: news.metaDescription || \"\"\n            });\n        }\n    }, [\n        news\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await updateNewsMutation.mutateAsync({\n                id: newsId,\n                data: submitData\n            });\n            router.push(\"/dashboard/news/\".concat(newsId));\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    if (newsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are trying to edit does not exist.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 172,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update the article information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Edit Article: \",\n                                    news.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the information below to modify the news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Excerpt\",\n                                                placeholder: \"Brief excerpt of the article\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>updateFormData(\"excerpt\", e.target.value),\n                                                error: errors.excerpt,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Write your article content here...\",\n                                                        required: true,\n                                                        value: formData.content,\n                                                        onChange: (e)=>updateFormData(\"content\", e.target.value),\n                                                        className: \"min-h-[300px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. HTML tags are supported.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Category & Media\",\n                                    description: \"Article categorization and media assets\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Category\",\n                                                    placeholder: \"Select a category\",\n                                                    value: formData.categoryId,\n                                                    onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                                    error: errors.categoryId,\n                                                    options: categories.map((category)=>({\n                                                            value: category.id.toString(),\n                                                            label: category.name\n                                                        })),\n                                                    disabled: isLoadingCategories,\n                                                    description: \"Choose the news category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Featured Image URL\",\n                                                    placeholder: \"https://example.com/image.jpg\",\n                                                    value: formData.featuredImage,\n                                                    onChange: (e)=>updateFormData(\"featuredImage\", e.target.value),\n                                                    error: errors.featuredImage,\n                                                    description: \"Optional image to display with the article\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isPublished,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isPublished\", checked),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isHot,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isHot\", checked),\n                                                    label: \"Hot Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 40\n                                        }, this),\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: [\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 30\n                                                }, this),\n                                                updateNewsMutation.isLoading ? \"Updating...\" : \"Update Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n        lineNumber: 199,\n        columnNumber: 5\n    }, this);\n}\n_s(EditNewsPage, \"l/CaPmMML2VztxFzqV5C817tgTQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews\n    ];\n});\n_c = EditNewsPage;\nvar _c;\n$RefreshReg$(_c, \"EditNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx\n"));

/***/ })

});