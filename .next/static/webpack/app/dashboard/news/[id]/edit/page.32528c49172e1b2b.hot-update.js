"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ui/rich-text-editor.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/rich-text-editor.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css\");\n/* harmony import */ var _styles_rich_text_editor_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/rich-text-editor.css */ \"(app-pages-browser)/./src/styles/rich-text-editor.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Dynamically import ReactQuill to avoid SSR issues\nconst ReactQuill = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-quill_lib_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"(app-pages-browser)/./node_modules/react-quill/lib/index.js\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-[200px] w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-5/6\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n});\n_c = ReactQuill;\n// Import Quill styles\n\n\nconst RichTextEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value = \"\", onChange, placeholder = \"Start writing...\", className, error, disabled = false, minHeight = 200 } = param;\n    _s();\n    const quillRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            focus: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.focus();\n                }\n            },\n            blur: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.blur();\n                }\n            },\n            getEditor: ()=>{\n                var _quillRef_current;\n                return (_quillRef_current = quillRef.current) === null || _quillRef_current === void 0 ? void 0 : _quillRef_current.getEditor();\n            }\n        }));\n    // Quill modules configuration\n    const modules = {\n        toolbar: [\n            [\n                {\n                    \"header\": [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6,\n                        false\n                    ]\n                }\n            ],\n            [\n                {\n                    \"font\": []\n                }\n            ],\n            [\n                {\n                    \"size\": [\n                        \"small\",\n                        false,\n                        \"large\",\n                        \"huge\"\n                    ]\n                }\n            ],\n            [\n                \"bold\",\n                \"italic\",\n                \"underline\",\n                \"strike\"\n            ],\n            [\n                {\n                    \"color\": []\n                },\n                {\n                    \"background\": []\n                }\n            ],\n            [\n                {\n                    \"script\": \"sub\"\n                },\n                {\n                    \"script\": \"super\"\n                }\n            ],\n            [\n                {\n                    \"list\": \"ordered\"\n                },\n                {\n                    \"list\": \"bullet\"\n                }\n            ],\n            [\n                {\n                    \"indent\": \"-1\"\n                },\n                {\n                    \"indent\": \"+1\"\n                }\n            ],\n            [\n                {\n                    \"direction\": \"rtl\"\n                }\n            ],\n            [\n                {\n                    \"align\": []\n                }\n            ],\n            [\n                \"blockquote\",\n                \"code-block\"\n            ],\n            [\n                \"link\",\n                \"image\",\n                \"video\"\n            ],\n            [\n                \"clean\"\n            ]\n        ],\n        clipboard: {\n            matchVisual: false\n        }\n    };\n    // Quill formats\n    const formats = [\n        \"header\",\n        \"font\",\n        \"size\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"color\",\n        \"background\",\n        \"script\",\n        \"list\",\n        \"bullet\",\n        \"indent\",\n        \"direction\",\n        \"align\",\n        \"blockquote\",\n        \"code-block\",\n        \"link\",\n        \"image\",\n        \"video\"\n    ];\n    // Apply dynamic styles\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const editorElement = document.querySelector(\".ql-editor\");\n        if (editorElement) {\n            editorElement.style.minHeight = \"\".concat(minHeight, \"px\");\n        }\n    }, [\n        minHeight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"rich-text-editor\", error && \"error\", disabled && \"disabled\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQuill, {\n                    ref: quillRef,\n                    theme: \"snow\",\n                    value: value,\n                    onChange: onChange,\n                    placeholder: placeholder,\n                    modules: modules,\n                    formats: formats,\n                    readOnly: disabled\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n}, \"B5iL0NdUNZPneueEEztTCcVNiMA=\")), \"B5iL0NdUNZPneueEEztTCcVNiMA=\");\n_c2 = RichTextEditor;\nRichTextEditor.displayName = \"RichTextEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (RichTextEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ReactQuill\");\n$RefreshReg$(_c1, \"RichTextEditor$forwardRef\");\n$RefreshReg$(_c2, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\n"));

/***/ })

});