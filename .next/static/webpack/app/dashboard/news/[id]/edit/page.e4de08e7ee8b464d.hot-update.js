"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/api/app-dynamic.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* reexport default from dynamic */ _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default.a; }\n/* harmony export */ });\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/lib/app-dynamic */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\");\n/* harmony import */ var _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = function(key) { return _shared_lib_app_dynamic__WEBPACK_IMPORTED_MODULE_0__[key]; }.bind(0, __WEBPACK_IMPORT_KEY__)\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n//# sourceMappingURL=app-dynamic.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYXBpL2FwcC1keW5hbWljLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUEwQztBQUNVOztBQUVwRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2FwaS9hcHAtZHluYW1pYy5qcz9iNzUwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCIuLi9zaGFyZWQvbGliL2FwcC1keW5hbWljXCI7XG5leHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4uL3NoYXJlZC9saWIvYXBwLWR5bmFtaWNcIjtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLWR5bmFtaWMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css":
/*!******************************************************!*\
  !*** ./node_modules/react-quill/dist/quill.snow.css ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"c1314ebde954\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1xdWlsbC9kaXN0L3F1aWxsLnNub3cuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtcXVpbGwvZGlzdC9xdWlsbC5zbm93LmNzcz82ZGYxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYzEzMTRlYmRlOTU0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js":
/*!**********************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/app-dynamic.js ***!
  \**********************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return dynamic;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"(app-pages-browser)/./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"));\nconst _loadable = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./lazy-dynamic/loadable */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\"));\nfunction dynamic(dynamicOptions, options) {\n    const loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    }\n    return (0, _loadable.default)({\n        ...loadableOptions,\n        ...options\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=app-dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/app-dynamic.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js ***!
  \**********************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"BailoutToCSR\", ({\n    enumerable: true,\n    get: function() {\n        return BailoutToCSR;\n    }\n}));\nconst _bailouttocsr = __webpack_require__(/*! ./bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js\");\nfunction BailoutToCSR(param) {\n    let { reason, children } = param;\n    if (typeof window === \"undefined\") {\n        throw new _bailouttocsr.BailoutToCSRError(reason);\n    }\n    return children;\n} //# sourceMappingURL=dynamic-bailout-to-csr.js.map\n_c = BailoutToCSR;\nvar _c;\n$RefreshReg$(_c, \"BailoutToCSR\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcyIsIm1hcHBpbmdzIjoicURBRWE7QUFDYkEsOENBQTZDO0lBQ3pDRyxPQUFPO0FBQ1gsQ0FBQyxFQUFDO0FBQ0ZILGdEQUErQztJQUMzQ0ksWUFBWTtJQUNaQyxLQUFLO1FBQ0QsT0FBT0M7SUFDWDtBQUNKLENBQUMsRUFBQztBQUNGLE1BQU1DLGdCQUFnQkMsbUJBQU9BLENBQUMsZ0hBQWtCO0FBQ2hELFNBQVNGLGFBQWFHLEtBQUs7SUFDdkIsSUFBSSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHRjtJQUMzQixJQUFJLE9BQU9HLFdBQVcsYUFBYTtRQUMvQixNQUFNLElBQUlMLGNBQWNNLGlCQUFpQixDQUFDSDtJQUM5QztJQUNBLE9BQU9DO0FBQ1gsRUFFQSxrREFBa0Q7S0FSekNMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi9sYXp5LWR5bmFtaWMvZHluYW1pYy1iYWlsb3V0LXRvLWNzci5qcz9mNjkxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5cInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIkJhaWxvdXRUb0NTUlwiLCB7XG4gICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gQmFpbG91dFRvQ1NSO1xuICAgIH1cbn0pO1xuY29uc3QgX2JhaWxvdXR0b2NzciA9IHJlcXVpcmUoXCIuL2JhaWxvdXQtdG8tY3NyXCIpO1xuZnVuY3Rpb24gQmFpbG91dFRvQ1NSKHBhcmFtKSB7XG4gICAgbGV0IHsgcmVhc29uLCBjaGlsZHJlbiB9ID0gcGFyYW07XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgdGhyb3cgbmV3IF9iYWlsb3V0dG9jc3IuQmFpbG91dFRvQ1NSRXJyb3IocmVhc29uKTtcbiAgICB9XG4gICAgcmV0dXJuIGNoaWxkcmVuO1xufVxuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1keW5hbWljLWJhaWxvdXQtdG8tY3NyLmpzLm1hcCJdLCJuYW1lcyI6WyJPYmplY3QiLCJkZWZpbmVQcm9wZXJ0eSIsImV4cG9ydHMiLCJ2YWx1ZSIsImVudW1lcmFibGUiLCJnZXQiLCJCYWlsb3V0VG9DU1IiLCJfYmFpbG91dHRvY3NyIiwicmVxdWlyZSIsInBhcmFtIiwicmVhc29uIiwiY2hpbGRyZW4iLCJ3aW5kb3ciLCJCYWlsb3V0VG9DU1JFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js":
/*!********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js ***!
  \********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\nconst _react = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\nconst _dynamicbailouttocsr = __webpack_require__(/*! ./dynamic-bailout-to-csr */ \"(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js\");\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    var _mod_default;\n    return {\n        default: (_mod_default = mod == null ? void 0 : mod.default) != null ? _mod_default : mod\n    };\n}\nconst defaultOptions = {\n    loader: ()=>Promise.resolve(convertModule(()=>null)),\n    loading: null,\n    ssr: true\n};\nfunction Loadable(options) {\n    const opts = {\n        ...defaultOptions,\n        ...options\n    };\n    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));\n    const Loading = opts.loading;\n    function LoadableComponent(props) {\n        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            isLoading: true,\n            pastDelay: true,\n            error: null\n        }) : null;\n        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n            ...props\n        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {\n            reason: \"next/dynamic\",\n            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {\n                ...props\n            })\n        });\n        return /*#__PURE__*/ (0, _jsxruntime.jsx)(_react.Suspense, {\n            fallback: fallbackElement,\n            children: children\n        });\n    }\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return LoadableComponent;\n}\n_c = Loadable;\nconst _default = Loadable; //# sourceMappingURL=loadable.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/news/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/rich-text-editor */ \"(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditNewsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const newsId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: \"\",\n        publishTime: \"\",\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Data fetching\n    const { data: news, isLoading: newsLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById)(newsId);\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories)();\n    // Mutations\n    const updateNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews)();\n    // Populate form when news data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (news) {\n            const publishDate = new Date(news.publishedAt || news.createdAt);\n            setFormData({\n                title: news.title,\n                content: news.content,\n                excerpt: news.excerpt || \"\",\n                featuredImage: news.featuredImage || \"\",\n                tags: news.tags ? news.tags.join(\", \") : \"\",\n                categoryId: news.categoryId ? news.categoryId.toString() : \"\",\n                status: news.status,\n                isFeatured: news.isFeatured,\n                publishDate: publishDate.toISOString().split(\"T\")[0],\n                publishTime: publishDate.toTimeString().slice(0, 5),\n                metaTitle: news.metaTitle || \"\",\n                metaDescription: news.metaDescription || \"\"\n            });\n        }\n    }, [\n        news\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await updateNewsMutation.mutateAsync({\n                id: newsId,\n                data: submitData\n            });\n            router.push(\"/dashboard/news/\".concat(newsId));\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    if (newsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are trying to edit does not exist.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update the article information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Edit Article: \",\n                                    news.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the information below to modify the news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Excerpt\",\n                                                placeholder: \"Brief excerpt of the article\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>updateFormData(\"excerpt\", e.target.value),\n                                                error: errors.excerpt,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        value: formData.content,\n                                                        onChange: (value)=>updateFormData(\"content\", value),\n                                                        placeholder: \"Write your article content here...\",\n                                                        error: errors.content,\n                                                        minHeight: 300,\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. Use the toolbar above for formatting.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Category & Media\",\n                                    description: \"Article categorization and media assets\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Category\",\n                                                    placeholder: \"Select a category\",\n                                                    value: formData.categoryId,\n                                                    onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                                    error: errors.categoryId,\n                                                    options: categories.map((category)=>({\n                                                            value: category.id.toString(),\n                                                            label: category.name\n                                                        })),\n                                                    disabled: isLoadingCategories,\n                                                    description: \"Choose the news category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Featured Image URL\",\n                                                    placeholder: \"https://example.com/image.jpg\",\n                                                    value: formData.featuredImage,\n                                                    onChange: (e)=>updateFormData(\"featuredImage\", e.target.value),\n                                                    error: errors.featuredImage,\n                                                    description: \"Optional image to display with the article\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.status === \"published\",\n                                                    onCheckedChange: (checked)=>updateFormData(\"status\", checked ? \"published\" : \"draft\"),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isFeatured,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isFeatured\", checked),\n                                                    label: \"Featured Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"SEO Settings\",\n                                    description: \"Search engine optimization settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Meta Title\",\n                                                placeholder: \"SEO-optimized title for search engines\",\n                                                value: formData.metaTitle,\n                                                onChange: (e)=>updateFormData(\"metaTitle\", e.target.value),\n                                                error: errors.metaTitle,\n                                                description: \"Title tag for search engines (max 200 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Meta Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>updateFormData(\"metaDescription\", e.target.value),\n                                                        className: \"min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.metaDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 40\n                                        }, this),\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: [\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 30\n                                                }, this),\n                                                updateNewsMutation.isLoading ? \"Updating...\" : \"Update Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(EditNewsPage, \"l/CaPmMML2VztxFzqV5C817tgTQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews\n    ];\n});\n_c = EditNewsPage;\nvar _c;\n$RefreshReg$(_c, \"EditNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL25ld3MvW2lkXS9lZGl0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTRDO0FBQ1c7QUFDMEM7QUFDakQ7QUFDK0M7QUFDbEM7QUFDVDtBQUNhO0FBQ0Q7QUFFUztBQUMxQztBQUMrQjtBQWlCL0MsU0FBU3lCOztJQUN0QixNQUFNQyxTQUFTeEIsMERBQVNBO0lBQ3hCLE1BQU15QixTQUFTeEIsMERBQVNBO0lBQ3hCLE1BQU15QixTQUFTQyxTQUFTSCxPQUFPSSxFQUFFO0lBQ2pDLE1BQU0sQ0FBQ0MsVUFBVUMsWUFBWSxHQUFHaEMsK0NBQVFBLENBQWU7UUFDckRpQyxPQUFPO1FBQ1BDLFNBQVM7UUFDVEMsU0FBUztRQUNUQyxlQUFlO1FBQ2ZDLE1BQU07UUFDTkMsWUFBWTtRQUNaQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsYUFBYTtRQUNiQyxhQUFhO1FBQ2JDLFdBQVc7UUFDWEMsaUJBQWlCO0lBQ25CO0lBRUEsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUc5QywrQ0FBUUEsQ0FBd0IsQ0FBQztJQUM3RCxnQkFBZ0I7SUFDaEIsTUFBTSxFQUFFK0MsTUFBTUMsSUFBSSxFQUFFQyxXQUFXQyxXQUFXLEVBQUVDLEtBQUssRUFBRSxHQUFHbkMsK0RBQVdBLENBQUNZO0lBQ2xFLE1BQU0sRUFBRW1CLE1BQU1LLGFBQWEsRUFBRSxFQUFFSCxXQUFXSSxtQkFBbUIsRUFBRSxHQUFHbkMsNkVBQW1CQTtJQUVyRixZQUFZO0lBQ1osTUFBTW9DLHFCQUFxQnJDLGlFQUFhQTtJQUV4QyxxQ0FBcUM7SUFDckNoQixnREFBU0EsQ0FBQztRQUNSLElBQUkrQyxNQUFNO1lBQ1IsTUFBTVAsY0FBYyxJQUFJYyxLQUFLUCxLQUFLUSxXQUFXLElBQUlSLEtBQUtTLFNBQVM7WUFDL0R6QixZQUFZO2dCQUNWQyxPQUFPZSxLQUFLZixLQUFLO2dCQUNqQkMsU0FBU2MsS0FBS2QsT0FBTztnQkFDckJDLFNBQVNhLEtBQUtiLE9BQU8sSUFBSTtnQkFDekJDLGVBQWVZLEtBQUtaLGFBQWEsSUFBSTtnQkFDckNDLE1BQU1XLEtBQUtYLElBQUksR0FBR1csS0FBS1gsSUFBSSxDQUFDcUIsSUFBSSxDQUFDLFFBQVE7Z0JBQ3pDcEIsWUFBWVUsS0FBS1YsVUFBVSxHQUFHVSxLQUFLVixVQUFVLENBQUNxQixRQUFRLEtBQUs7Z0JBQzNEcEIsUUFBUVMsS0FBS1QsTUFBTTtnQkFDbkJDLFlBQVlRLEtBQUtSLFVBQVU7Z0JBQzNCQyxhQUFhQSxZQUFZbUIsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7Z0JBQ3BEbkIsYUFBYUQsWUFBWXFCLFlBQVksR0FBR0MsS0FBSyxDQUFDLEdBQUc7Z0JBQ2pEcEIsV0FBV0ssS0FBS0wsU0FBUyxJQUFJO2dCQUM3QkMsaUJBQWlCSSxLQUFLSixlQUFlLElBQUk7WUFDM0M7UUFDRjtJQUNGLEdBQUc7UUFBQ0k7S0FBSztJQUVULE1BQU1nQixlQUFlO1FBQ25CLE1BQU1DLFlBQW1DLENBQUM7UUFFMUMsSUFBSSxDQUFDbEMsU0FBU0UsS0FBSyxDQUFDaUMsSUFBSSxJQUFJRCxVQUFVaEMsS0FBSyxHQUFHO1FBQzlDLElBQUksQ0FBQ0YsU0FBU0csT0FBTyxDQUFDZ0MsSUFBSSxJQUFJRCxVQUFVL0IsT0FBTyxHQUFHO1FBQ2xELElBQUksQ0FBQ0gsU0FBU1UsV0FBVyxFQUFFd0IsVUFBVXhCLFdBQVcsR0FBRztRQUNuRCxJQUFJLENBQUNWLFNBQVNXLFdBQVcsRUFBRXVCLFVBQVV2QixXQUFXLEdBQUc7UUFFbkRJLFVBQVVtQjtRQUNWLE9BQU9FLE9BQU9DLElBQUksQ0FBQ0gsV0FBV0ksTUFBTSxLQUFLO0lBQzNDO0lBRUEsTUFBTUMsZUFBZSxPQUFPQztRQUMxQkEsRUFBRUMsY0FBYztRQUVoQixJQUFJLENBQUNSLGdCQUFnQjtZQUNuQnpDLDBDQUFLQSxDQUFDNEIsS0FBSyxDQUFDO1lBQ1o7UUFDRjtRQUVBLHdCQUF3QjtRQUN4QixNQUFNc0Isa0JBQWtCLElBQUlsQixLQUFLLEdBQTJCeEIsT0FBeEJBLFNBQVNVLFdBQVcsRUFBQyxLQUF3QixPQUFyQlYsU0FBU1csV0FBVztRQUVoRixhQUFhO1FBQ2IsTUFBTWdDLFlBQVkzQyxTQUFTTSxJQUFJLENBQzVCd0IsS0FBSyxDQUFDLEtBQ05jLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSVYsSUFBSSxJQUNuQlcsTUFBTSxDQUFDRCxDQUFBQSxNQUFPQSxJQUFJUCxNQUFNLEdBQUc7UUFFOUIsTUFBTVMsYUFBNkI7WUFDakM3QyxPQUFPRixTQUFTRSxLQUFLO1lBQ3JCQyxTQUFTSCxTQUFTRyxPQUFPO1lBQ3pCQyxTQUFTSixTQUFTSSxPQUFPLElBQUk0QztZQUM3QjNDLGVBQWVMLFNBQVNLLGFBQWEsSUFBSTJDO1lBQ3pDMUMsTUFBTXFDLFVBQVVMLE1BQU0sR0FBRyxJQUFJSyxZQUFZSztZQUN6Q3pDLFlBQVlQLFNBQVNPLFVBQVUsR0FBR1QsU0FBU0UsU0FBU08sVUFBVSxJQUFJeUM7WUFDbEV4QyxRQUFRUixTQUFTUSxNQUFNO1lBQ3ZCaUIsYUFBYXpCLFNBQVNRLE1BQU0sS0FBSyxjQUFja0MsZ0JBQWdCYixXQUFXLEtBQUttQjtZQUMvRXZDLFlBQVlULFNBQVNTLFVBQVU7WUFDL0JHLFdBQVdaLFNBQVNZLFNBQVMsSUFBSW9DO1lBQ2pDbkMsaUJBQWlCYixTQUFTYSxlQUFlLElBQUltQztRQUMvQztRQUVBLElBQUk7WUFDRixNQUFNekIsbUJBQW1CMEIsV0FBVyxDQUFDO2dCQUFFbEQsSUFBSUY7Z0JBQVFtQixNQUFNK0I7WUFBVztZQUNwRW5ELE9BQU9zRCxJQUFJLENBQUMsbUJBQTBCLE9BQVByRDtRQUNqQyxFQUFFLE9BQU91QixPQUFPO1FBQ2QsNEJBQTRCO1FBQzlCO0lBQ0Y7SUFFQSxNQUFNK0IsaUJBQWlCLENBQUNDLE9BQTJCQztRQUNqRHBELFlBQVlxRCxDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsQ0FBQ0YsTUFBTSxFQUFFQztZQUFNO1FBQy9DLHNDQUFzQztRQUN0QyxJQUFJdkMsTUFBTSxDQUFDc0MsTUFBTSxFQUFFO1lBQ2pCckMsVUFBVXVDLENBQUFBLE9BQVM7b0JBQUUsR0FBR0EsSUFBSTtvQkFBRSxDQUFDRixNQUFNLEVBQUVKO2dCQUFVO1FBQ25EO0lBQ0Y7SUFFQSxJQUFJN0IsYUFBYTtRQUNmLHFCQUNFLDhEQUFDb0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ3hFLDZEQUFRQTs0QkFBQ3dFLFdBQVU7Ozs7OztzQ0FDcEIsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ3hFLDZEQUFRQTtvQ0FBQ3dFLFdBQVU7Ozs7Ozs4Q0FDcEIsOERBQUN4RSw2REFBUUE7b0NBQUN3RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR3hCLDhEQUFDbkYscURBQUlBOztzQ0FDSCw4REFBQ0csMkRBQVVBOzs4Q0FDVCw4REFBQ1EsNkRBQVFBO29DQUFDd0UsV0FBVTs7Ozs7OzhDQUNwQiw4REFBQ3hFLDZEQUFRQTtvQ0FBQ3dFLFdBQVU7Ozs7Ozs7Ozs7OztzQ0FFdEIsOERBQUNsRiw0REFBV0E7NEJBQUNrRixXQUFVOzs4Q0FDckIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3hFLDZEQUFRQTs0Q0FBQ3dFLFdBQVU7Ozs7OztzREFDcEIsOERBQUN4RSw2REFBUUE7NENBQUN3RSxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDeEUsNkRBQVFBOzRDQUFDd0UsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQ3hFLDZEQUFRQTs0Q0FBQ3dFLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFdEIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ3hFLDZEQUFRQTs0Q0FBQ3dFLFdBQVU7Ozs7OztzREFDcEIsOERBQUN4RSw2REFBUUE7NENBQUN3RSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNaEM7SUFFQSxJQUFJcEMsU0FBUyxDQUFDSCxNQUFNO1FBQ2xCLHFCQUNFLDhEQUFDc0M7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDOUUseURBQU1BO3dCQUFDK0UsU0FBUTt3QkFBVUMsU0FBUyxJQUFNOUQsT0FBTytELElBQUk7OzBDQUNsRCw4REFBQ3ZFLG1IQUFTQTtnQ0FBQ29FLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7Ozs7Ozs4QkFJMUMsOERBQUNuRixxREFBSUE7OEJBQ0gsNEVBQUNDLDREQUFXQTt3QkFBQ2tGLFdBQVU7a0NBQ3JCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNqRSxtSEFBYUE7b0NBQUNpRSxXQUFVOzs7Ozs7OENBQ3pCLDhEQUFDSTtvQ0FBR0osV0FBVTs4Q0FDWHBDLFFBQVEsd0JBQXdCOzs7Ozs7Z0NBQzlCOzhDQUFjLDhEQUFDeUM7b0NBQUVMLFdBQVU7OENBQzdCLENBQUNwQyxrQkFBQUEsNEJBQUQsTUFBa0IwQyxPQUFPLEtBQUk7Ozs7Ozs4Q0FFaEMsOERBQUNwRix5REFBTUE7b0NBQUNnRixTQUFTLElBQU05RCxPQUFPc0QsSUFBSSxDQUFDOzhDQUFvQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQVFuRTtJQUVBLHFCQUNFLDhEQUFDSztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDOUUseURBQU1BO3dCQUFDK0UsU0FBUTt3QkFBVUMsU0FBUyxJQUFNOUQsT0FBTytELElBQUk7OzBDQUNsRCw4REFBQ3ZFLG1IQUFTQTtnQ0FBQ29FLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBR3hDLDhEQUFDRDs7MENBQ0MsOERBQUNRO2dDQUFHUCxXQUFVOztrREFDWiw4REFBQ2xFLG1IQUFTQTt3Q0FBQ2tFLFdBQVU7Ozs7OztvQ0FBK0I7Ozs7Ozs7MENBR3RELDhEQUFDSztnQ0FBRUwsV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFLdEMsOERBQUNuRixxREFBSUE7O2tDQUNILDhEQUFDRywyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7O29DQUFDO29DQUFld0MsS0FBS2YsS0FBSzs7Ozs7OzswQ0FDcEMsOERBQUMzQixnRUFBZUE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FJbkIsOERBQUNELDREQUFXQTtrQ0FDViw0RUFBQzBGOzRCQUFLQyxVQUFVMUI7NEJBQWNpQixXQUFVOzs4Q0FDdEMsOERBQUMzRSxrRUFBV0E7b0NBQUNxQixPQUFNO29DQUFvQmdFLGFBQVk7OENBQ2pELDRFQUFDWDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUM3RSxpRUFBVUE7Z0RBQ1R3RixPQUFNO2dEQUNOQyxhQUFZO2dEQUNaQyxRQUFRO2dEQUNSaEIsT0FBT3JELFNBQVNFLEtBQUs7Z0RBQ3JCb0UsVUFBVSxDQUFDOUIsSUFBTVcsZUFBZSxTQUFTWCxFQUFFK0IsTUFBTSxDQUFDbEIsS0FBSztnREFDdkRqQyxPQUFPTixPQUFPWixLQUFLO2dEQUNuQmdFLGFBQVk7Ozs7OzswREFHZCw4REFBQ3ZGLGlFQUFVQTtnREFDVHdGLE9BQU07Z0RBQ05DLGFBQVk7Z0RBQ1pmLE9BQU9yRCxTQUFTSSxPQUFPO2dEQUN2QmtFLFVBQVUsQ0FBQzlCLElBQU1XLGVBQWUsV0FBV1gsRUFBRStCLE1BQU0sQ0FBQ2xCLEtBQUs7Z0RBQ3pEakMsT0FBT04sT0FBT1YsT0FBTztnREFDckI4RCxhQUFZOzs7Ozs7MERBR2QsOERBQUNYO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ1c7d0RBQU1YLFdBQVU7a0VBQW9DOzs7Ozs7a0VBQ3JELDhEQUFDL0Qsd0VBQWNBO3dEQUNiNEQsT0FBT3JELFNBQVNHLE9BQU87d0RBQ3ZCbUUsVUFBVSxDQUFDakIsUUFBVUYsZUFBZSxXQUFXRTt3REFDL0NlLGFBQVk7d0RBQ1poRCxPQUFPTixPQUFPWCxPQUFPO3dEQUNyQnFFLFdBQVc7d0RBQ1hoQixXQUFVOzs7Ozs7a0VBRVosOERBQUNLO3dEQUFFTCxXQUFVO2tFQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Z0NBSzdCOzhDQUFZLDhEQUFDM0Usa0VBQVdBO29DQUFDcUIsT0FBTTtvQ0FBbUJnRSxhQUFZOztzREFDMUUsOERBQUNYOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQzVFLGtFQUFXQTtvREFDVnVGLE9BQU07b0RBQ05DLGFBQVk7b0RBQ1pmLE9BQU9yRCxTQUFTTyxVQUFVO29EQUMxQmtFLGVBQWUsQ0FBQ3BCLFFBQVVGLGVBQWUsY0FBY0U7b0RBQ3ZEakMsT0FBT04sT0FBT1AsVUFBVTtvREFDeEJtRSxTQUFTckQsV0FBV3VCLEdBQUcsQ0FBQyxDQUFDK0IsV0FBbUI7NERBQzFDdEIsT0FBT3NCLFNBQVM1RSxFQUFFLENBQUM2QixRQUFROzREQUMzQnVDLE9BQU9RLFNBQVNDLElBQUk7d0RBQ3RCO29EQUNBQyxVQUFVdkQ7b0RBQ1Y0QyxhQUFZOzs7Ozs7OERBR2QsOERBQUN2RixpRUFBVUE7b0RBQ1R3RixPQUFNO29EQUNOQyxhQUFZO29EQUNaZixPQUFPckQsU0FBU0ssYUFBYTtvREFDN0JpRSxVQUFVLENBQUM5QixJQUFNVyxlQUFlLGlCQUFpQlgsRUFBRStCLE1BQU0sQ0FBQ2xCLEtBQUs7b0RBQy9EakMsT0FBT04sT0FBT1QsYUFBYTtvREFDM0I2RCxhQUFZOzs7Ozs7Ozs7Ozs7c0RBSWhCLDhEQUFDdkYsaUVBQVVBOzRDQUNUd0YsT0FBTTs0Q0FDTkMsYUFBWTs0Q0FDWmYsT0FBT3JELFNBQVNNLElBQUk7NENBQ3BCZ0UsVUFBVSxDQUFDOUIsSUFBTVcsZUFBZSxRQUFRWCxFQUFFK0IsTUFBTSxDQUFDbEIsS0FBSzs0Q0FDdERhLGFBQVk7Ozs7Ozs7Ozs7Ozs4Q0FJaEIsOERBQUNyRixrRUFBV0E7b0NBQUNxQixPQUFNO29DQUFzQmdFLGFBQVk7O3NEQUNuRCw4REFBQ1g7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDN0UsaUVBQVVBO29EQUNUd0YsT0FBTTtvREFDTlcsTUFBSztvREFDTFQsUUFBUTtvREFDUmhCLE9BQU9yRCxTQUFTVSxXQUFXO29EQUMzQjRELFVBQVUsQ0FBQzlCLElBQU1XLGVBQWUsZUFBZVgsRUFBRStCLE1BQU0sQ0FBQ2xCLEtBQUs7b0RBQzdEakMsT0FBT04sT0FBT0osV0FBVzs7Ozs7OzhEQUczQiw4REFBQy9CLGlFQUFVQTtvREFDVHdGLE9BQU07b0RBQ05XLE1BQUs7b0RBQ0xULFFBQVE7b0RBQ1JoQixPQUFPckQsU0FBU1csV0FBVztvREFDM0IyRCxVQUFVLENBQUM5QixJQUFNVyxlQUFlLGVBQWVYLEVBQUUrQixNQUFNLENBQUNsQixLQUFLO29EQUM3RGpDLE9BQU9OLE9BQU9ILFdBQVc7Ozs7Ozs7Ozs7OztzREFJN0IsOERBQUM0Qzs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUN6RSxzRUFBWUE7b0RBQ1hnRyxTQUFTL0UsU0FBU1EsTUFBTSxLQUFLO29EQUM3QndFLGlCQUFpQixDQUFDRCxVQUFZNUIsZUFBZSxVQUFVNEIsVUFBVSxjQUFjO29EQUMvRVosT0FBTTtvREFDTkQsYUFBWTs7Ozs7OzhEQUdkLDhEQUFDbkYsc0VBQVlBO29EQUNYZ0csU0FBUy9FLFNBQVNTLFVBQVU7b0RBQzVCdUUsaUJBQWlCLENBQUNELFVBQVk1QixlQUFlLGNBQWM0QjtvREFDM0RaLE9BQU07b0RBQ05ELGFBQVk7b0RBQ1pULFNBQVE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FLZCw4REFBQzVFLGtFQUFXQTtvQ0FBQ3FCLE9BQU07b0NBQWVnRSxhQUFZOzhDQUM1Qyw0RUFBQ1g7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDN0UsaUVBQVVBO2dEQUNUd0YsT0FBTTtnREFDTkMsYUFBWTtnREFDWmYsT0FBT3JELFNBQVNZLFNBQVM7Z0RBQ3pCMEQsVUFBVSxDQUFDOUIsSUFBTVcsZUFBZSxhQUFhWCxFQUFFK0IsTUFBTSxDQUFDbEIsS0FBSztnREFDM0RqQyxPQUFPTixPQUFPRixTQUFTO2dEQUN2QnNELGFBQVk7Ozs7OzswREFHZCw4REFBQ1g7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDVzt3REFBTVgsV0FBVTtrRUFBb0M7Ozs7OztrRUFDckQsOERBQUN5Qjt3REFDQ2IsYUFBWTt3REFDWmYsT0FBT3JELFNBQVNhLGVBQWU7d0RBQy9CeUQsVUFBVSxDQUFDOUIsSUFBTVcsZUFBZSxtQkFBbUJYLEVBQUUrQixNQUFNLENBQUNsQixLQUFLO3dEQUNqRUcsV0FBVTs7Ozs7O29EQUVYMUMsT0FBT0QsZUFBZSxrQkFDckIsOERBQUNnRDt3REFBRUwsV0FBVTtrRUFBd0IxQyxPQUFPRCxlQUFlOzs7Ozs7a0VBRTdELDhEQUFDZ0Q7d0RBQUVMLFdBQVU7a0VBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FPM0MsOERBQUMxRSxrRUFBV0E7O3dDQUFDO3NEQUFjLDhEQUFDSix5REFBTUE7NENBQ2hDb0csTUFBSzs0Q0FDTHJCLFNBQVE7NENBQ1JDLFNBQVMsSUFBTTlELE9BQU8rRCxJQUFJOzRDQUMxQmtCLFVBQVV0RCxtQkFBbUJMLFNBQVM7c0RBQ3ZDOzs7Ozs7d0NBRVE7c0RBQWMsOERBQUN4Qyx5REFBTUE7NENBQzVCb0csTUFBSzs0Q0FDTEQsVUFBVXRELG1CQUFtQkwsU0FBUzs7Z0RBQ3ZDOzhEQUFnQiw4REFBQzdCLG1IQUFJQTtvREFBQ21FLFdBQVU7Ozs7OztnREFDNUJqQyxtQkFBbUJMLFNBQVMsR0FBRyxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFoRTtHQWhXd0J4Qjs7UUFDUHZCLHNEQUFTQTtRQUNUQyxzREFBU0E7UUFtQjhCYSwyREFBV0E7UUFDQ0UseUVBQW1CQTtRQUcxREQsNkRBQWFBOzs7S0F6QmxCUSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2Rhc2hib2FyZC9uZXdzL1tpZF0vZWRpdC9wYWdlLnRzeD80NGYwIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZVBhcmFtcywgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xyXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcclxuaW1wb3J0IHsgSW5wdXRGaWVsZCwgU2VsZWN0RmllbGQsIEZvcm1TZWN0aW9uLCBGb3JtQWN0aW9ucyB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9mb3JtLWZpZWxkJztcclxuaW1wb3J0IHsgVG9nZ2xlU3dpdGNoIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RvZ2dsZS1zd2l0Y2gnO1xyXG5pbXBvcnQgeyBTa2VsZXRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9za2VsZXRvbic7XHJcbmltcG9ydCB7IHVzZU5ld3NCeUlkLCB1c2VVcGRhdGVOZXdzIH0gZnJvbSAnQC9saWIvaG9va3MvdXNlTmV3cyc7XHJcbmltcG9ydCB7IHVzZVB1YmxpY0NhdGVnb3JpZXMgfSBmcm9tICdAL2xpYi9ob29rcy91c2VDYXRlZ29yaWVzJztcclxuaW1wb3J0IHsgVXBkYXRlTmV3c0RhdGEgfSBmcm9tICdAL2xpYi9hcGkvbmV3cyc7XHJcbmltcG9ydCB7IEFycm93TGVmdCwgU2F2ZSwgTmV3c3BhcGVyLCBBbGVydFRyaWFuZ2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdzb25uZXInO1xyXG5pbXBvcnQgUmljaFRleHRFZGl0b3IgZnJvbSAnQC9jb21wb25lbnRzL3VpL3JpY2gtdGV4dC1lZGl0b3InO1xyXG5cclxuaW50ZXJmYWNlIE5ld3NGb3JtRGF0YSB7XHJcbiAgdGl0bGU6IHN0cmluZztcclxuICBjb250ZW50OiBzdHJpbmc7XHJcbiAgZXhjZXJwdDogc3RyaW5nO1xyXG4gIGZlYXR1cmVkSW1hZ2U6IHN0cmluZztcclxuICB0YWdzOiBzdHJpbmc7XHJcbiAgY2F0ZWdvcnlJZDogc3RyaW5nO1xyXG4gIHN0YXR1czogJ2RyYWZ0JyB8ICdwdWJsaXNoZWQnIHwgJ2FyY2hpdmVkJztcclxuICBpc0ZlYXR1cmVkOiBib29sZWFuO1xyXG4gIHB1Ymxpc2hEYXRlOiBzdHJpbmc7XHJcbiAgcHVibGlzaFRpbWU6IHN0cmluZztcclxuICBtZXRhVGl0bGU6IHN0cmluZztcclxuICBtZXRhRGVzY3JpcHRpb246IHN0cmluZztcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRWRpdE5ld3NQYWdlKCkge1xyXG4gIGNvbnN0IHBhcmFtcyA9IHVzZVBhcmFtcygpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IG5ld3NJZCA9IHBhcnNlSW50KHBhcmFtcy5pZCBhcyBzdHJpbmcpO1xyXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8TmV3c0Zvcm1EYXRhPih7XHJcbiAgICB0aXRsZTogJycsXHJcbiAgICBjb250ZW50OiAnJyxcclxuICAgIGV4Y2VycHQ6ICcnLFxyXG4gICAgZmVhdHVyZWRJbWFnZTogJycsXHJcbiAgICB0YWdzOiAnJyxcclxuICAgIGNhdGVnb3J5SWQ6ICcnLFxyXG4gICAgc3RhdHVzOiAnZHJhZnQnLFxyXG4gICAgaXNGZWF0dXJlZDogZmFsc2UsXHJcbiAgICBwdWJsaXNoRGF0ZTogJycsXHJcbiAgICBwdWJsaXNoVGltZTogJycsXHJcbiAgICBtZXRhVGl0bGU6ICcnLFxyXG4gICAgbWV0YURlc2NyaXB0aW9uOiAnJyxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgW2Vycm9ycywgc2V0RXJyb3JzXSA9IHVzZVN0YXRlPFBhcnRpYWw8TmV3c0Zvcm1EYXRhPj4oe30pO1xyXG4gIC8vIERhdGEgZmV0Y2hpbmdcclxuICBjb25zdCB7IGRhdGE6IG5ld3MsIGlzTG9hZGluZzogbmV3c0xvYWRpbmcsIGVycm9yIH0gPSB1c2VOZXdzQnlJZChuZXdzSWQpO1xyXG4gIGNvbnN0IHsgZGF0YTogY2F0ZWdvcmllcyA9IFtdLCBpc0xvYWRpbmc6IGlzTG9hZGluZ0NhdGVnb3JpZXMgfSA9IHVzZVB1YmxpY0NhdGVnb3JpZXMoKTtcclxuXHJcbiAgLy8gTXV0YXRpb25zXHJcbiAgY29uc3QgdXBkYXRlTmV3c011dGF0aW9uID0gdXNlVXBkYXRlTmV3cygpO1xyXG5cclxuICAvLyBQb3B1bGF0ZSBmb3JtIHdoZW4gbmV3cyBkYXRhIGxvYWRzXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChuZXdzKSB7XHJcbiAgICAgIGNvbnN0IHB1Ymxpc2hEYXRlID0gbmV3IERhdGUobmV3cy5wdWJsaXNoZWRBdCB8fCBuZXdzLmNyZWF0ZWRBdCk7XHJcbiAgICAgIHNldEZvcm1EYXRhKHtcclxuICAgICAgICB0aXRsZTogbmV3cy50aXRsZSxcclxuICAgICAgICBjb250ZW50OiBuZXdzLmNvbnRlbnQsXHJcbiAgICAgICAgZXhjZXJwdDogbmV3cy5leGNlcnB0IHx8ICcnLFxyXG4gICAgICAgIGZlYXR1cmVkSW1hZ2U6IG5ld3MuZmVhdHVyZWRJbWFnZSB8fCAnJyxcclxuICAgICAgICB0YWdzOiBuZXdzLnRhZ3MgPyBuZXdzLnRhZ3Muam9pbignLCAnKSA6ICcnLFxyXG4gICAgICAgIGNhdGVnb3J5SWQ6IG5ld3MuY2F0ZWdvcnlJZCA/IG5ld3MuY2F0ZWdvcnlJZC50b1N0cmluZygpIDogJycsXHJcbiAgICAgICAgc3RhdHVzOiBuZXdzLnN0YXR1cyxcclxuICAgICAgICBpc0ZlYXR1cmVkOiBuZXdzLmlzRmVhdHVyZWQsXHJcbiAgICAgICAgcHVibGlzaERhdGU6IHB1Ymxpc2hEYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcclxuICAgICAgICBwdWJsaXNoVGltZTogcHVibGlzaERhdGUudG9UaW1lU3RyaW5nKCkuc2xpY2UoMCwgNSksXHJcbiAgICAgICAgbWV0YVRpdGxlOiBuZXdzLm1ldGFUaXRsZSB8fCAnJyxcclxuICAgICAgICBtZXRhRGVzY3JpcHRpb246IG5ld3MubWV0YURlc2NyaXB0aW9uIHx8ICcnLFxyXG4gICAgICB9KTtcclxuICAgIH1cclxuICB9LCBbbmV3c10pO1xyXG5cclxuICBjb25zdCB2YWxpZGF0ZUZvcm0gPSAoKTogYm9vbGVhbiA9PiB7XHJcbiAgICBjb25zdCBuZXdFcnJvcnM6IFBhcnRpYWw8TmV3c0Zvcm1EYXRhPiA9IHt9O1xyXG5cclxuICAgIGlmICghZm9ybURhdGEudGl0bGUudHJpbSgpKSBuZXdFcnJvcnMudGl0bGUgPSAnVGl0bGUgaXMgcmVxdWlyZWQnO1xyXG4gICAgaWYgKCFmb3JtRGF0YS5jb250ZW50LnRyaW0oKSkgbmV3RXJyb3JzLmNvbnRlbnQgPSAnQ29udGVudCBpcyByZXF1aXJlZCc7XHJcbiAgICBpZiAoIWZvcm1EYXRhLnB1Ymxpc2hEYXRlKSBuZXdFcnJvcnMucHVibGlzaERhdGUgPSAnUHVibGlzaCBkYXRlIGlzIHJlcXVpcmVkJztcclxuICAgIGlmICghZm9ybURhdGEucHVibGlzaFRpbWUpIG5ld0Vycm9ycy5wdWJsaXNoVGltZSA9ICdQdWJsaXNoIHRpbWUgaXMgcmVxdWlyZWQnO1xyXG5cclxuICAgIHNldEVycm9ycyhuZXdFcnJvcnMpO1xyXG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jIChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcclxuICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuXHJcbiAgICBpZiAoIXZhbGlkYXRlRm9ybSgpKSB7XHJcbiAgICAgIHRvYXN0LmVycm9yKCdQbGVhc2UgZml4IHRoZSBmb3JtIGVycm9ycycpO1xyXG4gICAgICByZXR1cm47XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ29tYmluZSBkYXRlIGFuZCB0aW1lXHJcbiAgICBjb25zdCBwdWJsaXNoRGF0ZVRpbWUgPSBuZXcgRGF0ZShgJHtmb3JtRGF0YS5wdWJsaXNoRGF0ZX1UJHtmb3JtRGF0YS5wdWJsaXNoVGltZX1gKTtcclxuXHJcbiAgICAvLyBQYXJzZSB0YWdzXHJcbiAgICBjb25zdCB0YWdzQXJyYXkgPSBmb3JtRGF0YS50YWdzXHJcbiAgICAgIC5zcGxpdCgnLCcpXHJcbiAgICAgIC5tYXAodGFnID0+IHRhZy50cmltKCkpXHJcbiAgICAgIC5maWx0ZXIodGFnID0+IHRhZy5sZW5ndGggPiAwKTtcclxuXHJcbiAgICBjb25zdCBzdWJtaXREYXRhOiBVcGRhdGVOZXdzRGF0YSA9IHtcclxuICAgICAgdGl0bGU6IGZvcm1EYXRhLnRpdGxlLFxyXG4gICAgICBjb250ZW50OiBmb3JtRGF0YS5jb250ZW50LFxyXG4gICAgICBleGNlcnB0OiBmb3JtRGF0YS5leGNlcnB0IHx8IHVuZGVmaW5lZCxcclxuICAgICAgZmVhdHVyZWRJbWFnZTogZm9ybURhdGEuZmVhdHVyZWRJbWFnZSB8fCB1bmRlZmluZWQsXHJcbiAgICAgIHRhZ3M6IHRhZ3NBcnJheS5sZW5ndGggPiAwID8gdGFnc0FycmF5IDogdW5kZWZpbmVkLFxyXG4gICAgICBjYXRlZ29yeUlkOiBmb3JtRGF0YS5jYXRlZ29yeUlkID8gcGFyc2VJbnQoZm9ybURhdGEuY2F0ZWdvcnlJZCkgOiB1bmRlZmluZWQsXHJcbiAgICAgIHN0YXR1czogZm9ybURhdGEuc3RhdHVzLFxyXG4gICAgICBwdWJsaXNoZWRBdDogZm9ybURhdGEuc3RhdHVzID09PSAncHVibGlzaGVkJyA/IHB1Ymxpc2hEYXRlVGltZS50b0lTT1N0cmluZygpIDogdW5kZWZpbmVkLFxyXG4gICAgICBpc0ZlYXR1cmVkOiBmb3JtRGF0YS5pc0ZlYXR1cmVkLFxyXG4gICAgICBtZXRhVGl0bGU6IGZvcm1EYXRhLm1ldGFUaXRsZSB8fCB1bmRlZmluZWQsXHJcbiAgICAgIG1ldGFEZXNjcmlwdGlvbjogZm9ybURhdGEubWV0YURlc2NyaXB0aW9uIHx8IHVuZGVmaW5lZCxcclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgYXdhaXQgdXBkYXRlTmV3c011dGF0aW9uLm11dGF0ZUFzeW5jKHsgaWQ6IG5ld3NJZCwgZGF0YTogc3VibWl0RGF0YSB9KTtcclxuICAgICAgcm91dGVyLnB1c2goYC9kYXNoYm9hcmQvbmV3cy8ke25ld3NJZH1gKTtcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIC8vIEVycm9yIGhhbmRsZWQgYnkgbXV0YXRpb25cclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCB1cGRhdGVGb3JtRGF0YSA9IChmaWVsZDoga2V5b2YgTmV3c0Zvcm1EYXRhLCB2YWx1ZTogc3RyaW5nIHwgYm9vbGVhbikgPT4ge1xyXG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoeyAuLi5wcmV2LCBbZmllbGRdOiB2YWx1ZSB9KSk7XHJcbiAgICAvLyBDbGVhciBlcnJvciB3aGVuIHVzZXIgc3RhcnRzIHR5cGluZ1xyXG4gICAgaWYgKGVycm9yc1tmaWVsZF0pIHtcclxuICAgICAgc2V0RXJyb3JzKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogdW5kZWZpbmVkIH0pKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBpZiAobmV3c0xvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTEwIHctMjBcIiAvPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtOCB3LTY0XCIgLz5cclxuICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtNCB3LTQ4XCIgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgPENhcmRIZWFkZXI+XHJcbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTYgdy00OFwiIC8+XHJcbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy02NFwiIC8+XHJcbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XHJcbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtNCB3LTMyXCIgLz5cclxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMFwiIC8+XHJcbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxyXG4gICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTMyXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTNcIj5cclxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTIwXCIgLz5cclxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTMyXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgIDwvQ2FyZD5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKGVycm9yIHx8ICFuZXdzKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XHJcbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX0+XHJcbiAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgQmFja1xyXG4gICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPENhcmQ+XHJcbiAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwicC02XCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8QWxlcnRUcmlhbmdsZSBjbGFzc05hbWU9XCJteC1hdXRvIGgtMTIgdy0xMiB0ZXh0LXJlZC01MDAgbWItNFwiIC8+XHJcbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1yZWQtNjAwIG1iLTJcIj5cclxuICAgICAgICAgICAgICAgIHtlcnJvciA/ICdGYWlsZWQgdG8gbG9hZCBuZXdzJyA6ICdOZXdzIG5vdCBmb3VuZCd9XHJcbiAgICAgICAgICAgICAgPC9oMz4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbWItNFwiPlxyXG4gICAgICAgICAgICAgICAgeyhlcnJvciBhcyBFcnJvcik/Lm1lc3NhZ2UgfHwgJ1RoZSBuZXdzIGFydGljbGUgeW91IGFyZSB0cnlpbmcgdG8gZWRpdCBkb2VzIG5vdCBleGlzdC4nfVxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkL25ld3MnKX0+XHJcbiAgICAgICAgICAgICAgICBSZXR1cm4gdG8gTmV3c1xyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgICAgPC9DYXJkPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgey8qIEhlYWRlciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cclxuICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17KCkgPT4gcm91dGVyLmJhY2soKX0+XHJcbiAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XHJcbiAgICAgICAgICBCYWNrXHJcbiAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgPGRpdj5cclxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBmbGV4IGl0ZW1zLWNlbnRlclwiPlxyXG4gICAgICAgICAgICA8TmV3c3BhcGVyIGNsYXNzTmFtZT1cIm1yLTMgaC04IHctOCB0ZXh0LWJsdWUtNjAwXCIgLz5cclxuICAgICAgICAgICAgRWRpdCBOZXdzIEFydGljbGVcclxuICAgICAgICAgIDwvaDE+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG10LTFcIj5VcGRhdGUgdGhlIGFydGljbGUgaW5mb3JtYXRpb248L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgey8qIEZvcm0gKi99XHJcbiAgICAgIDxDYXJkPlxyXG4gICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgPENhcmRUaXRsZT5FZGl0IEFydGljbGU6IHtuZXdzLnRpdGxlfTwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cclxuICAgICAgICAgICAgVXBkYXRlIHRoZSBpbmZvcm1hdGlvbiBiZWxvdyB0byBtb2RpZnkgdGhlIG5ld3MgYXJ0aWNsZVxyXG4gICAgICAgICAgPC9DYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgIDxDYXJkQ29udGVudD5cclxuICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXR9IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICA8Rm9ybVNlY3Rpb24gdGl0bGU9XCJCYXNpYyBJbmZvcm1hdGlvblwiIGRlc2NyaXB0aW9uPVwiRXNzZW50aWFsIGFydGljbGUgZGV0YWlsc1wiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XHJcbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlRpdGxlICpcIlxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkVudGVyIGFydGljbGUgdGl0bGVcIlxyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3RpdGxlJywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj1cIlRoZSBtYWluIGhlYWRsaW5lIG9mIHlvdXIgbmV3cyBhcnRpY2xlXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFeGNlcnB0XCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJCcmllZiBleGNlcnB0IG9mIHRoZSBhcnRpY2xlXCJcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmV4Y2VycHR9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2V4Y2VycHQnLCBlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuZXhjZXJwdH1cclxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJBIHNob3J0IGRlc2NyaXB0aW9uIHRoYXQgYXBwZWFycyBpbiBwcmV2aWV3cyBhbmQgbGlzdHNcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxyXG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+Q29udGVudCAqPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPFJpY2hUZXh0RWRpdG9yXHJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbnRlbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyh2YWx1ZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2NvbnRlbnQnLCB2YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJXcml0ZSB5b3VyIGFydGljbGUgY29udGVudCBoZXJlLi4uXCJcclxuICAgICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmNvbnRlbnR9XHJcbiAgICAgICAgICAgICAgICAgICAgbWluSGVpZ2h0PXszMDB9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgV3JpdGUgdGhlIGZ1bGwgY29udGVudCBvZiB5b3VyIG5ld3MgYXJ0aWNsZS4gVXNlIHRoZSB0b29sYmFyIGFib3ZlIGZvciBmb3JtYXR0aW5nLlxyXG4gICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9Gb3JtU2VjdGlvbj4gICAgICAgICAgICA8Rm9ybVNlY3Rpb24gdGl0bGU9XCJDYXRlZ29yeSAmIE1lZGlhXCIgZGVzY3JpcHRpb249XCJBcnRpY2xlIGNhdGVnb3JpemF0aW9uIGFuZCBtZWRpYSBhc3NldHNcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgIDxTZWxlY3RGaWVsZFxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIkNhdGVnb3J5XCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWxlY3QgYSBjYXRlZ29yeVwiXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jYXRlZ29yeUlkfVxyXG4gICAgICAgICAgICAgICAgICBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IHVwZGF0ZUZvcm1EYXRhKCdjYXRlZ29yeUlkJywgdmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLmNhdGVnb3J5SWR9XHJcbiAgICAgICAgICAgICAgICAgIG9wdGlvbnM9e2NhdGVnb3JpZXMubWFwKChjYXRlZ29yeTogYW55KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlOiBjYXRlZ29yeS5pZC50b1N0cmluZygpLFxyXG4gICAgICAgICAgICAgICAgICAgIGxhYmVsOiBjYXRlZ29yeS5uYW1lXHJcbiAgICAgICAgICAgICAgICAgIH0pKX1cclxuICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzTG9hZGluZ0NhdGVnb3JpZXN9XHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiQ2hvb3NlIHRoZSBuZXdzIGNhdGVnb3J5XCJcclxuICAgICAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJGZWF0dXJlZCBJbWFnZSBVUkxcIlxyXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImh0dHBzOi8vZXhhbXBsZS5jb20vaW1hZ2UuanBnXCJcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmZlYXR1cmVkSW1hZ2V9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ2ZlYXR1cmVkSW1hZ2UnLCBlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuZmVhdHVyZWRJbWFnZX1cclxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJPcHRpb25hbCBpbWFnZSB0byBkaXNwbGF5IHdpdGggdGhlIGFydGljbGVcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPElucHV0RmllbGRcclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiVGFnc1wiXHJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cInNwb3J0LCBmb290YmFsbCwgbmV3cywgYnJlYWtpbmdcIlxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRhZ3N9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd0YWdzJywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJDb21tYS1zZXBhcmF0ZWQgdGFncyBmb3IgY2F0ZWdvcml6YXRpb25cIlxyXG4gICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDwvRm9ybVNlY3Rpb24+XHJcblxyXG4gICAgICAgICAgICA8Rm9ybVNlY3Rpb24gdGl0bGU9XCJQdWJsaXNoaW5nIFNldHRpbmdzXCIgZGVzY3JpcHRpb249XCJQdWJsaWNhdGlvbiBkYXRlIGFuZCB2aXNpYmlsaXR5IG9wdGlvbnNcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cclxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUHVibGlzaCBEYXRlICpcIlxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXHJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wdWJsaXNoRGF0ZX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncHVibGlzaERhdGUnLCBlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMucHVibGlzaERhdGV9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUHVibGlzaCBUaW1lICpcIlxyXG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGltZVwiXHJcbiAgICAgICAgICAgICAgICAgIHJlcXVpcmVkXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wdWJsaXNoVGltZX1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgncHVibGlzaFRpbWUnLCBlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMucHVibGlzaFRpbWV9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgPFRvZ2dsZVN3aXRjaFxyXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5zdGF0dXMgPT09ICdwdWJsaXNoZWQnfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eyhjaGVja2VkKSA9PiB1cGRhdGVGb3JtRGF0YSgnc3RhdHVzJywgY2hlY2tlZCA/ICdwdWJsaXNoZWQnIDogJ2RyYWZ0Jyl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiUHVibGlzaCBBcnRpY2xlXCJcclxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJNYWtlIHRoaXMgYXJ0aWNsZSB2aXNpYmxlIHRvIHRoZSBwdWJsaWNcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgICA8VG9nZ2xlU3dpdGNoXHJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLmlzRmVhdHVyZWR9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+IHVwZGF0ZUZvcm1EYXRhKCdpc0ZlYXR1cmVkJywgY2hlY2tlZCl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiRmVhdHVyZWQgQXJ0aWNsZVwiXHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiTWFyayB0aGlzIGFzIGEgZmVhdHVyZWQvdHJlbmRpbmcgYXJ0aWNsZVwiXHJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJkYW5nZXJcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9Gb3JtU2VjdGlvbj5cclxuXHJcbiAgICAgICAgICAgIDxGb3JtU2VjdGlvbiB0aXRsZT1cIlNFTyBTZXR0aW5nc1wiIGRlc2NyaXB0aW9uPVwiU2VhcmNoIGVuZ2luZSBvcHRpbWl6YXRpb24gc2V0dGluZ3NcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJNZXRhIFRpdGxlXCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTRU8tb3B0aW1pemVkIHRpdGxlIGZvciBzZWFyY2ggZW5naW5lc1wiXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5tZXRhVGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ21ldGFUaXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5tZXRhVGl0bGV9XHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiVGl0bGUgdGFnIGZvciBzZWFyY2ggZW5naW5lcyAobWF4IDIwMCBjaGFyYWN0ZXJzKVwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5NZXRhIERlc2NyaXB0aW9uPC9sYWJlbD5cclxuICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXHJcbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJCcmllZiBkZXNjcmlwdGlvbiBmb3Igc2VhcmNoIGVuZ2luZSByZXN1bHRzXCJcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubWV0YURlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ21ldGFEZXNjcmlwdGlvbicsIGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtaW4taC1bMTAwcHhdIHctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnQgcmVzaXplLXZlcnRpY2FsXCJcclxuICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAge2Vycm9ycy5tZXRhRGVzY3JpcHRpb24gJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5tZXRhRGVzY3JpcHRpb259PC9wPlxyXG4gICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cclxuICAgICAgICAgICAgICAgICAgICBEZXNjcmlwdGlvbiBmb3Igc2VhcmNoIGVuZ2luZSByZXN1bHRzIChtYXggNTAwIGNoYXJhY3RlcnMpXHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPlxyXG5cclxuICAgICAgICAgICAgPEZvcm1BY3Rpb25zPiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxyXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcclxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByb3V0ZXIuYmFjaygpfVxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGVOZXdzTXV0YXRpb24uaXNMb2FkaW5nfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgQ2FuY2VsXHJcbiAgICAgICAgICAgIDwvQnV0dG9uPiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgIHR5cGU9XCJzdWJtaXRcIlxyXG4gICAgICAgICAgICAgIGRpc2FibGVkPXt1cGRhdGVOZXdzTXV0YXRpb24uaXNMb2FkaW5nfVxyXG4gICAgICAgICAgICA+ICAgICAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICB7dXBkYXRlTmV3c011dGF0aW9uLmlzTG9hZGluZyA/ICdVcGRhdGluZy4uLicgOiAnVXBkYXRlIEFydGljbGUnfVxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICA8L0Zvcm1BY3Rpb25zPlxyXG4gICAgICAgICAgPC9mb3JtPlxyXG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XHJcbiAgICAgIDwvQ2FyZD5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUGFyYW1zIiwidXNlUm91dGVyIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJ1dHRvbiIsIklucHV0RmllbGQiLCJTZWxlY3RGaWVsZCIsIkZvcm1TZWN0aW9uIiwiRm9ybUFjdGlvbnMiLCJUb2dnbGVTd2l0Y2giLCJTa2VsZXRvbiIsInVzZU5ld3NCeUlkIiwidXNlVXBkYXRlTmV3cyIsInVzZVB1YmxpY0NhdGVnb3JpZXMiLCJBcnJvd0xlZnQiLCJTYXZlIiwiTmV3c3BhcGVyIiwiQWxlcnRUcmlhbmdsZSIsInRvYXN0IiwiUmljaFRleHRFZGl0b3IiLCJFZGl0TmV3c1BhZ2UiLCJwYXJhbXMiLCJyb3V0ZXIiLCJuZXdzSWQiLCJwYXJzZUludCIsImlkIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsInRpdGxlIiwiY29udGVudCIsImV4Y2VycHQiLCJmZWF0dXJlZEltYWdlIiwidGFncyIsImNhdGVnb3J5SWQiLCJzdGF0dXMiLCJpc0ZlYXR1cmVkIiwicHVibGlzaERhdGUiLCJwdWJsaXNoVGltZSIsIm1ldGFUaXRsZSIsIm1ldGFEZXNjcmlwdGlvbiIsImVycm9ycyIsInNldEVycm9ycyIsImRhdGEiLCJuZXdzIiwiaXNMb2FkaW5nIiwibmV3c0xvYWRpbmciLCJlcnJvciIsImNhdGVnb3JpZXMiLCJpc0xvYWRpbmdDYXRlZ29yaWVzIiwidXBkYXRlTmV3c011dGF0aW9uIiwiRGF0ZSIsInB1Ymxpc2hlZEF0IiwiY3JlYXRlZEF0Iiwiam9pbiIsInRvU3RyaW5nIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInRvVGltZVN0cmluZyIsInNsaWNlIiwidmFsaWRhdGVGb3JtIiwibmV3RXJyb3JzIiwidHJpbSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJwdWJsaXNoRGF0ZVRpbWUiLCJ0YWdzQXJyYXkiLCJtYXAiLCJ0YWciLCJmaWx0ZXIiLCJzdWJtaXREYXRhIiwidW5kZWZpbmVkIiwibXV0YXRlQXN5bmMiLCJwdXNoIiwidXBkYXRlRm9ybURhdGEiLCJmaWVsZCIsInZhbHVlIiwicHJldiIsImRpdiIsImNsYXNzTmFtZSIsInZhcmlhbnQiLCJvbkNsaWNrIiwiYmFjayIsImgzIiwicCIsIm1lc3NhZ2UiLCJoMSIsImZvcm0iLCJvblN1Ym1pdCIsImRlc2NyaXB0aW9uIiwibGFiZWwiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJtaW5IZWlnaHQiLCJvblZhbHVlQ2hhbmdlIiwib3B0aW9ucyIsImNhdGVnb3J5IiwibmFtZSIsImRpc2FibGVkIiwidHlwZSIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiLCJ0ZXh0YXJlYSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/rich-text-editor.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/rich-text-editor.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Dynamically import ReactQuill to avoid SSR issues\nconst ReactQuill = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-quill_lib_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"(app-pages-browser)/./node_modules/react-quill/lib/index.js\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-[200px] w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-5/6\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n});\n_c = ReactQuill;\n// Import Quill styles\n\nconst RichTextEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value = \"\", onChange, placeholder = \"Start writing...\", className, error, disabled = false, minHeight = 200 } = param;\n    _s();\n    const quillRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            focus: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.focus();\n                }\n            },\n            blur: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.blur();\n                }\n            },\n            getEditor: ()=>{\n                var _quillRef_current;\n                return (_quillRef_current = quillRef.current) === null || _quillRef_current === void 0 ? void 0 : _quillRef_current.getEditor();\n            }\n        }));\n    // Quill modules configuration\n    const modules = {\n        toolbar: [\n            [\n                {\n                    \"header\": [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6,\n                        false\n                    ]\n                }\n            ],\n            [\n                {\n                    \"font\": []\n                }\n            ],\n            [\n                {\n                    \"size\": [\n                        \"small\",\n                        false,\n                        \"large\",\n                        \"huge\"\n                    ]\n                }\n            ],\n            [\n                \"bold\",\n                \"italic\",\n                \"underline\",\n                \"strike\"\n            ],\n            [\n                {\n                    \"color\": []\n                },\n                {\n                    \"background\": []\n                }\n            ],\n            [\n                {\n                    \"script\": \"sub\"\n                },\n                {\n                    \"script\": \"super\"\n                }\n            ],\n            [\n                {\n                    \"list\": \"ordered\"\n                },\n                {\n                    \"list\": \"bullet\"\n                }\n            ],\n            [\n                {\n                    \"indent\": \"-1\"\n                },\n                {\n                    \"indent\": \"+1\"\n                }\n            ],\n            [\n                {\n                    \"direction\": \"rtl\"\n                }\n            ],\n            [\n                {\n                    \"align\": []\n                }\n            ],\n            [\n                \"blockquote\",\n                \"code-block\"\n            ],\n            [\n                \"link\",\n                \"image\",\n                \"video\"\n            ],\n            [\n                \"clean\"\n            ]\n        ],\n        clipboard: {\n            matchVisual: false\n        }\n    };\n    // Quill formats\n    const formats = [\n        \"header\",\n        \"font\",\n        \"size\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"color\",\n        \"background\",\n        \"script\",\n        \"list\",\n        \"bullet\",\n        \"indent\",\n        \"direction\",\n        \"align\",\n        \"blockquote\",\n        \"code-block\",\n        \"link\",\n        \"image\",\n        \"video\"\n    ];\n    // Custom styles for the editor\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Add custom CSS for the editor\n        const style = document.createElement(\"style\");\n        style.textContent = \"\\n      .ql-editor {\\n        min-height: \".concat(minHeight, \"px;\\n        font-family: inherit;\\n        font-size: 14px;\\n        line-height: 1.6;\\n      }\\n      .ql-toolbar {\\n        border-top: 1px solid #e2e8f0;\\n        border-left: 1px solid #e2e8f0;\\n        border-right: 1px solid #e2e8f0;\\n        border-bottom: none;\\n        border-radius: 6px 6px 0 0;\\n      }\\n      .ql-container {\\n        border-bottom: 1px solid #e2e8f0;\\n        border-left: 1px solid #e2e8f0;\\n        border-right: 1px solid #e2e8f0;\\n        border-top: none;\\n        border-radius: 0 0 6px 6px;\\n        font-family: inherit;\\n      }\\n      .ql-editor.ql-blank::before {\\n        color: #9ca3af;\\n        font-style: normal;\\n      }\\n      \").concat(error ? \"\\n        .ql-toolbar,\\n        .ql-container {\\n          border-color: #ef4444;\\n        }\\n      \" : \"\", \"\\n      \").concat(disabled ? \"\\n        .ql-toolbar {\\n          pointer-events: none;\\n          opacity: 0.6;\\n        }\\n        .ql-editor {\\n          background-color: #f9fafb;\\n          color: #6b7280;\\n        }\\n      \" : \"\", \"\\n    \");\n        document.head.appendChild(style);\n        return ()=>{\n            document.head.removeChild(style);\n        };\n    }, [\n        minHeight,\n        error,\n        disabled\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQuill, {\n                ref: quillRef,\n                theme: \"snow\",\n                value: value,\n                onChange: onChange,\n                placeholder: placeholder,\n                modules: modules,\n                formats: formats,\n                readOnly: disabled,\n                style: {\n                    backgroundColor: disabled ? \"#f9fafb\" : \"white\"\n                }\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 170,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n}, \"B5iL0NdUNZPneueEEztTCcVNiMA=\")), \"B5iL0NdUNZPneueEEztTCcVNiMA=\");\n_c2 = RichTextEditor;\nRichTextEditor.displayName = \"RichTextEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (RichTextEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ReactQuill\");\n$RefreshReg$(_c1, \"RichTextEditor$forwardRef\");\n$RefreshReg$(_c2, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\n"));

/***/ })

});