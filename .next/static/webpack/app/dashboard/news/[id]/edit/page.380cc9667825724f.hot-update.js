"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/news/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/rich-text-editor */ \"(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditNewsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const newsId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: \"\",\n        publishTime: \"\",\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Data fetching\n    const { data: news, isLoading: newsLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById)(newsId);\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories)();\n    // Mutations\n    const updateNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews)();\n    // Populate form when news data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (news) {\n            const publishDate = new Date(news.publishedAt || news.createdAt);\n            setFormData({\n                title: news.title,\n                content: news.content,\n                excerpt: news.excerpt || \"\",\n                featuredImage: news.featuredImage || \"\",\n                tags: news.tags ? news.tags.join(\", \") : \"\",\n                categoryId: news.categoryId ? news.categoryId.toString() : \"\",\n                status: news.status,\n                isFeatured: news.isFeatured,\n                publishDate: publishDate.toISOString().split(\"T\")[0],\n                publishTime: publishDate.toTimeString().slice(0, 5),\n                metaTitle: news.metaTitle || \"\",\n                metaDescription: news.metaDescription || \"\"\n            });\n        }\n    }, [\n        news\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            excerpt: formData.excerpt || undefined,\n            featuredImage: formData.featuredImage || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,\n            status: formData.status,\n            publishedAt: formData.status === \"published\" ? publishDateTime.toISOString() : undefined,\n            isFeatured: formData.isFeatured,\n            metaTitle: formData.metaTitle || undefined,\n            metaDescription: formData.metaDescription || undefined\n        };\n        try {\n            await updateNewsMutation.mutateAsync({\n                id: newsId,\n                data: submitData\n            });\n            router.push(\"/dashboard/news/\".concat(newsId));\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    if (newsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are trying to edit does not exist.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 174,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update the article information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Edit Article: \",\n                                    news.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the information below to modify the news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Excerpt\",\n                                                placeholder: \"Brief excerpt of the article\",\n                                                value: formData.excerpt,\n                                                onChange: (e)=>updateFormData(\"excerpt\", e.target.value),\n                                                error: errors.excerpt,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_rich_text_editor__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        value: formData.content,\n                                                        onChange: (value)=>updateFormData(\"content\", value),\n                                                        placeholder: \"Write your article content here...\",\n                                                        error: errors.content,\n                                                        minHeight: 300,\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. Use the toolbar above for formatting.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 258,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Category & Media\",\n                                    description: \"Article categorization and media assets\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.RadioField, {\n                                            label: \"Category\",\n                                            value: formData.categoryId,\n                                            onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                            error: errors.categoryId,\n                                            options: categories.map((category)=>({\n                                                    value: category.id.toString(),\n                                                    label: category.name,\n                                                    disabled: isLoadingCategories\n                                                })),\n                                            orientation: \"vertical\",\n                                            description: \"Choose the news category\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Featured Image URL\",\n                                            placeholder: \"https://example.com/image.jpg\",\n                                            value: formData.featuredImage,\n                                            onChange: (e)=>updateFormData(\"featuredImage\", e.target.value),\n                                            error: errors.featuredImage,\n                                            description: \"Optional image to display with the article\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.status === \"published\",\n                                                    onCheckedChange: (checked)=>updateFormData(\"status\", checked ? \"published\" : \"draft\"),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isFeatured,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isFeatured\", checked),\n                                                    label: \"Featured Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"SEO Settings\",\n                                    description: \"Search engine optimization settings\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Meta Title\",\n                                                placeholder: \"SEO-optimized title for search engines\",\n                                                value: formData.metaTitle,\n                                                onChange: (e)=>updateFormData(\"metaTitle\", e.target.value),\n                                                error: errors.metaTitle,\n                                                description: \"Title tag for search engines (max 200 characters)\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Meta Description\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Brief description for search engine results\",\n                                                        value: formData.metaDescription,\n                                                        onChange: (e)=>updateFormData(\"metaDescription\", e.target.value),\n                                                        className: \"min-h-[100px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.metaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.metaDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Description for search engine results (max 500 characters)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 40\n                                        }, this),\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: [\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 30\n                                                }, this),\n                                                updateNewsMutation.isLoading ? \"Updating...\" : \"Update Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n        lineNumber: 201,\n        columnNumber: 5\n    }, this);\n}\n_s(EditNewsPage, \"l/CaPmMML2VztxFzqV5C817tgTQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews\n    ];\n});\n_c = EditNewsPage;\nvar _c;\n$RefreshReg$(_c, \"EditNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx\n"));

/***/ })

});