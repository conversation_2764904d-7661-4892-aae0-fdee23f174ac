"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/components/ui/rich-text-editor.tsx":
/*!************************************************!*\
  !*** ./src/components/ui/rich-text-editor.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react_quill_dist_quill_snow_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-quill/dist/quill.snow.css */ \"(app-pages-browser)/./node_modules/react-quill/dist/quill.snow.css\");\n/* harmony import */ var _styles_rich_text_editor_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/rich-text-editor.css */ \"(app-pages-browser)/./src/styles/rich-text-editor.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Dynamically import ReactQuill to avoid SSR issues\nconst ReactQuill = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-quill_lib_index_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! react-quill */ \"(app-pages-browser)/./node_modules/react-quill/lib/index.js\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx -> \" + \"react-quill\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-[200px] w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-3/4 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-1/2 mb-2\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-200 rounded w-5/6\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n            lineNumber: 11,\n            columnNumber: 5\n        }, undefined)\n});\n_c = ReactQuill;\n// Import Quill styles\n\n\nconst RichTextEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value = \"\", onChange, placeholder = \"Start writing...\", className, error, disabled = false, minHeight = 200 } = param;\n    _s();\n    const quillRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, ()=>({\n            focus: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.focus();\n                }\n            },\n            blur: ()=>{\n                if (quillRef.current) {\n                    quillRef.current.blur();\n                }\n            },\n            getEditor: ()=>{\n                var _quillRef_current;\n                return (_quillRef_current = quillRef.current) === null || _quillRef_current === void 0 ? void 0 : _quillRef_current.getEditor();\n            }\n        }));\n    // Image handler function\n    const imageHandler = ()=>{\n        const input = document.createElement(\"input\");\n        input.setAttribute(\"type\", \"file\");\n        input.setAttribute(\"accept\", \"image/*\");\n        input.click();\n        input.onchange = ()=>{\n            var _input_files;\n            const file = (_input_files = input.files) === null || _input_files === void 0 ? void 0 : _input_files[0];\n            if (file) {\n                // For now, we'll use a simple file reader to convert to base64\n                // In production, you'd want to upload to a server\n                const reader = new FileReader();\n                reader.onload = (e)=>{\n                    var _e_target, _quillRef_current;\n                    const imageUrl = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                    const quill = (_quillRef_current = quillRef.current) === null || _quillRef_current === void 0 ? void 0 : _quillRef_current.getEditor();\n                    if (quill) {\n                        const range = quill.getSelection();\n                        quill.insertEmbed((range === null || range === void 0 ? void 0 : range.index) || 0, \"image\", imageUrl);\n                    }\n                };\n                reader.readAsDataURL(file);\n            }\n        };\n    };\n    // Quill modules configuration\n    const modules = {\n        toolbar: [\n            [\n                {\n                    \"header\": [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6,\n                        false\n                    ]\n                }\n            ],\n            [\n                {\n                    \"font\": []\n                }\n            ],\n            [\n                {\n                    \"size\": [\n                        \"small\",\n                        false,\n                        \"large\",\n                        \"huge\"\n                    ]\n                }\n            ],\n            [\n                \"bold\",\n                \"italic\",\n                \"underline\",\n                \"strike\"\n            ],\n            [\n                {\n                    \"color\": []\n                },\n                {\n                    \"background\": []\n                }\n            ],\n            [\n                {\n                    \"script\": \"sub\"\n                },\n                {\n                    \"script\": \"super\"\n                }\n            ],\n            [\n                {\n                    \"list\": \"ordered\"\n                },\n                {\n                    \"list\": \"bullet\"\n                }\n            ],\n            [\n                {\n                    \"indent\": \"-1\"\n                },\n                {\n                    \"indent\": \"+1\"\n                }\n            ],\n            [\n                {\n                    \"direction\": \"rtl\"\n                }\n            ],\n            [\n                {\n                    \"align\": []\n                }\n            ],\n            [\n                \"blockquote\",\n                \"code-block\"\n            ],\n            [\n                \"link\",\n                \"image\",\n                \"video\"\n            ],\n            [\n                \"clean\"\n            ]\n        ],\n        clipboard: {\n            matchVisual: false\n        }\n    };\n    // Quill formats\n    const formats = [\n        \"header\",\n        \"font\",\n        \"size\",\n        \"bold\",\n        \"italic\",\n        \"underline\",\n        \"strike\",\n        \"color\",\n        \"background\",\n        \"script\",\n        \"list\",\n        \"bullet\",\n        \"indent\",\n        \"direction\",\n        \"align\",\n        \"blockquote\",\n        \"code-block\",\n        \"link\",\n        \"image\",\n        \"video\"\n    ];\n    // Apply dynamic styles\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const editorElement = document.querySelector(\".ql-editor\");\n        if (editorElement) {\n            editorElement.style.minHeight = \"\".concat(minHeight, \"px\");\n        }\n    }, [\n        minHeight\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"rich-text-editor\", error && \"error\", disabled && \"disabled\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactQuill, {\n                    ref: quillRef,\n                    theme: \"snow\",\n                    value: value,\n                    onChange: onChange,\n                    placeholder: placeholder,\n                    modules: modules,\n                    formats: formats,\n                    readOnly: disabled\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-red-600 mt-1\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/rich-text-editor.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n}, \"B5iL0NdUNZPneueEEztTCcVNiMA=\")), \"B5iL0NdUNZPneueEEztTCcVNiMA=\");\n_c2 = RichTextEditor;\nRichTextEditor.displayName = \"RichTextEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (RichTextEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ReactQuill\");\n$RefreshReg$(_c1, \"RichTextEditor$forwardRef\");\n$RefreshReg$(_c2, \"RichTextEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/rich-text-editor.tsx\n"));

/***/ })

});