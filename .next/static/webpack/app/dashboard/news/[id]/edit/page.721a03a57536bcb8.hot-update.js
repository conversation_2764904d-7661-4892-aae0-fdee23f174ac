"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/[id]/edit/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/news/[id]/edit/page.tsx ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ EditNewsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/form-field */ \"(app-pages-browser)/./src/components/ui/form-field.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/hooks/useNews */ \"(app-pages-browser)/./src/lib/hooks/useNews.ts\");\n/* harmony import */ var _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/hooks/useCategories */ \"(app-pages-browser)/./src/lib/hooks/useCategories.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/newspaper.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Newspaper,Save!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction EditNewsPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const newsId = parseInt(params.id);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        content: \"\",\n        excerpt: \"\",\n        featuredImage: \"\",\n        tags: \"\",\n        categoryId: \"\",\n        status: \"draft\",\n        isFeatured: false,\n        publishDate: \"\",\n        publishTime: \"\",\n        metaTitle: \"\",\n        metaDescription: \"\"\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Data fetching\n    const { data: news, isLoading: newsLoading, error } = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById)(newsId);\n    const { data: categories = [], isLoading: isLoadingCategories } = (0,_lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories)();\n    // Mutations\n    const updateNewsMutation = (0,_lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews)();\n    // Populate form when news data loads\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (news) {\n            const publishDate = new Date(news.publishedAt || news.createdAt);\n            setFormData({\n                title: news.title,\n                content: news.content,\n                excerpt: news.excerpt || \"\",\n                featuredImage: news.featuredImage || \"\",\n                tags: news.tags ? news.tags.join(\", \") : \"\",\n                categoryId: news.categoryId ? news.categoryId.toString() : \"\",\n                status: news.status,\n                isFeatured: news.isFeatured,\n                publishDate: publishDate.toISOString().split(\"T\")[0],\n                publishTime: publishDate.toTimeString().slice(0, 5),\n                metaTitle: news.metaTitle || \"\",\n                metaDescription: news.metaDescription || \"\"\n            });\n        }\n    }, [\n        news\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.title.trim()) newErrors.title = \"Title is required\";\n        if (!formData.content.trim()) newErrors.content = \"Content is required\";\n        if (!formData.publishDate) newErrors.publishDate = \"Publish date is required\";\n        if (!formData.publishTime) newErrors.publishTime = \"Publish time is required\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            sonner__WEBPACK_IMPORTED_MODULE_10__.toast.error(\"Please fix the form errors\");\n            return;\n        }\n        // Combine date and time\n        const publishDateTime = new Date(\"\".concat(formData.publishDate, \"T\").concat(formData.publishTime));\n        // Parse tags\n        const tagsArray = formData.tags.split(\",\").map((tag)=>tag.trim()).filter((tag)=>tag.length > 0);\n        const submitData = {\n            title: formData.title,\n            content: formData.content,\n            author: formData.author,\n            summary: formData.summary || undefined,\n            imageUrl: formData.imageUrl || undefined,\n            tags: tagsArray.length > 0 ? tagsArray : undefined,\n            categoryId: formData.categoryId ? parseInt(formData.categoryId) : undefined,\n            isPublished: formData.isPublished,\n            isHot: formData.isHot,\n            publishDate: publishDateTime.toISOString()\n        };\n        try {\n            await updateNewsMutation.mutateAsync({\n                id: newsId,\n                data: submitData\n            });\n            router.push(\"/dashboard/news/\".concat(newsId));\n        } catch (error) {\n        // Error handled by mutation\n        }\n    };\n    const updateFormData = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        // Clear error when user starts typing\n        if (errors[field]) {\n            setErrors((prev)=>({\n                    ...prev,\n                    [field]: undefined\n                }));\n        }\n    };\n    if (newsLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-6 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                    className: \"h-4 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-4 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.Skeleton, {\n                                            className: \"h-10 w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 137,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !news) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"mx-auto h-12 w-12 text-red-500 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-red-600 mb-2\",\n                                    children: error ? \"Failed to load news\" : \"News not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, this),\n                                \"              \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: (error === null || error === void 0 ? void 0 : error.message) || \"The news article you are trying to edit does not exist.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 34\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/news\"),\n                                    children: \"Return to News\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n            lineNumber: 169,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-3 h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Edit News Article\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Update the article information\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: [\n                                    \"Edit Article: \",\n                                    news.title\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Update the information below to modify the news article\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Basic Information\",\n                                    description: \"Essential article details\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Title *\",\n                                                placeholder: \"Enter article title\",\n                                                required: true,\n                                                value: formData.title,\n                                                onChange: (e)=>updateFormData(\"title\", e.target.value),\n                                                error: errors.title,\n                                                description: \"The main headline of your news article\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                label: \"Summary\",\n                                                placeholder: \"Brief summary of the article\",\n                                                value: formData.summary,\n                                                onChange: (e)=>updateFormData(\"summary\", e.target.value),\n                                                error: errors.summary,\n                                                description: \"A short description that appears in previews and lists\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: \"Content *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        placeholder: \"Write your article content here...\",\n                                                        required: true,\n                                                        value: formData.content,\n                                                        onChange: (e)=>updateFormData(\"content\", e.target.value),\n                                                        className: \"min-h-[300px] w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-red-600\",\n                                                        children: errors.content\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Write the full content of your news article. HTML tags are supported.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                \"            \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Author & Media\",\n                                    description: \"Author information and media assets\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Author *\",\n                                                    placeholder: \"Author name\",\n                                                    required: true,\n                                                    value: formData.author,\n                                                    onChange: (e)=>updateFormData(\"author\", e.target.value),\n                                                    error: errors.author\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.SelectField, {\n                                                    label: \"Category\",\n                                                    placeholder: \"Select a category\",\n                                                    value: formData.categoryId,\n                                                    onValueChange: (value)=>updateFormData(\"categoryId\", value),\n                                                    error: errors.categoryId,\n                                                    options: categories.map((category)=>({\n                                                            value: category.id.toString(),\n                                                            label: category.name\n                                                        })),\n                                                    disabled: isLoadingCategories,\n                                                    description: \"Choose the news category\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Featured Image URL\",\n                                                    placeholder: \"https://example.com/image.jpg\",\n                                                    value: formData.imageUrl,\n                                                    onChange: (e)=>updateFormData(\"imageUrl\", e.target.value),\n                                                    error: errors.imageUrl,\n                                                    description: \"Optional image to display with the article\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                            label: \"Tags\",\n                                            placeholder: \"sport, football, news, breaking\",\n                                            value: formData.tags,\n                                            onChange: (e)=>updateFormData(\"tags\", e.target.value),\n                                            description: \"Comma-separated tags for categorization\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 39\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormSection, {\n                                    title: \"Publishing Settings\",\n                                    description: \"Publication date and visibility options\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Date *\",\n                                                    type: \"date\",\n                                                    required: true,\n                                                    value: formData.publishDate,\n                                                    onChange: (e)=>updateFormData(\"publishDate\", e.target.value),\n                                                    error: errors.publishDate\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.InputField, {\n                                                    label: \"Publish Time *\",\n                                                    type: \"time\",\n                                                    required: true,\n                                                    value: formData.publishTime,\n                                                    onChange: (e)=>updateFormData(\"publishTime\", e.target.value),\n                                                    error: errors.publishTime\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isPublished,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isPublished\", checked),\n                                                    label: \"Publish Article\",\n                                                    description: \"Make this article visible to the public\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_6__.ToggleSwitch, {\n                                                    checked: formData.isHot,\n                                                    onCheckedChange: (checked)=>updateFormData(\"isHot\", checked),\n                                                    label: \"Hot Article\",\n                                                    description: \"Mark this as a featured/trending article\",\n                                                    variant: \"danger\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form_field__WEBPACK_IMPORTED_MODULE_5__.FormActions, {\n                                    children: [\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"button\",\n                                            variant: \"outline\",\n                                            onClick: ()=>router.back(),\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: \"Cancel\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 40\n                                        }, this),\n                                        \"              \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            type: \"submit\",\n                                            disabled: updateNewsMutation.isLoading,\n                                            children: [\n                                                \"                \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Newspaper_Save_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 30\n                                                }, this),\n                                                updateNewsMutation.isLoading ? \"Updating...\" : \"Update Article\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                                    lineNumber: 342,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/news/[id]/edit/page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(EditNewsPage, \"l/CaPmMML2VztxFzqV5C817tgTQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useNewsById,\n        _lib_hooks_useCategories__WEBPACK_IMPORTED_MODULE_9__.usePublicCategories,\n        _lib_hooks_useNews__WEBPACK_IMPORTED_MODULE_8__.useUpdateNews\n    ];\n});\n_c = EditNewsPage;\nvar _c;\n$RefreshReg$(_c, \"EditNewsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL25ld3MvW2lkXS9lZGl0L3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDVztBQUMwQztBQUNqRDtBQUMrQztBQUNsQztBQUNUO0FBQ2E7QUFDRDtBQUVTO0FBQzFDO0FBaUJoQixTQUFTd0I7O0lBQ3RCLE1BQU1DLFNBQVN2QiwwREFBU0E7SUFDeEIsTUFBTXdCLFNBQVN2QiwwREFBU0E7SUFDeEIsTUFBTXdCLFNBQVNDLFNBQVNILE9BQU9JLEVBQUU7SUFDakMsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUcvQiwrQ0FBUUEsQ0FBZTtRQUNyRGdDLE9BQU87UUFDUEMsU0FBUztRQUNUQyxTQUFTO1FBQ1RDLGVBQWU7UUFDZkMsTUFBTTtRQUNOQyxZQUFZO1FBQ1pDLFFBQVE7UUFDUkMsWUFBWTtRQUNaQyxhQUFhO1FBQ2JDLGFBQWE7UUFDYkMsV0FBVztRQUNYQyxpQkFBaUI7SUFDbkI7SUFFQSxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBRzdDLCtDQUFRQSxDQUF3QixDQUFDO0lBQzdELGdCQUFnQjtJQUNoQixNQUFNLEVBQUU4QyxNQUFNQyxJQUFJLEVBQUVDLFdBQVdDLFdBQVcsRUFBRUMsS0FBSyxFQUFFLEdBQUdsQywrREFBV0EsQ0FBQ1c7SUFDbEUsTUFBTSxFQUFFbUIsTUFBTUssYUFBYSxFQUFFLEVBQUVILFdBQVdJLG1CQUFtQixFQUFFLEdBQUdsQyw2RUFBbUJBO0lBRXJGLFlBQVk7SUFDWixNQUFNbUMscUJBQXFCcEMsaUVBQWFBO0lBRXhDLHFDQUFxQztJQUNyQ2hCLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSThDLE1BQU07WUFDUixNQUFNUCxjQUFjLElBQUljLEtBQUtQLEtBQUtRLFdBQVcsSUFBSVIsS0FBS1MsU0FBUztZQUMvRHpCLFlBQVk7Z0JBQ1ZDLE9BQU9lLEtBQUtmLEtBQUs7Z0JBQ2pCQyxTQUFTYyxLQUFLZCxPQUFPO2dCQUNyQkMsU0FBU2EsS0FBS2IsT0FBTyxJQUFJO2dCQUN6QkMsZUFBZVksS0FBS1osYUFBYSxJQUFJO2dCQUNyQ0MsTUFBTVcsS0FBS1gsSUFBSSxHQUFHVyxLQUFLWCxJQUFJLENBQUNxQixJQUFJLENBQUMsUUFBUTtnQkFDekNwQixZQUFZVSxLQUFLVixVQUFVLEdBQUdVLEtBQUtWLFVBQVUsQ0FBQ3FCLFFBQVEsS0FBSztnQkFDM0RwQixRQUFRUyxLQUFLVCxNQUFNO2dCQUNuQkMsWUFBWVEsS0FBS1IsVUFBVTtnQkFDM0JDLGFBQWFBLFlBQVltQixXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDcERuQixhQUFhRCxZQUFZcUIsWUFBWSxHQUFHQyxLQUFLLENBQUMsR0FBRztnQkFDakRwQixXQUFXSyxLQUFLTCxTQUFTLElBQUk7Z0JBQzdCQyxpQkFBaUJJLEtBQUtKLGVBQWUsSUFBSTtZQUMzQztRQUNGO0lBQ0YsR0FBRztRQUFDSTtLQUFLO0lBRVQsTUFBTWdCLGVBQWU7UUFDbkIsTUFBTUMsWUFBbUMsQ0FBQztRQUUxQyxJQUFJLENBQUNsQyxTQUFTRSxLQUFLLENBQUNpQyxJQUFJLElBQUlELFVBQVVoQyxLQUFLLEdBQUc7UUFDOUMsSUFBSSxDQUFDRixTQUFTRyxPQUFPLENBQUNnQyxJQUFJLElBQUlELFVBQVUvQixPQUFPLEdBQUc7UUFDbEQsSUFBSSxDQUFDSCxTQUFTVSxXQUFXLEVBQUV3QixVQUFVeEIsV0FBVyxHQUFHO1FBQ25ELElBQUksQ0FBQ1YsU0FBU1csV0FBVyxFQUFFdUIsVUFBVXZCLFdBQVcsR0FBRztRQUVuREksVUFBVW1CO1FBQ1YsT0FBT0UsT0FBT0MsSUFBSSxDQUFDSCxXQUFXSSxNQUFNLEtBQUs7SUFDM0M7SUFFQSxNQUFNQyxlQUFlLE9BQU9DO1FBQzFCQSxFQUFFQyxjQUFjO1FBRWhCLElBQUksQ0FBQ1IsZ0JBQWdCO1lBQ25CeEMsMENBQUtBLENBQUMyQixLQUFLLENBQUM7WUFDWjtRQUNGO1FBRUEsd0JBQXdCO1FBQ3hCLE1BQU1zQixrQkFBa0IsSUFBSWxCLEtBQUssR0FBMkJ4QixPQUF4QkEsU0FBU1UsV0FBVyxFQUFDLEtBQXdCLE9BQXJCVixTQUFTVyxXQUFXO1FBRWhGLGFBQWE7UUFDYixNQUFNZ0MsWUFBWTNDLFNBQVNNLElBQUksQ0FDNUJ3QixLQUFLLENBQUMsS0FDTmMsR0FBRyxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJVixJQUFJLElBQ25CVyxNQUFNLENBQUNELENBQUFBLE1BQU9BLElBQUlQLE1BQU0sR0FBRztRQUFJLE1BQU1TLGFBQTZCO1lBQ2pFN0MsT0FBT0YsU0FBU0UsS0FBSztZQUNyQkMsU0FBU0gsU0FBU0csT0FBTztZQUN6QjZDLFFBQVFoRCxTQUFTZ0QsTUFBTTtZQUN2QkMsU0FBU2pELFNBQVNpRCxPQUFPLElBQUlDO1lBQzdCQyxVQUFVbkQsU0FBU21ELFFBQVEsSUFBSUQ7WUFDL0I1QyxNQUFNcUMsVUFBVUwsTUFBTSxHQUFHLElBQUlLLFlBQVlPO1lBQ3pDM0MsWUFBWVAsU0FBU08sVUFBVSxHQUFHVCxTQUFTRSxTQUFTTyxVQUFVLElBQUkyQztZQUNsRUUsYUFBYXBELFNBQVNvRCxXQUFXO1lBQ2pDQyxPQUFPckQsU0FBU3FELEtBQUs7WUFDckIzQyxhQUFhZ0MsZ0JBQWdCYixXQUFXO1FBQzFDO1FBRUYsSUFBSTtZQUNGLE1BQU1OLG1CQUFtQitCLFdBQVcsQ0FBQztnQkFBRXZELElBQUlGO2dCQUFRbUIsTUFBTStCO1lBQVc7WUFDcEVuRCxPQUFPMkQsSUFBSSxDQUFDLG1CQUEwQixPQUFQMUQ7UUFDakMsRUFBRSxPQUFPdUIsT0FBTztRQUNkLDRCQUE0QjtRQUM5QjtJQUNGO0lBRUEsTUFBTW9DLGlCQUFpQixDQUFDQyxPQUEyQkM7UUFDakR6RCxZQUFZMEQsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFLENBQUNGLE1BQU0sRUFBRUM7WUFBTTtRQUMvQyxzQ0FBc0M7UUFDdEMsSUFBSTVDLE1BQU0sQ0FBQzJDLE1BQU0sRUFBRTtZQUNqQjFDLFVBQVU0QyxDQUFBQSxPQUFTO29CQUFFLEdBQUdBLElBQUk7b0JBQUUsQ0FBQ0YsTUFBTSxFQUFFUDtnQkFBVTtRQUNuRDtJQUNGO0lBRUEsSUFBSS9CLGFBQWE7UUFDZixxQkFDRSw4REFBQ3lDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUM1RSw2REFBUUE7NEJBQUM0RSxXQUFVOzs7Ozs7c0NBQ3BCLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM1RSw2REFBUUE7b0NBQUM0RSxXQUFVOzs7Ozs7OENBQ3BCLDhEQUFDNUUsNkRBQVFBO29DQUFDNEUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUd4Qiw4REFBQ3ZGLHFEQUFJQTs7c0NBQ0gsOERBQUNHLDJEQUFVQTs7OENBQ1QsOERBQUNRLDZEQUFRQTtvQ0FBQzRFLFdBQVU7Ozs7Ozs4Q0FDcEIsOERBQUM1RSw2REFBUUE7b0NBQUM0RSxXQUFVOzs7Ozs7Ozs7Ozs7c0NBRXRCLDhEQUFDdEYsNERBQVdBOzRCQUFDc0YsV0FBVTs7OENBQ3JCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1RSw2REFBUUE7NENBQUM0RSxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDNUUsNkRBQVFBOzRDQUFDNEUsV0FBVTs7Ozs7O3NEQUNwQiw4REFBQzVFLDZEQUFRQTs0Q0FBQzRFLFdBQVU7Ozs7OztzREFDcEIsOERBQUM1RSw2REFBUUE7NENBQUM0RSxXQUFVOzs7Ozs7Ozs7Ozs7OENBRXRCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUM1RSw2REFBUUE7NENBQUM0RSxXQUFVOzs7Ozs7c0RBQ3BCLDhEQUFDNUUsNkRBQVFBOzRDQUFDNEUsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTWhDO0lBRUEsSUFBSXpDLFNBQVMsQ0FBQ0gsTUFBTTtRQUNsQixxQkFDRSw4REFBQzJDO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ2xGLHlEQUFNQTt3QkFBQ21GLFNBQVE7d0JBQVVDLFNBQVMsSUFBTW5FLE9BQU9vRSxJQUFJOzswQ0FDbEQsOERBQUMzRSxtSEFBU0E7Z0NBQUN3RSxXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7Ozs7Ozs7OEJBSTFDLDhEQUFDdkYscURBQUlBOzhCQUNILDRFQUFDQyw0REFBV0E7d0JBQUNzRixXQUFVO2tDQUNyQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDckUsbUhBQWFBO29DQUFDcUUsV0FBVTs7Ozs7OzhDQUN6Qiw4REFBQ0k7b0NBQUdKLFdBQVU7OENBQ1h6QyxRQUFRLHdCQUF3Qjs7Ozs7O2dDQUM5Qjs4Q0FBYyw4REFBQzhDO29DQUFFTCxXQUFVOzhDQUM3QixDQUFDekMsa0JBQUFBLDRCQUFELE1BQWtCK0MsT0FBTyxLQUFJOzs7Ozs7OENBRWhDLDhEQUFDeEYseURBQU1BO29DQUFDb0YsU0FBUyxJQUFNbkUsT0FBTzJELElBQUksQ0FBQzs4Q0FBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFRbkU7SUFFQSxxQkFDRSw4REFBQ0s7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2xGLHlEQUFNQTt3QkFBQ21GLFNBQVE7d0JBQVVDLFNBQVMsSUFBTW5FLE9BQU9vRSxJQUFJOzswQ0FDbEQsOERBQUMzRSxtSEFBU0E7Z0NBQUN3RSxXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7O2tDQUd4Qyw4REFBQ0Q7OzBDQUNDLDhEQUFDUTtnQ0FBR1AsV0FBVTs7a0RBQ1osOERBQUN0RSxtSEFBU0E7d0NBQUNzRSxXQUFVOzs7Ozs7b0NBQStCOzs7Ozs7OzBDQUd0RCw4REFBQ0s7Z0NBQUVMLFdBQVU7MENBQXFCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS3RDLDhEQUFDdkYscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBOzswQ0FDVCw4REFBQ0MsMERBQVNBOztvQ0FBQztvQ0FBZXVDLEtBQUtmLEtBQUs7Ozs7Ozs7MENBQ3BDLDhEQUFDMUIsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBSW5CLDhEQUFDRCw0REFBV0E7a0NBQ1YsNEVBQUM4Rjs0QkFBS0MsVUFBVS9COzRCQUFjc0IsV0FBVTs7OENBQ3RDLDhEQUFDL0Usa0VBQVdBO29DQUFDb0IsT0FBTTtvQ0FBb0JxRSxhQUFZOzhDQUNqRCw0RUFBQ1g7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDakYsaUVBQVVBO2dEQUNUNEYsT0FBTTtnREFDTkMsYUFBWTtnREFDWkMsUUFBUTtnREFDUmhCLE9BQU8xRCxTQUFTRSxLQUFLO2dEQUNyQnlFLFVBQVUsQ0FBQ25DLElBQU1nQixlQUFlLFNBQVNoQixFQUFFb0MsTUFBTSxDQUFDbEIsS0FBSztnREFDdkR0QyxPQUFPTixPQUFPWixLQUFLO2dEQUNuQnFFLGFBQVk7Ozs7OzswREFHZCw4REFBQzNGLGlFQUFVQTtnREFDVDRGLE9BQU07Z0RBQ05DLGFBQVk7Z0RBQ1pmLE9BQU8xRCxTQUFTaUQsT0FBTztnREFDdkIwQixVQUFVLENBQUNuQyxJQUFNZ0IsZUFBZSxXQUFXaEIsRUFBRW9DLE1BQU0sQ0FBQ2xCLEtBQUs7Z0RBQ3pEdEMsT0FBT04sT0FBT21DLE9BQU87Z0RBQ3JCc0IsYUFBWTs7Ozs7OzBEQUdkLDhEQUFDWDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNXO3dEQUFNWCxXQUFVO2tFQUFvQzs7Ozs7O2tFQUNyRCw4REFBQ2dCO3dEQUNDSixhQUFZO3dEQUNaQyxRQUFRO3dEQUNSaEIsT0FBTzFELFNBQVNHLE9BQU87d0RBQ3ZCd0UsVUFBVSxDQUFDbkMsSUFBTWdCLGVBQWUsV0FBV2hCLEVBQUVvQyxNQUFNLENBQUNsQixLQUFLO3dEQUN6REcsV0FBVTs7Ozs7O29EQUVYL0MsT0FBT1gsT0FBTyxrQkFDYiw4REFBQytEO3dEQUFFTCxXQUFVO2tFQUF3Qi9DLE9BQU9YLE9BQU87Ozs7OztrRUFFckQsOERBQUMrRDt3REFBRUwsV0FBVTtrRUFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUs3Qjs4Q0FBWSw4REFBQy9FLGtFQUFXQTtvQ0FBQ29CLE9BQU07b0NBQWlCcUUsYUFBWTs7c0RBQ3hFLDhEQUFDWDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNqRixpRUFBVUE7b0RBQ1Q0RixPQUFNO29EQUNOQyxhQUFZO29EQUNaQyxRQUFRO29EQUNSaEIsT0FBTzFELFNBQVNnRCxNQUFNO29EQUN0QjJCLFVBQVUsQ0FBQ25DLElBQU1nQixlQUFlLFVBQVVoQixFQUFFb0MsTUFBTSxDQUFDbEIsS0FBSztvREFDeER0QyxPQUFPTixPQUFPa0MsTUFBTTs7Ozs7OzhEQUd0Qiw4REFBQ25FLGtFQUFXQTtvREFDVjJGLE9BQU07b0RBQ05DLGFBQVk7b0RBQW9CZixPQUFPMUQsU0FBU08sVUFBVTtvREFDMUR1RSxlQUFlLENBQUNwQixRQUFVRixlQUFlLGNBQWNFO29EQUN2RHRDLE9BQU9OLE9BQU9QLFVBQVU7b0RBQ3hCd0UsU0FBUzFELFdBQVd1QixHQUFHLENBQUMsQ0FBQ29DLFdBQW1COzREQUMxQ3RCLE9BQU9zQixTQUFTakYsRUFBRSxDQUFDNkIsUUFBUTs0REFDM0I0QyxPQUFPUSxTQUFTQyxJQUFJO3dEQUN0QjtvREFDQUMsVUFBVTVEO29EQUNWaUQsYUFBWTs7Ozs7OzhEQUdkLDhEQUFDM0YsaUVBQVVBO29EQUNUNEYsT0FBTTtvREFDTkMsYUFBWTtvREFDWmYsT0FBTzFELFNBQVNtRCxRQUFRO29EQUN4QndCLFVBQVUsQ0FBQ25DLElBQU1nQixlQUFlLFlBQVloQixFQUFFb0MsTUFBTSxDQUFDbEIsS0FBSztvREFDMUR0QyxPQUFPTixPQUFPcUMsUUFBUTtvREFDdEJvQixhQUFZOzs7Ozs7Ozs7Ozs7c0RBSWhCLDhEQUFDM0YsaUVBQVVBOzRDQUNUNEYsT0FBTTs0Q0FDTkMsYUFBWTs0Q0FDWmYsT0FBTzFELFNBQVNNLElBQUk7NENBQ3BCcUUsVUFBVSxDQUFDbkMsSUFBTWdCLGVBQWUsUUFBUWhCLEVBQUVvQyxNQUFNLENBQUNsQixLQUFLOzRDQUN0RGEsYUFBWTs7Ozs7Ozs7Ozs7OzhDQUloQiw4REFBQ3pGLGtFQUFXQTtvQ0FBQ29CLE9BQU07b0NBQXNCcUUsYUFBWTs7c0RBQ25ELDhEQUFDWDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNqRixpRUFBVUE7b0RBQ1Q0RixPQUFNO29EQUNOVyxNQUFLO29EQUNMVCxRQUFRO29EQUNSaEIsT0FBTzFELFNBQVNVLFdBQVc7b0RBQzNCaUUsVUFBVSxDQUFDbkMsSUFBTWdCLGVBQWUsZUFBZWhCLEVBQUVvQyxNQUFNLENBQUNsQixLQUFLO29EQUM3RHRDLE9BQU9OLE9BQU9KLFdBQVc7Ozs7Ozs4REFHM0IsOERBQUM5QixpRUFBVUE7b0RBQ1Q0RixPQUFNO29EQUNOVyxNQUFLO29EQUNMVCxRQUFRO29EQUNSaEIsT0FBTzFELFNBQVNXLFdBQVc7b0RBQzNCZ0UsVUFBVSxDQUFDbkMsSUFBTWdCLGVBQWUsZUFBZWhCLEVBQUVvQyxNQUFNLENBQUNsQixLQUFLO29EQUM3RHRDLE9BQU9OLE9BQU9ILFdBQVc7Ozs7Ozs7Ozs7OztzREFJN0IsOERBQUNpRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUM3RSxzRUFBWUE7b0RBQ1hvRyxTQUFTcEYsU0FBU29ELFdBQVc7b0RBQzdCaUMsaUJBQWlCLENBQUNELFVBQVk1QixlQUFlLGVBQWU0QjtvREFDNURaLE9BQU07b0RBQ05ELGFBQVk7Ozs7Ozs4REFHZCw4REFBQ3ZGLHNFQUFZQTtvREFDWG9HLFNBQVNwRixTQUFTcUQsS0FBSztvREFDdkJnQyxpQkFBaUIsQ0FBQ0QsVUFBWTVCLGVBQWUsU0FBUzRCO29EQUN0RFosT0FBTTtvREFDTkQsYUFBWTtvREFDWlQsU0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUtkLDhEQUFDL0Usa0VBQVdBOzt3Q0FBQztzREFBYyw4REFBQ0oseURBQU1BOzRDQUNoQ3dHLE1BQUs7NENBQ0xyQixTQUFROzRDQUNSQyxTQUFTLElBQU1uRSxPQUFPb0UsSUFBSTs0Q0FDMUJrQixVQUFVM0QsbUJBQW1CTCxTQUFTO3NEQUN2Qzs7Ozs7O3dDQUVRO3NEQUFjLDhEQUFDdkMseURBQU1BOzRDQUM1QndHLE1BQUs7NENBQ0xELFVBQVUzRCxtQkFBbUJMLFNBQVM7O2dEQUN2Qzs4REFBZ0IsOERBQUM1QixtSEFBSUE7b0RBQUN1RSxXQUFVOzs7Ozs7Z0RBQzVCdEMsbUJBQW1CTCxTQUFTLEdBQUcsZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRaEU7R0ExVXdCeEI7O1FBQ1B0QixzREFBU0E7UUFDVEMsc0RBQVNBO1FBbUI4QmEsMkRBQVdBO1FBQ0NFLHlFQUFtQkE7UUFHMURELDZEQUFhQTs7O0tBekJsQk8iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvbmV3cy9baWRdL2VkaXQvcGFnZS50c3g/NDRmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYXJkJztcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XHJcbmltcG9ydCB7IElucHV0RmllbGQsIFNlbGVjdEZpZWxkLCBGb3JtU2VjdGlvbiwgRm9ybUFjdGlvbnMgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvZm9ybS1maWVsZCc7XHJcbmltcG9ydCB7IFRvZ2dsZVN3aXRjaCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90b2dnbGUtc3dpdGNoJztcclxuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2tlbGV0b24nO1xyXG5pbXBvcnQgeyB1c2VOZXdzQnlJZCwgdXNlVXBkYXRlTmV3cyB9IGZyb20gJ0AvbGliL2hvb2tzL3VzZU5ld3MnO1xyXG5pbXBvcnQgeyB1c2VQdWJsaWNDYXRlZ29yaWVzIH0gZnJvbSAnQC9saWIvaG9va3MvdXNlQ2F0ZWdvcmllcyc7XHJcbmltcG9ydCB7IFVwZGF0ZU5ld3NEYXRhIH0gZnJvbSAnQC9saWIvYXBpL25ld3MnO1xyXG5pbXBvcnQgeyBBcnJvd0xlZnQsIFNhdmUsIE5ld3NwYXBlciwgQWxlcnRUcmlhbmdsZSB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSAnc29ubmVyJztcclxuXHJcbmludGVyZmFjZSBOZXdzRm9ybURhdGEge1xyXG4gIHRpdGxlOiBzdHJpbmc7XHJcbiAgY29udGVudDogc3RyaW5nO1xyXG4gIGV4Y2VycHQ6IHN0cmluZztcclxuICBmZWF0dXJlZEltYWdlOiBzdHJpbmc7XHJcbiAgdGFnczogc3RyaW5nO1xyXG4gIGNhdGVnb3J5SWQ6IHN0cmluZztcclxuICBzdGF0dXM6ICdkcmFmdCcgfCAncHVibGlzaGVkJyB8ICdhcmNoaXZlZCc7XHJcbiAgaXNGZWF0dXJlZDogYm9vbGVhbjtcclxuICBwdWJsaXNoRGF0ZTogc3RyaW5nO1xyXG4gIHB1Ymxpc2hUaW1lOiBzdHJpbmc7XHJcbiAgbWV0YVRpdGxlOiBzdHJpbmc7XHJcbiAgbWV0YURlc2NyaXB0aW9uOiBzdHJpbmc7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVkaXROZXdzUGFnZSgpIHtcclxuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBuZXdzSWQgPSBwYXJzZUludChwYXJhbXMuaWQgYXMgc3RyaW5nKTtcclxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlPE5ld3NGb3JtRGF0YT4oe1xyXG4gICAgdGl0bGU6ICcnLFxyXG4gICAgY29udGVudDogJycsXHJcbiAgICBleGNlcnB0OiAnJyxcclxuICAgIGZlYXR1cmVkSW1hZ2U6ICcnLFxyXG4gICAgdGFnczogJycsXHJcbiAgICBjYXRlZ29yeUlkOiAnJyxcclxuICAgIHN0YXR1czogJ2RyYWZ0JyxcclxuICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxyXG4gICAgcHVibGlzaERhdGU6ICcnLFxyXG4gICAgcHVibGlzaFRpbWU6ICcnLFxyXG4gICAgbWV0YVRpdGxlOiAnJyxcclxuICAgIG1ldGFEZXNjcmlwdGlvbjogJycsXHJcbiAgfSk7XHJcblxyXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxQYXJ0aWFsPE5ld3NGb3JtRGF0YT4+KHt9KTtcclxuICAvLyBEYXRhIGZldGNoaW5nXHJcbiAgY29uc3QgeyBkYXRhOiBuZXdzLCBpc0xvYWRpbmc6IG5ld3NMb2FkaW5nLCBlcnJvciB9ID0gdXNlTmV3c0J5SWQobmV3c0lkKTtcclxuICBjb25zdCB7IGRhdGE6IGNhdGVnb3JpZXMgPSBbXSwgaXNMb2FkaW5nOiBpc0xvYWRpbmdDYXRlZ29yaWVzIH0gPSB1c2VQdWJsaWNDYXRlZ29yaWVzKCk7XHJcblxyXG4gIC8vIE11dGF0aW9uc1xyXG4gIGNvbnN0IHVwZGF0ZU5ld3NNdXRhdGlvbiA9IHVzZVVwZGF0ZU5ld3MoKTtcclxuXHJcbiAgLy8gUG9wdWxhdGUgZm9ybSB3aGVuIG5ld3MgZGF0YSBsb2Fkc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAobmV3cykge1xyXG4gICAgICBjb25zdCBwdWJsaXNoRGF0ZSA9IG5ldyBEYXRlKG5ld3MucHVibGlzaGVkQXQgfHwgbmV3cy5jcmVhdGVkQXQpO1xyXG4gICAgICBzZXRGb3JtRGF0YSh7XHJcbiAgICAgICAgdGl0bGU6IG5ld3MudGl0bGUsXHJcbiAgICAgICAgY29udGVudDogbmV3cy5jb250ZW50LFxyXG4gICAgICAgIGV4Y2VycHQ6IG5ld3MuZXhjZXJwdCB8fCAnJyxcclxuICAgICAgICBmZWF0dXJlZEltYWdlOiBuZXdzLmZlYXR1cmVkSW1hZ2UgfHwgJycsXHJcbiAgICAgICAgdGFnczogbmV3cy50YWdzID8gbmV3cy50YWdzLmpvaW4oJywgJykgOiAnJyxcclxuICAgICAgICBjYXRlZ29yeUlkOiBuZXdzLmNhdGVnb3J5SWQgPyBuZXdzLmNhdGVnb3J5SWQudG9TdHJpbmcoKSA6ICcnLFxyXG4gICAgICAgIHN0YXR1czogbmV3cy5zdGF0dXMsXHJcbiAgICAgICAgaXNGZWF0dXJlZDogbmV3cy5pc0ZlYXR1cmVkLFxyXG4gICAgICAgIHB1Ymxpc2hEYXRlOiBwdWJsaXNoRGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXHJcbiAgICAgICAgcHVibGlzaFRpbWU6IHB1Ymxpc2hEYXRlLnRvVGltZVN0cmluZygpLnNsaWNlKDAsIDUpLFxyXG4gICAgICAgIG1ldGFUaXRsZTogbmV3cy5tZXRhVGl0bGUgfHwgJycsXHJcbiAgICAgICAgbWV0YURlc2NyaXB0aW9uOiBuZXdzLm1ldGFEZXNjcmlwdGlvbiB8fCAnJyxcclxuICAgICAgfSk7XHJcbiAgICB9XHJcbiAgfSwgW25ld3NdKTtcclxuXHJcbiAgY29uc3QgdmFsaWRhdGVGb3JtID0gKCk6IGJvb2xlYW4gPT4ge1xyXG4gICAgY29uc3QgbmV3RXJyb3JzOiBQYXJ0aWFsPE5ld3NGb3JtRGF0YT4gPSB7fTtcclxuXHJcbiAgICBpZiAoIWZvcm1EYXRhLnRpdGxlLnRyaW0oKSkgbmV3RXJyb3JzLnRpdGxlID0gJ1RpdGxlIGlzIHJlcXVpcmVkJztcclxuICAgIGlmICghZm9ybURhdGEuY29udGVudC50cmltKCkpIG5ld0Vycm9ycy5jb250ZW50ID0gJ0NvbnRlbnQgaXMgcmVxdWlyZWQnO1xyXG4gICAgaWYgKCFmb3JtRGF0YS5wdWJsaXNoRGF0ZSkgbmV3RXJyb3JzLnB1Ymxpc2hEYXRlID0gJ1B1Ymxpc2ggZGF0ZSBpcyByZXF1aXJlZCc7XHJcbiAgICBpZiAoIWZvcm1EYXRhLnB1Ymxpc2hUaW1lKSBuZXdFcnJvcnMucHVibGlzaFRpbWUgPSAnUHVibGlzaCB0aW1lIGlzIHJlcXVpcmVkJztcclxuXHJcbiAgICBzZXRFcnJvcnMobmV3RXJyb3JzKTtcclxuICAgIHJldHVybiBPYmplY3Qua2V5cyhuZXdFcnJvcnMpLmxlbmd0aCA9PT0gMDtcclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XHJcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcblxyXG4gICAgaWYgKCF2YWxpZGF0ZUZvcm0oKSkge1xyXG4gICAgICB0b2FzdC5lcnJvcignUGxlYXNlIGZpeCB0aGUgZm9ybSBlcnJvcnMnKTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENvbWJpbmUgZGF0ZSBhbmQgdGltZVxyXG4gICAgY29uc3QgcHVibGlzaERhdGVUaW1lID0gbmV3IERhdGUoYCR7Zm9ybURhdGEucHVibGlzaERhdGV9VCR7Zm9ybURhdGEucHVibGlzaFRpbWV9YCk7XHJcblxyXG4gICAgLy8gUGFyc2UgdGFnc1xyXG4gICAgY29uc3QgdGFnc0FycmF5ID0gZm9ybURhdGEudGFnc1xyXG4gICAgICAuc3BsaXQoJywnKVxyXG4gICAgICAubWFwKHRhZyA9PiB0YWcudHJpbSgpKVxyXG4gICAgICAuZmlsdGVyKHRhZyA9PiB0YWcubGVuZ3RoID4gMCk7IGNvbnN0IHN1Ym1pdERhdGE6IFVwZGF0ZU5ld3NEYXRhID0ge1xyXG4gICAgICAgIHRpdGxlOiBmb3JtRGF0YS50aXRsZSxcclxuICAgICAgICBjb250ZW50OiBmb3JtRGF0YS5jb250ZW50LFxyXG4gICAgICAgIGF1dGhvcjogZm9ybURhdGEuYXV0aG9yLFxyXG4gICAgICAgIHN1bW1hcnk6IGZvcm1EYXRhLnN1bW1hcnkgfHwgdW5kZWZpbmVkLFxyXG4gICAgICAgIGltYWdlVXJsOiBmb3JtRGF0YS5pbWFnZVVybCB8fCB1bmRlZmluZWQsXHJcbiAgICAgICAgdGFnczogdGFnc0FycmF5Lmxlbmd0aCA+IDAgPyB0YWdzQXJyYXkgOiB1bmRlZmluZWQsXHJcbiAgICAgICAgY2F0ZWdvcnlJZDogZm9ybURhdGEuY2F0ZWdvcnlJZCA/IHBhcnNlSW50KGZvcm1EYXRhLmNhdGVnb3J5SWQpIDogdW5kZWZpbmVkLFxyXG4gICAgICAgIGlzUHVibGlzaGVkOiBmb3JtRGF0YS5pc1B1Ymxpc2hlZCxcclxuICAgICAgICBpc0hvdDogZm9ybURhdGEuaXNIb3QsXHJcbiAgICAgICAgcHVibGlzaERhdGU6IHB1Ymxpc2hEYXRlVGltZS50b0lTT1N0cmluZygpLFxyXG4gICAgICB9O1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIGF3YWl0IHVwZGF0ZU5ld3NNdXRhdGlvbi5tdXRhdGVBc3luYyh7IGlkOiBuZXdzSWQsIGRhdGE6IHN1Ym1pdERhdGEgfSk7XHJcbiAgICAgIHJvdXRlci5wdXNoKGAvZGFzaGJvYXJkL25ld3MvJHtuZXdzSWR9YCk7XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICAvLyBFcnJvciBoYW5kbGVkIGJ5IG11dGF0aW9uXHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgdXBkYXRlRm9ybURhdGEgPSAoZmllbGQ6IGtleW9mIE5ld3NGb3JtRGF0YSwgdmFsdWU6IHN0cmluZyB8IGJvb2xlYW4pID0+IHtcclxuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgW2ZpZWxkXTogdmFsdWUgfSkpO1xyXG4gICAgLy8gQ2xlYXIgZXJyb3Igd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcclxuICAgIGlmIChlcnJvcnNbZmllbGRdKSB7XHJcbiAgICAgIHNldEVycm9ycyhwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IHVuZGVmaW5lZCB9KSk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgaWYgKG5ld3NMb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XHJcbiAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0xMCB3LTIwXCIgLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTggdy02NFwiIC8+XHJcbiAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy00OFwiIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC02IHctNDhcIiAvPlxyXG4gICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC00IHctNjRcIiAvPlxyXG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTQgdy0zMlwiIC8+XHJcbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTBcIiAvPlxyXG4gICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJoLTEwXCIgLz5cclxuICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC0zMlwiIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1lbmQgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTAgdy0yMFwiIC8+XHJcbiAgICAgICAgICAgICAgPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtMTAgdy0zMlwiIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgICA8L0NhcmQ+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIGlmIChlcnJvciB8fCAhbmV3cykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtNFwiPlxyXG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9PlxyXG4gICAgICAgICAgICA8QXJyb3dMZWZ0IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgIEJhY2tcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxDYXJkPlxyXG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInAtNlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwibXgtYXV0byBoLTEyIHctMTIgdGV4dC1yZWQtNTAwIG1iLTRcIiAvPlxyXG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtIHRleHQtcmVkLTYwMCBtYi0yXCI+XHJcbiAgICAgICAgICAgICAgICB7ZXJyb3IgPyAnRmFpbGVkIHRvIGxvYWQgbmV3cycgOiAnTmV3cyBub3QgZm91bmQnfVxyXG4gICAgICAgICAgICAgIDwvaDM+ICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1iLTRcIj5cclxuICAgICAgICAgICAgICAgIHsoZXJyb3IgYXMgRXJyb3IpPy5tZXNzYWdlIHx8ICdUaGUgbmV3cyBhcnRpY2xlIHlvdSBhcmUgdHJ5aW5nIHRvIGVkaXQgZG9lcyBub3QgZXhpc3QuJ31cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZC9uZXdzJyl9PlxyXG4gICAgICAgICAgICAgICAgUmV0dXJuIHRvIE5ld3NcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxyXG4gICAgICAgIDwvQ2FyZD5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XHJcbiAgICAgIHsvKiBIZWFkZXIgKi99XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XHJcbiAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9PlxyXG4gICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgQmFja1xyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgIDxkaXY+XHJcbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXJcIj5cclxuICAgICAgICAgICAgPE5ld3NwYXBlciBjbGFzc05hbWU9XCJtci0zIGgtOCB3LTggdGV4dC1ibHVlLTYwMFwiIC8+XHJcbiAgICAgICAgICAgIEVkaXQgTmV3cyBBcnRpY2xlXHJcbiAgICAgICAgICA8L2gxPlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtdC0xXCI+VXBkYXRlIHRoZSBhcnRpY2xlIGluZm9ybWF0aW9uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBGb3JtICovfVxyXG4gICAgICA8Q2FyZD5cclxuICAgICAgICA8Q2FyZEhlYWRlcj5cclxuICAgICAgICAgIDxDYXJkVGl0bGU+RWRpdCBBcnRpY2xlOiB7bmV3cy50aXRsZX08L0NhcmRUaXRsZT5cclxuICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICAgIFVwZGF0ZSB0aGUgaW5mb3JtYXRpb24gYmVsb3cgdG8gbW9kaWZ5IHRoZSBuZXdzIGFydGljbGVcclxuICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxyXG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cclxuICAgICAgICA8Q2FyZENvbnRlbnQ+XHJcbiAgICAgICAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cclxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiQmFzaWMgSW5mb3JtYXRpb25cIiBkZXNjcmlwdGlvbj1cIkVzc2VudGlhbCBhcnRpY2xlIGRldGFpbHNcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxyXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJUaXRsZSAqXCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJFbnRlciBhcnRpY2xlIHRpdGxlXCJcclxuICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb249XCJUaGUgbWFpbiBoZWFkbGluZSBvZiB5b3VyIG5ld3MgYXJ0aWNsZVwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiU3VtbWFyeVwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQnJpZWYgc3VtbWFyeSBvZiB0aGUgYXJ0aWNsZVwiXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5zdW1tYXJ5fVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdzdW1tYXJ5JywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnN1bW1hcnl9XHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiQSBzaG9ydCBkZXNjcmlwdGlvbiB0aGF0IGFwcGVhcnMgaW4gcHJldmlld3MgYW5kIGxpc3RzXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPkNvbnRlbnQgKjwvbGFiZWw+XHJcbiAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxyXG4gICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiV3JpdGUgeW91ciBhcnRpY2xlIGNvbnRlbnQgaGVyZS4uLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgcmVxdWlyZWRcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuY29udGVudH1cclxuICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdjb250ZW50JywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1pbi1oLVszMDBweF0gdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCByZXNpemUtdmVydGljYWxcIlxyXG4gICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICB7ZXJyb3JzLmNvbnRlbnQgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5jb250ZW50fTwvcD5cclxuICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgV3JpdGUgdGhlIGZ1bGwgY29udGVudCBvZiB5b3VyIG5ld3MgYXJ0aWNsZS4gSFRNTCB0YWdzIGFyZSBzdXBwb3J0ZWQuXHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPiAgICAgICAgICAgIDxGb3JtU2VjdGlvbiB0aXRsZT1cIkF1dGhvciAmIE1lZGlhXCIgZGVzY3JpcHRpb249XCJBdXRob3IgaW5mb3JtYXRpb24gYW5kIG1lZGlhIGFzc2V0c1wiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxyXG4gICAgICAgICAgICAgICAgPElucHV0RmllbGRcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBdXRob3IgKlwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiQXV0aG9yIG5hbWVcIlxyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYXV0aG9yfVxyXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKCdhdXRob3InLCBlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuYXV0aG9yfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgICA8U2VsZWN0RmllbGRcclxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJDYXRlZ29yeVwiXHJcbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiU2VsZWN0IGEgY2F0ZWdvcnlcIiB2YWx1ZT17Zm9ybURhdGEuY2F0ZWdvcnlJZH1cclxuICAgICAgICAgICAgICAgICAgb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiB1cGRhdGVGb3JtRGF0YSgnY2F0ZWdvcnlJZCcsIHZhbHVlKX1cclxuICAgICAgICAgICAgICAgICAgZXJyb3I9e2Vycm9ycy5jYXRlZ29yeUlkfVxyXG4gICAgICAgICAgICAgICAgICBvcHRpb25zPXtjYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnk6IGFueSkgPT4gKHtcclxuICAgICAgICAgICAgICAgICAgICB2YWx1ZTogY2F0ZWdvcnkuaWQudG9TdHJpbmcoKSxcclxuICAgICAgICAgICAgICAgICAgICBsYWJlbDogY2F0ZWdvcnkubmFtZVxyXG4gICAgICAgICAgICAgICAgICB9KSl9XHJcbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmdDYXRlZ29yaWVzfVxyXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj1cIkNob29zZSB0aGUgbmV3cyBjYXRlZ29yeVwiXHJcbiAgICAgICAgICAgICAgICAvPlxyXG5cclxuICAgICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiRmVhdHVyZWQgSW1hZ2UgVVJMXCJcclxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJodHRwczovL2V4YW1wbGUuY29tL2ltYWdlLmpwZ1wiXHJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5pbWFnZVVybH1cclxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgnaW1hZ2VVcmwnLCBlLnRhcmdldC52YWx1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGVycm9yPXtlcnJvcnMuaW1hZ2VVcmx9XHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiT3B0aW9uYWwgaW1hZ2UgdG8gZGlzcGxheSB3aXRoIHRoZSBhcnRpY2xlXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICAgIDxJbnB1dEZpZWxkXHJcbiAgICAgICAgICAgICAgICBsYWJlbD1cIlRhZ3NcIlxyXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJzcG9ydCwgZm9vdGJhbGwsIG5ld3MsIGJyZWFraW5nXCJcclxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS50YWdzfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgndGFncycsIGUudGFyZ2V0LnZhbHVlKX1cclxuICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiQ29tbWEtc2VwYXJhdGVkIHRhZ3MgZm9yIGNhdGVnb3JpemF0aW9uXCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8L0Zvcm1TZWN0aW9uPlxyXG5cclxuICAgICAgICAgICAgPEZvcm1TZWN0aW9uIHRpdGxlPVwiUHVibGlzaGluZyBTZXR0aW5nc1wiIGRlc2NyaXB0aW9uPVwiUHVibGljYXRpb24gZGF0ZSBhbmQgdmlzaWJpbGl0eSBvcHRpb25zXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XHJcbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlB1Ymxpc2ggRGF0ZSAqXCJcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHVibGlzaERhdGV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3B1Ymxpc2hEYXRlJywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnB1Ymxpc2hEYXRlfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgICAgICA8SW5wdXRGaWVsZFxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlB1Ymxpc2ggVGltZSAqXCJcclxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRpbWVcIlxyXG4gICAgICAgICAgICAgICAgICByZXF1aXJlZFxyXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHVibGlzaFRpbWV9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoJ3B1Ymxpc2hUaW1lJywgZS50YXJnZXQudmFsdWUpfVxyXG4gICAgICAgICAgICAgICAgICBlcnJvcj17ZXJyb3JzLnB1Ymxpc2hUaW1lfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cclxuICAgICAgICAgICAgICAgIDxUb2dnbGVTd2l0Y2hcclxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEuaXNQdWJsaXNoZWR9XHJcbiAgICAgICAgICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KGNoZWNrZWQpID0+IHVwZGF0ZUZvcm1EYXRhKCdpc1B1Ymxpc2hlZCcsIGNoZWNrZWQpfVxyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlB1Ymxpc2ggQXJ0aWNsZVwiXHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uPVwiTWFrZSB0aGlzIGFydGljbGUgdmlzaWJsZSB0byB0aGUgcHVibGljXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcblxyXG4gICAgICAgICAgICAgICAgPFRvZ2dsZVN3aXRjaFxyXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5pc0hvdH1cclxuICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsoY2hlY2tlZCkgPT4gdXBkYXRlRm9ybURhdGEoJ2lzSG90JywgY2hlY2tlZCl9XHJcbiAgICAgICAgICAgICAgICAgIGxhYmVsPVwiSG90IEFydGljbGVcIlxyXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj1cIk1hcmsgdGhpcyBhcyBhIGZlYXR1cmVkL3RyZW5kaW5nIGFydGljbGVcIlxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZGFuZ2VyXCJcclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvRm9ybVNlY3Rpb24+XHJcblxyXG4gICAgICAgICAgICA8Rm9ybUFjdGlvbnM+ICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXHJcbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5iYWNrKCl9XHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwZGF0ZU5ld3NNdXRhdGlvbi5pc0xvYWRpbmd9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICBDYW5jZWxcclxuICAgICAgICAgICAgPC9CdXR0b24+ICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXHJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e3VwZGF0ZU5ld3NNdXRhdGlvbi5pc0xvYWRpbmd9XHJcbiAgICAgICAgICAgID4gICAgICAgICAgICAgICAgPFNhdmUgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgIHt1cGRhdGVOZXdzTXV0YXRpb24uaXNMb2FkaW5nID8gJ1VwZGF0aW5nLi4uJyA6ICdVcGRhdGUgQXJ0aWNsZSd9XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvRm9ybUFjdGlvbnM+XHJcbiAgICAgICAgICA8L2Zvcm0+XHJcbiAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgPC9DYXJkPlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VQYXJhbXMiLCJ1c2VSb3V0ZXIiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkRGVzY3JpcHRpb24iLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiSW5wdXRGaWVsZCIsIlNlbGVjdEZpZWxkIiwiRm9ybVNlY3Rpb24iLCJGb3JtQWN0aW9ucyIsIlRvZ2dsZVN3aXRjaCIsIlNrZWxldG9uIiwidXNlTmV3c0J5SWQiLCJ1c2VVcGRhdGVOZXdzIiwidXNlUHVibGljQ2F0ZWdvcmllcyIsIkFycm93TGVmdCIsIlNhdmUiLCJOZXdzcGFwZXIiLCJBbGVydFRyaWFuZ2xlIiwidG9hc3QiLCJFZGl0TmV3c1BhZ2UiLCJwYXJhbXMiLCJyb3V0ZXIiLCJuZXdzSWQiLCJwYXJzZUludCIsImlkIiwiZm9ybURhdGEiLCJzZXRGb3JtRGF0YSIsInRpdGxlIiwiY29udGVudCIsImV4Y2VycHQiLCJmZWF0dXJlZEltYWdlIiwidGFncyIsImNhdGVnb3J5SWQiLCJzdGF0dXMiLCJpc0ZlYXR1cmVkIiwicHVibGlzaERhdGUiLCJwdWJsaXNoVGltZSIsIm1ldGFUaXRsZSIsIm1ldGFEZXNjcmlwdGlvbiIsImVycm9ycyIsInNldEVycm9ycyIsImRhdGEiLCJuZXdzIiwiaXNMb2FkaW5nIiwibmV3c0xvYWRpbmciLCJlcnJvciIsImNhdGVnb3JpZXMiLCJpc0xvYWRpbmdDYXRlZ29yaWVzIiwidXBkYXRlTmV3c011dGF0aW9uIiwiRGF0ZSIsInB1Ymxpc2hlZEF0IiwiY3JlYXRlZEF0Iiwiam9pbiIsInRvU3RyaW5nIiwidG9JU09TdHJpbmciLCJzcGxpdCIsInRvVGltZVN0cmluZyIsInNsaWNlIiwidmFsaWRhdGVGb3JtIiwibmV3RXJyb3JzIiwidHJpbSIsIk9iamVjdCIsImtleXMiLCJsZW5ndGgiLCJoYW5kbGVTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJwdWJsaXNoRGF0ZVRpbWUiLCJ0YWdzQXJyYXkiLCJtYXAiLCJ0YWciLCJmaWx0ZXIiLCJzdWJtaXREYXRhIiwiYXV0aG9yIiwic3VtbWFyeSIsInVuZGVmaW5lZCIsImltYWdlVXJsIiwiaXNQdWJsaXNoZWQiLCJpc0hvdCIsIm11dGF0ZUFzeW5jIiwicHVzaCIsInVwZGF0ZUZvcm1EYXRhIiwiZmllbGQiLCJ2YWx1ZSIsInByZXYiLCJkaXYiLCJjbGFzc05hbWUiLCJ2YXJpYW50Iiwib25DbGljayIsImJhY2siLCJoMyIsInAiLCJtZXNzYWdlIiwiaDEiLCJmb3JtIiwib25TdWJtaXQiLCJkZXNjcmlwdGlvbiIsImxhYmVsIiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwidGV4dGFyZWEiLCJvblZhbHVlQ2hhbmdlIiwib3B0aW9ucyIsImNhdGVnb3J5IiwibmFtZSIsImRpc2FibGVkIiwidHlwZSIsImNoZWNrZWQiLCJvbkNoZWNrZWRDaGFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/news/[id]/edit/page.tsx\n"));

/***/ })

});