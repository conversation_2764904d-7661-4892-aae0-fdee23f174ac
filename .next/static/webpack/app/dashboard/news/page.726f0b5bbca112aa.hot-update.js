"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/news/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/useNews.ts":
/*!**********************************!*\
  !*** ./src/lib/hooks/useNews.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCreateNews: function() { return /* binding */ useCreateNews; },\n/* harmony export */   useDeleteNews: function() { return /* binding */ useDeleteNews; },\n/* harmony export */   useHotNews: function() { return /* binding */ useHotNews; },\n/* harmony export */   useNews: function() { return /* binding */ useNews; },\n/* harmony export */   useNewsById: function() { return /* binding */ useNewsById; },\n/* harmony export */   usePublishedNews: function() { return /* binding */ usePublishedNews; },\n/* harmony export */   useToggleHotStatus: function() { return /* binding */ useToggleHotStatus; },\n/* harmony export */   useToggleNewsStatus: function() { return /* binding */ useToggleNewsStatus; },\n/* harmony export */   useUpdateNews: function() { return /* binding */ useUpdateNews; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _lib_api_news__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api/news */ \"(app-pages-browser)/./src/lib/api/news.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n\n\n\n// News Query Hooks\nconst useNews = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            \"news\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.getNews(filters),\n        staleTime: 5 * 60 * 1000\n    });\n};\nconst useNewsById = function(id) {\n    let enabled = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            \"news\",\n            id\n        ],\n        queryFn: ()=>_lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.getNewsById(id),\n        enabled: !!id && enabled,\n        staleTime: 10 * 60 * 1000\n    });\n};\nconst usePublishedNews = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            \"news\",\n            \"published\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.getPublishedNews(filters),\n        staleTime: 5 * 60 * 1000\n    });\n};\nconst useHotNews = function() {\n    let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.useQuery)({\n        queryKey: [\n            \"news\",\n            \"hot\",\n            filters\n        ],\n        queryFn: ()=>_lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.getHotNews(filters),\n        staleTime: 5 * 60 * 1000\n    });\n};\n// News Mutation Hooks\nconst useCreateNews = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (data)=>_lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.createNews(data),\n        onSuccess: (newNews)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"News created successfully\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to create news: \".concat(error.message));\n        }\n    });\n};\nconst useUpdateNews = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return _lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.updateNews(id, data);\n        },\n        onSuccess: (updatedNews)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\",\n                    updatedNews.id\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"News updated successfully\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to update news: \".concat(error.message));\n        }\n    });\n};\nconst useDeleteNews = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (id)=>_lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.deleteNews(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"News deleted successfully\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to delete news: \".concat(error.message));\n        }\n    });\n};\nconst useToggleNewsStatus = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, isPublished } = param;\n            return _lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.toggleNewsStatus(id, isPublished);\n        },\n        onSuccess: (updatedNews)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\",\n                    updatedNews.id\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"News \".concat(updatedNews.isPublished ? \"published\" : \"unpublished\", \" successfully\"));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to toggle news status: \".concat(error.message));\n        }\n    });\n};\nconst useToggleHotStatus = ()=>{\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.useQueryClient)();\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_4__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, isHot } = param;\n            return _lib_api_news__WEBPACK_IMPORTED_MODULE_0__.newsApi.toggleHotStatus(id, isHot);\n        },\n        onSuccess: (updatedNews)=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\"\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"news\",\n                    updatedNews.id\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.success(\"News \".concat(updatedNews.isHot ? \"marked as hot\" : \"unmarked as hot\", \" successfully\"));\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to toggle hot status: \".concat(error.message));\n        }\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/useNews.ts\n"));

/***/ })

});