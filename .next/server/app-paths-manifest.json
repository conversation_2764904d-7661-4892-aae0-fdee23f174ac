{"/not-found": "app/not-found.js", "/api/news/[id]/route": "app/api/news/[id]/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/dashboard/news/[id]/edit/page": "app/dashboard/news/[id]/edit/page.js", "/dashboard/news/categories/[id]/edit/page": "app/dashboard/news/categories/[id]/edit/page.js", "/dashboard/news/create/page": "app/dashboard/news/create/page.js", "/dashboard/news/page": "app/dashboard/news/page.js", "/dashboard/news/[id]/page": "app/dashboard/news/[id]/page.js"}