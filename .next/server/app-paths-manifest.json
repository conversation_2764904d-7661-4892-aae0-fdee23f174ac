{"/api/news/route": "app/api/news/route.js", "/api/news/categories/route": "app/api/news/categories/route.js", "/api/news/categories/[id]/route": "app/api/news/categories/[id]/route.js", "/dashboard/news/page": "app/dashboard/news/page.js", "/dashboard/news/[id]/edit/page": "app/dashboard/news/[id]/edit/page.js", "/auth/login/page": "app/auth/login/page.js", "/dashboard/news/[id]/page": "app/dashboard/news/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/news/categories/page": "app/dashboard/news/categories/page.js", "/dashboard/news/categories/[id]/edit/page": "app/dashboard/news/categories/[id]/edit/page.js"}