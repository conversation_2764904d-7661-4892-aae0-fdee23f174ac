"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/news/categories/route";
exports.ids = ["app/api/news/categories/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2Froute&page=%2Fapi%2Fnews%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2Froute&page=%2Fapi%2Fnews%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_news_categories_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/news/categories/route.ts */ \"(rsc)/./src/app/api/news/categories/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/news/categories/route\",\n        pathname: \"/api/news/categories\",\n        filename: \"route\",\n        bundlePath: \"app/api/news/categories/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/news/categories/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_news_categories_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/news/categories/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2Froute&page=%2Fapi%2Fnews%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/news/categories/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/news/categories/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request) {\n    try {\n        console.info(\"Fetching news categories from backend\");\n        const url = new URL(request.url);\n        const params = url.searchParams.toString();\n        // Check if this is an admin request based on authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        const endpoint = authHeader ? `/admin/news/categories${params ? \"?\" + params : \"\"}` : `/news/categories${params ? \"?\" + params : \"\"}`;\n        const fullBackendUrl = `${API_BASE_URL}${endpoint}`;\n        const response = await fetch(fullBackendUrl, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...authHeader && {\n                    \"Authorization\": authHeader\n                }\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Backend error:\", errorText);\n            throw new Error(`Backend responded with status ${response.status}: ${errorText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error fetching news categories:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to fetch news categories\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.info(\"Creating new news category\", body);\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Authorization required for category creation\"\n            }, {\n                status: 401\n            });\n        }\n        const fullBackendUrl = `${API_BASE_URL}/admin/news/categories`;\n        const response = await fetch(fullBackendUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": authHeader\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Backend error:\", errorText);\n            throw new Error(`Backend responded with status ${response.status}: ${errorText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        console.error(\"Error creating news category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to create news category\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/news/categories/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2Froute&page=%2Fapi%2Fnews%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();