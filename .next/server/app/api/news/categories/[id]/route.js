"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/news/categories/[id]/route";
exports.ids = ["app/api/news/categories/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_news_categories_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/news/categories/[id]/route.ts */ \"(rsc)/./src/app/api/news/categories/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/news/categories/[id]/route\",\n        pathname: \"/api/news/categories/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/news/categories/[id]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/news/categories/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_news_categories_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/news/categories/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZuZXdzJTJGY2F0ZWdvcmllcyUyRiU1QmlkJTVEJTJGcm91dGUmcGFnZT0lMkZhcGklMkZuZXdzJTJGY2F0ZWdvcmllcyUyRiU1QmlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGbmV3cyUyRmNhdGVnb3JpZXMlMkYlNUJpZCU1RCUyRnJvdXRlLnRzJmFwcERpcj0lMkZob21lJTJGZHV5YW5oc3RhciUyRkZFQ01TLXNwb3J0JTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZob21lJTJGZHV5YW5oc3RhciUyRkZFQ01TLXNwb3J0JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ3NCO0FBQ25HO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDNko7O0FBRTdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY21zLWFwaXNwb3J0c2dhbWUvPzYxOGUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL2hvbWUvZHV5YW5oc3Rhci9GRUNNUy1zcG9ydC9zcmMvYXBwL2FwaS9uZXdzL2NhdGVnb3JpZXMvW2lkXS9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvbmV3cy9jYXRlZ29yaWVzL1tpZF0vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9uZXdzL2NhdGVnb3JpZXMvW2lkXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvbmV3cy9jYXRlZ29yaWVzL1tpZF0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvaG9tZS9kdXlhbmhzdGFyL0ZFQ01TLXNwb3J0L3NyYy9hcHAvYXBpL25ld3MvY2F0ZWdvcmllcy9baWRdL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL25ld3MvY2F0ZWdvcmllcy9baWRdL3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCwgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/news/categories/[id]/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/news/categories/[id]/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        const authHeader = request.headers.get(\"authorization\");\n        console.log(\"\\uD83D\\uDD04 Fetching category with ID:\", id, \"Auth:\", !!authHeader);\n        // Admin endpoint uses ID, public endpoint might use slug\n        // Try admin endpoint first if authenticated\n        if (authHeader) {\n            const adminResponse = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": authHeader\n                }\n            });\n            if (adminResponse.ok) {\n                const data = await adminResponse.json();\n                console.log(\"✅ Category fetched from admin endpoint\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n            }\n            console.log(\"❌ Admin endpoint failed:\", adminResponse.status);\n        }\n        // Try public endpoint as fallback (might need slug)\n        const publicResponse = await fetch(`${API_BASE_URL}/news/categories/${id}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (publicResponse.ok) {\n            const data = await publicResponse.json();\n            console.log(\"✅ Category fetched from public endpoint\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        }\n        console.log(\"❌ Both endpoints failed\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Category not found\"\n        }, {\n            status: 404\n        });\n    } catch (error) {\n        console.error(\"Error fetching category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    try {\n        const { id } = params;\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authorization required for category update\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const response = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": authHeader\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update category\"\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error updating category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, { params }) {\n    try {\n        const { id } = params;\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authorization required for category patch\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const response = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": authHeader\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to patch category\"\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error patching category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = params;\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authorization required for category deletion\"\n            }, {\n                status: 401\n            });\n        }\n        const response = await fetch(`${API_BASE_URL}/admin/news/categories/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": authHeader\n            }\n        });\n        if (!response.ok) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to delete category\"\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"Error deleting category:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/news/categories/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2Fcategories%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();