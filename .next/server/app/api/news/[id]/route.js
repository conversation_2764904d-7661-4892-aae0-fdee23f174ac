"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/news/[id]/route";
exports.ids = ["app/api/news/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:console":
/*!*******************************!*\
  !*** external "node:console" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("node:console");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_news_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/news/[id]/route.ts */ \"(rsc)/./src/app/api/news/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/news/[id]/route\",\n        pathname: \"/api/news/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/news/[id]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/news/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_news_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/news/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/news/[id]/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/news/[id]/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var node_console__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:console */ \"node:console\");\n/* harmony import */ var node_console__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(node_console__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const { id } = params;\n        node_console__WEBPACK_IMPORTED_MODULE_1__.info(`Fetching news with id: ${id}`);\n        const authHeader = request.headers.get(\"authorization\");\n        // Use admin endpoint if authenticated, public endpoint otherwise\n        // For public access, we'll need to first get all news and find by ID\n        // since the public API uses slug-based access\n        let fullBackendUrl;\n        let headers = {\n            \"Content-Type\": \"application/json\"\n        };\n        if (authHeader) {\n            // Admin access - use ID-based endpoint\n            fullBackendUrl = `${API_BASE_URL}/admin/news/articles/${id}`;\n            headers[\"Authorization\"] = authHeader;\n        } else {\n            // Public access - we need to get the news item differently\n            // For now, we'll try the admin endpoint without auth and fallback if needed\n            fullBackendUrl = `${API_BASE_URL}/admin/news/articles/${id}`;\n        }\n        const response = await fetch(fullBackendUrl, {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Backend error:\", errorText);\n            throw new Error(`Backend responded with status ${response.status}: ${errorText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Error fetching news:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to fetch news\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request, { params }) {\n    try {\n        const { id } = params;\n        const body = await request.json();\n        node_console__WEBPACK_IMPORTED_MODULE_1__.info(`Updating news with id: ${id}`, body);\n        const fullBackendUrl = `${API_BASE_URL}/admin/news/articles/${id}`;\n        const response = await fetch(fullBackendUrl, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Backend error:\", errorText);\n            throw new Error(`Backend responded with status ${response.status}: ${errorText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Error updating news:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to update news\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, { params }) {\n    try {\n        const { id } = params;\n        const body = await request.json();\n        node_console__WEBPACK_IMPORTED_MODULE_1__.info(`Patching news with id: ${id}`, body);\n        const fullBackendUrl = `${API_BASE_URL}/admin/news/articles/${id}`;\n        const response = await fetch(fullBackendUrl, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Backend error:\", errorText);\n            throw new Error(`Backend responded with status ${response.status}: ${errorText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Error patching news:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to patch news\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const { id } = params;\n        node_console__WEBPACK_IMPORTED_MODULE_1__.info(`Deleting news with id: ${id}`);\n        const fullBackendUrl = `${API_BASE_URL}/admin/news/articles/${id}`;\n        const response = await fetch(fullBackendUrl, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Backend error:\", errorText);\n            throw new Error(`Backend responded with status ${response.status}: ${errorText}`);\n        }\n        const data = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: response.status\n        });\n    } catch (error) {\n        node_console__WEBPACK_IMPORTED_MODULE_1__.error(\"Error deleting news:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Failed to delete news\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/news/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fnews%2F%5Bid%5D%2Froute&page=%2Fapi%2Fnews%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fnews%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();