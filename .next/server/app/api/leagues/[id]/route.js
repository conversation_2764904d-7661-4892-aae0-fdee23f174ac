"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/leagues/[id]/route";
exports.ids = ["app/api/leagues/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_leagues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/leagues/[id]/route.ts */ \"(rsc)/./src/app/api/leagues/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/leagues/[id]/route\",\n        pathname: \"/api/leagues/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/leagues/[id]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/leagues/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_leagues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/leagues/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/leagues/[id]/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/leagues/[id]/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const idParam = params.id;\n        // Parse externalId and season from URL parameter (e.g., \"61-2024\")\n        let leagueId;\n        let season;\n        if (idParam.includes(\"-\")) {\n            const [externalId, parsedSeason] = idParam.split(\"-\");\n            leagueId = externalId;\n            season = parsedSeason;\n        } else {\n            leagueId = idParam;\n        }\n        // Build query parameters (include season if parsed from URL)\n        const queryParams = new URLSearchParams();\n        searchParams.forEach((value, key)=>{\n            queryParams.append(key, value);\n        });\n        // Add season from URL if parsed\n        if (season && !queryParams.has(\"season\")) {\n            queryParams.append(\"season\", season);\n        }\n        const queryString = queryParams.toString();\n        const url = `${API_BASE_URL}/football/leagues/${leagueId}${queryString ? `?${queryString}` : \"\"}`;\n        console.log(\"\\uD83D\\uDD04 Proxying league detail request:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch league\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ League detail fetched successfully:\", data);\n        // The API returns an array in data field\n        if (data.data && Array.isArray(data.data)) {\n            const leagues = data.data;\n            // If season is specified in URL, find the exact match\n            if (season) {\n                const seasonInt = parseInt(season);\n                const selectedLeague = leagues.find((league)=>league.season === seasonInt);\n                if (selectedLeague) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(selectedLeague);\n                } else {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `No league data found for season ${season}`\n                    }, {\n                        status: 404\n                    });\n                }\n            }\n            // If no season specified, find the current/active league first\n            let selectedLeague = leagues.find((league)=>league.season_detail?.current === true);\n            // If no current league, get the most recent one (highest season)\n            if (!selectedLeague && leagues.length > 0) {\n                selectedLeague = leagues.reduce((prev, current)=>current.season > prev.season ? current : prev);\n            }\n            if (selectedLeague) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(selectedLeague);\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"No league data found\"\n                }, {\n                    status: 404\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, { params }) {\n    try {\n        const idParam = params.id;\n        const body = await request.json();\n        // Determine if this is an internal ID or externalId-season format\n        let leagueId;\n        if (idParam.includes(\"-\")) {\n            // Format: \"61-2024\" -> use externalId \"61\"\n            leagueId = idParam.split(\"-\")[0];\n        } else {\n            // Direct internal ID (e.g., \"21\") -> use as is\n            leagueId = idParam;\n        }\n        console.log(\"\\uD83D\\uDD04 Proxying league update request to internal ID:\", leagueId);\n        const response = await fetch(`${API_BASE_URL}/football/leagues/${leagueId}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update league\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ League updated successfully:\", data.id || data.externalId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const idParam = params.id;\n        // Parse externalId from URL parameter (e.g., \"61-2024\" -> \"61\")\n        const leagueId = idParam.includes(\"-\") ? idParam.split(\"-\")[0] : idParam;\n        console.log(\"\\uD83D\\uDD04 Proxying league delete request:\", leagueId);\n        const response = await fetch(`${API_BASE_URL}/football/leagues/${leagueId}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to delete league\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        console.log(\"✅ League deleted successfully:\", leagueId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/leagues/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();