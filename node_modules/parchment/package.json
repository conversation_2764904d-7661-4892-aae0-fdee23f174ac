{"name": "parchment", "version": "3.0.0", "description": "A document model for rich text editors", "author": "<PERSON> <<EMAIL>>", "homepage": "http://quilljs.com/docs/parchment", "main": "./dist/parchment.js", "types": "./dist/parchment.d.ts", "type": "module", "sideEffects": false, "files": ["tsconfig.json", "dist", "src"], "devDependencies": {"@arethetypeswrong/cli": "^0.15.1", "@microsoft/api-extractor": "^7.42.3", "@types/node": "^18.15.11", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitest/browser": "^1.4.0", "del-cli": "^5.1.0", "eslint": "^8.46.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-require-extensions": "^0.1.3", "eslint-plugin-tree-shaking": "^1.12.1", "playwright": "1.42.1", "prettier": "^3.2.5", "typescript": "^5.4.2", "vite": "^5.1.6", "vitest": "^1.4.0"}, "prettier": {"singleQuote": true}, "license": "BSD-3-<PERSON><PERSON>", "repository": "github:quilljs/parchment", "scripts": {"build": "npm run build:bundle && npm run build:types", "build:bundle": "vite build", "build:types": "tsc --emitDeclarationOnly && api-extractor run && del-cli dist/typings", "lint": "eslint 'src/**/*.ts' 'tests/**/*.ts'", "prepare": "npm run build", "test": "npm run test:unit", "test:unit": "vitest --typecheck", "test:pkg": "attw $(npm pack)"}, "bugs": {"url": "https://github.com/quilljs/parchment/issues"}}