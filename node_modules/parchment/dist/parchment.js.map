{"version": 3, "file": "parchment.js", "sources": ["../src/scope.ts", "../src/attributor/attributor.ts", "../src/error.ts", "../src/registry.ts", "../src/attributor/class.ts", "../src/attributor/style.ts", "../src/attributor/store.ts", "../src/blot/abstract/shadow.ts", "../src/blot/abstract/leaf.ts", "../src/collection/linked-list.ts", "../src/blot/abstract/parent.ts", "../src/blot/inline.ts", "../src/blot/block.ts", "../src/blot/abstract/container.ts", "../src/blot/embed.ts", "../src/blot/scroll.ts", "../src/blot/text.ts"], "sourcesContent": ["enum Scope {\n  TYPE = (1 << 2) - 1, // 0011 Lower two bits\n  LEVEL = ((1 << 2) - 1) << 2, // 1100 Higher two bits\n\n  ATTRIBUTE = (1 << 0) | LEVEL, // 1101\n  BLOT = (1 << 1) | LEVEL, // 1110\n  INLINE = (1 << 2) | TYPE, // 0111\n  BLOCK = (1 << 3) | TYPE, // 1011\n\n  BLOCK_BLOT = BLOCK & BLOT, // 1010\n  INLINE_BLOT = INLINE & BLOT, // 0110\n  BLOCK_ATTRIBUTE = BLOCK & ATTRIBUTE, // 1001\n  INLINE_ATTRIBUTE = INLINE & ATTRIBUTE, // 0101\n\n  ANY = TYPE | LEVEL,\n}\n\nexport default Scope;\n", "import Scope from '../scope.js';\n\nexport interface AttributorOptions {\n  scope?: Scope;\n  whitelist?: string[];\n}\n\nexport default class Attributor {\n  public static keys(node: HTMLElement): string[] {\n    return Array.from(node.attributes).map((item: Attr) => item.name);\n  }\n\n  public scope: Scope;\n  public whitelist: string[] | undefined;\n\n  constructor(\n    public readonly attrName: string,\n    public readonly keyName: string,\n    options: AttributorOptions = {},\n  ) {\n    const attributeBit = Scope.TYPE & Scope.ATTRIBUTE;\n    this.scope =\n      options.scope != null\n        ? // Ignore type bits, force attribute bit\n          (options.scope & Scope.LEVEL) | attributeBit\n        : Scope.ATTRIBUTE;\n    if (options.whitelist != null) {\n      this.whitelist = options.whitelist;\n    }\n  }\n\n  public add(node: HTMLElement, value: any): boolean {\n    if (!this.canAdd(node, value)) {\n      return false;\n    }\n    node.setAttribute(this.keyName, value);\n    return true;\n  }\n\n  public canAdd(_node: HTMLElement, value: any): boolean {\n    if (this.whitelist == null) {\n      return true;\n    }\n    if (typeof value === 'string') {\n      return this.whitelist.indexOf(value.replace(/[\"']/g, '')) > -1;\n    } else {\n      return this.whitelist.indexOf(value) > -1;\n    }\n  }\n\n  public remove(node: HTMLElement): void {\n    node.removeAttribute(this.keyName);\n  }\n\n  public value(node: HTMLElement): any {\n    const value = node.getAttribute(this.keyName);\n    if (this.canAdd(node, value) && value) {\n      return value;\n    }\n    return '';\n  }\n}\n", "export default class ParchmentError extends Error {\n  public message: string;\n  public name: string;\n  public stack!: string;\n\n  constructor(message: string) {\n    message = '[Parchment] ' + message;\n    super(message);\n    this.message = message;\n    this.name = this.constructor.name;\n  }\n}\n", "import Attributor from './attributor/attributor.js';\nimport {\n  type Blot,\n  type BlotConstructor,\n  type Root,\n} from './blot/abstract/blot.js';\nimport ParchmentError from './error.js';\nimport Scope from './scope.js';\n\nexport type RegistryDefinition = Attributor | BlotConstructor;\n\nexport interface RegistryInterface {\n  create(scroll: Root, input: Node | string | Scope, value?: any): Blot;\n  query(query: string | Node | Scope, scope: Scope): RegistryDefinition | null;\n  register(...definitions: any[]): any;\n}\n\nexport default class Registry implements RegistryInterface {\n  public static blots = new WeakMap<Node, Blot>();\n\n  public static find(node?: Node | null, bubble = false): Blot | null {\n    if (node == null) {\n      return null;\n    }\n    if (this.blots.has(node)) {\n      return this.blots.get(node) || null;\n    }\n    if (bubble) {\n      let parentNode: Node | null = null;\n      try {\n        parentNode = node.parentNode;\n      } catch (err) {\n        // Probably hit a permission denied error.\n        // A known case is in Firefox, event targets can be anonymous DIVs\n        // inside an input element.\n        // https://bugzilla.mozilla.org/show_bug.cgi?id=208427\n        return null;\n      }\n      return this.find(parentNode, bubble);\n    }\n    return null;\n  }\n\n  private attributes: { [key: string]: Attributor } = {};\n  private classes: { [key: string]: BlotConstructor } = {};\n  private tags: { [key: string]: BlotConstructor } = {};\n  private types: { [key: string]: RegistryDefinition } = {};\n\n  public create(scroll: Root, input: Node | string | Scope, value?: any): Blot {\n    const match = this.query(input);\n    if (match == null) {\n      throw new ParchmentError(`Unable to create ${input} blot`);\n    }\n    const blotClass = match as BlotConstructor;\n    const node =\n      // @ts-expect-error Fix me later\n      input instanceof Node || input.nodeType === Node.TEXT_NODE\n        ? input\n        : blotClass.create(value);\n\n    const blot = new blotClass(scroll, node as Node, value);\n    Registry.blots.set(blot.domNode, blot);\n    return blot;\n  }\n\n  public find(node: Node | null, bubble = false): Blot | null {\n    return Registry.find(node, bubble);\n  }\n\n  public query(\n    query: string | Node | Scope,\n    scope: Scope = Scope.ANY,\n  ): RegistryDefinition | null {\n    let match;\n    if (typeof query === 'string') {\n      match = this.types[query] || this.attributes[query];\n      // @ts-expect-error Fix me later\n    } else if (query instanceof Text || query.nodeType === Node.TEXT_NODE) {\n      match = this.types.text;\n    } else if (typeof query === 'number') {\n      if (query & Scope.LEVEL & Scope.BLOCK) {\n        match = this.types.block;\n      } else if (query & Scope.LEVEL & Scope.INLINE) {\n        match = this.types.inline;\n      }\n    } else if (query instanceof Element) {\n      const names = (query.getAttribute('class') || '').split(/\\s+/);\n      names.some((name) => {\n        match = this.classes[name];\n        if (match) {\n          return true;\n        }\n        return false;\n      });\n      match = match || this.tags[query.tagName];\n    }\n    if (match == null) {\n      return null;\n    }\n    if (\n      'scope' in match &&\n      scope & Scope.LEVEL & match.scope &&\n      scope & Scope.TYPE & match.scope\n    ) {\n      return match;\n    }\n    return null;\n  }\n\n  public register(...definitions: RegistryDefinition[]): RegistryDefinition[] {\n    return definitions.map((definition) => {\n      const isBlot = 'blotName' in definition;\n      const isAttr = 'attrName' in definition;\n      if (!isBlot && !isAttr) {\n        throw new ParchmentError('Invalid definition');\n      } else if (isBlot && definition.blotName === 'abstract') {\n        throw new ParchmentError('Cannot register abstract class');\n      }\n      const key = isBlot\n        ? definition.blotName\n        : isAttr\n          ? definition.attrName\n          : (undefined as never); // already handled by above checks\n      this.types[key] = definition;\n\n      if (isAttr) {\n        if (typeof definition.keyName === 'string') {\n          this.attributes[definition.keyName] = definition;\n        }\n      } else if (isBlot) {\n        if (definition.className) {\n          this.classes[definition.className] = definition;\n        }\n        if (definition.tagName) {\n          if (Array.isArray(definition.tagName)) {\n            definition.tagName = definition.tagName.map((tagName: string) => {\n              return tagName.toUpperCase();\n            });\n          } else {\n            definition.tagName = definition.tagName.toUpperCase();\n          }\n          const tagNames = Array.isArray(definition.tagName)\n            ? definition.tagName\n            : [definition.tagName];\n          tagNames.forEach((tag: string) => {\n            if (this.tags[tag] == null || definition.className == null) {\n              this.tags[tag] = definition;\n            }\n          });\n        }\n      }\n      return definition;\n    });\n  }\n}\n", "import Attributor from './attributor.js';\n\nfunction match(node: HTMLElement, prefix: string): string[] {\n  const className = node.getAttribute('class') || '';\n  return className\n    .split(/\\s+/)\n    .filter((name) => name.indexOf(`${prefix}-`) === 0);\n}\n\nclass ClassAttributor extends Attributor {\n  public static keys(node: HTMLElement): string[] {\n    return (node.getAttribute('class') || '')\n      .split(/\\s+/)\n      .map((name) => name.split('-').slice(0, -1).join('-'));\n  }\n\n  public add(node: HTMLElement, value: any): boolean {\n    if (!this.canAdd(node, value)) {\n      return false;\n    }\n    this.remove(node);\n    node.classList.add(`${this.keyName}-${value}`);\n    return true;\n  }\n\n  public remove(node: HTMLElement): void {\n    const matches = match(node, this.keyName);\n    matches.forEach((name) => {\n      node.classList.remove(name);\n    });\n    if (node.classList.length === 0) {\n      node.removeAttribute('class');\n    }\n  }\n\n  public value(node: HTMLElement): any {\n    const result = match(node, this.keyName)[0] || '';\n    const value = result.slice(this.keyName.length + 1); // +1 for hyphen\n    return this.canAdd(node, value) ? value : '';\n  }\n}\n\nexport default ClassAttributor;\n", "import Attributor from './attributor.js';\n\nfunction camelize(name: string): string {\n  const parts = name.split('-');\n  const rest = parts\n    .slice(1)\n    .map((part: string) => part[0].toUpperCase() + part.slice(1))\n    .join('');\n  return parts[0] + rest;\n}\n\nclass StyleAttributor extends Attributor {\n  public static keys(node: HTMLElement): string[] {\n    return (node.getAttribute('style') || '').split(';').map((value) => {\n      const arr = value.split(':');\n      return arr[0].trim();\n    });\n  }\n\n  public add(node: HTMLElement, value: any): boolean {\n    if (!this.canAdd(node, value)) {\n      return false;\n    }\n    // @ts-expect-error Fix me later\n    node.style[camelize(this.keyName)] = value;\n    return true;\n  }\n\n  public remove(node: HTMLElement): void {\n    // @ts-expect-error Fix me later\n    node.style[camelize(this.keyName)] = '';\n    if (!node.getAttribute('style')) {\n      node.removeAttribute('style');\n    }\n  }\n\n  public value(node: HTMLElement): any {\n    // @ts-expect-error Fix me later\n    const value = node.style[camelize(this.keyName)];\n    return this.canAdd(node, value) ? value : '';\n  }\n}\n\nexport default StyleAttributor;\n", "import type { Formattable } from '../blot/abstract/blot.js';\nimport Registry from '../registry.js';\nimport Scope from '../scope.js';\nimport Attributor from './attributor.js';\nimport ClassAttributor from './class.js';\nimport StyleAttributor from './style.js';\n\nclass AttributorStore {\n  private attributes: { [key: string]: Attributor } = {};\n  private domNode: HTMLElement;\n\n  constructor(domNode: HTMLElement) {\n    this.domNode = domNode;\n    this.build();\n  }\n\n  public attribute(attribute: Attributor, value: any): void {\n    // verb\n    if (value) {\n      if (attribute.add(this.domNode, value)) {\n        if (attribute.value(this.domNode) != null) {\n          this.attributes[attribute.attrName] = attribute;\n        } else {\n          delete this.attributes[attribute.attrName];\n        }\n      }\n    } else {\n      attribute.remove(this.domNode);\n      delete this.attributes[attribute.attrName];\n    }\n  }\n\n  public build(): void {\n    this.attributes = {};\n    const blot = Registry.find(this.domNode);\n    if (blot == null) {\n      return;\n    }\n    const attributes = Attributor.keys(this.domNode);\n    const classes = ClassAttributor.keys(this.domNode);\n    const styles = StyleAttributor.keys(this.domNode);\n    attributes\n      .concat(classes)\n      .concat(styles)\n      .forEach((name) => {\n        const attr = blot.scroll.query(name, Scope.ATTRIBUTE);\n        if (attr instanceof Attributor) {\n          this.attributes[attr.attrName] = attr;\n        }\n      });\n  }\n\n  public copy(target: Formattable): void {\n    Object.keys(this.attributes).forEach((key) => {\n      const value = this.attributes[key].value(this.domNode);\n      target.format(key, value);\n    });\n  }\n\n  public move(target: Formattable): void {\n    this.copy(target);\n    Object.keys(this.attributes).forEach((key) => {\n      this.attributes[key].remove(this.domNode);\n    });\n    this.attributes = {};\n  }\n\n  public values(): { [key: string]: any } {\n    return Object.keys(this.attributes).reduce(\n      (attributes: { [key: string]: any }, name: string) => {\n        attributes[name] = this.attributes[name].value(this.domNode);\n        return attributes;\n      },\n      {},\n    );\n  }\n}\n\nexport default AttributorStore;\n", "import ParchmentError from '../../error.js';\nimport Registry from '../../registry.js';\nimport Scope from '../../scope.js';\nimport type {\n  Blot,\n  BlotConstructor,\n  Formattable,\n  Parent,\n  Root,\n} from './blot.js';\n\nclass ShadowBlot implements Blot {\n  public static blotName = 'abstract';\n  public static className: string;\n  public static requiredContainer: BlotConstructor;\n  public static scope: Scope;\n  public static tagName: string | string[];\n\n  public static create(rawValue?: unknown): Node {\n    if (this.tagName == null) {\n      throw new ParchmentError('Blot definition missing tagName');\n    }\n    let node: HTMLElement;\n    let value: string | number | undefined;\n    if (Array.isArray(this.tagName)) {\n      if (typeof rawValue === 'string') {\n        value = rawValue.toUpperCase();\n        if (parseInt(value, 10).toString() === value) {\n          value = parseInt(value, 10);\n        }\n      } else if (typeof rawValue === 'number') {\n        value = rawValue;\n      }\n      if (typeof value === 'number') {\n        node = document.createElement(this.tagName[value - 1]);\n      } else if (value && this.tagName.indexOf(value) > -1) {\n        node = document.createElement(value);\n      } else {\n        node = document.createElement(this.tagName[0]);\n      }\n    } else {\n      node = document.createElement(this.tagName);\n    }\n    if (this.className) {\n      node.classList.add(this.className);\n    }\n    return node;\n  }\n\n  public prev: Blot | null;\n  public next: Blot | null;\n  // @ts-expect-error Fix me later\n  public parent: Parent;\n\n  // Hack for accessing inherited static methods\n  get statics(): any {\n    return this.constructor;\n  }\n  constructor(\n    public scroll: Root,\n    public domNode: Node,\n  ) {\n    Registry.blots.set(domNode, this);\n    this.prev = null;\n    this.next = null;\n  }\n\n  public attach(): void {\n    // Nothing to do\n  }\n\n  public clone(): Blot {\n    const domNode = this.domNode.cloneNode(false);\n    return this.scroll.create(domNode);\n  }\n\n  public detach(): void {\n    if (this.parent != null) {\n      this.parent.removeChild(this);\n    }\n    Registry.blots.delete(this.domNode);\n  }\n\n  public deleteAt(index: number, length: number): void {\n    const blot = this.isolate(index, length);\n    blot.remove();\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    const blot = this.isolate(index, length);\n    if (this.scroll.query(name, Scope.BLOT) != null && value) {\n      blot.wrap(name, value);\n    } else if (this.scroll.query(name, Scope.ATTRIBUTE) != null) {\n      const parent = this.scroll.create(this.statics.scope) as Parent &\n        Formattable;\n      blot.wrap(parent);\n      parent.format(name, value);\n    }\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    const blot =\n      def == null\n        ? this.scroll.create('text', value)\n        : this.scroll.create(value, def);\n    const ref = this.split(index);\n    this.parent.insertBefore(blot, ref || undefined);\n  }\n\n  public isolate(index: number, length: number): Blot {\n    const target = this.split(index);\n    if (target == null) {\n      throw new Error('Attempt to isolate at end');\n    }\n    target.split(length);\n    return target;\n  }\n\n  public length(): number {\n    return 1;\n  }\n\n  public offset(root: Blot = this.parent): number {\n    if (this.parent == null || this === root) {\n      return 0;\n    }\n    return this.parent.children.offset(this) + this.parent.offset(root);\n  }\n\n  public optimize(_context?: { [key: string]: any }): void {\n    if (\n      this.statics.requiredContainer &&\n      !(this.parent instanceof this.statics.requiredContainer)\n    ) {\n      this.wrap(this.statics.requiredContainer.blotName);\n    }\n  }\n\n  public remove(): void {\n    if (this.domNode.parentNode != null) {\n      this.domNode.parentNode.removeChild(this.domNode);\n    }\n    this.detach();\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement =\n      typeof name === 'string' ? this.scroll.create(name, value) : name;\n    if (this.parent != null) {\n      this.parent.insertBefore(replacement, this.next || undefined);\n      this.remove();\n    }\n    return replacement;\n  }\n\n  public split(index: number, _force?: boolean): Blot | null {\n    return index === 0 ? this : this.next;\n  }\n\n  public update(\n    _mutations: MutationRecord[],\n    _context: { [key: string]: any },\n  ): void {\n    // Nothing to do by default\n  }\n\n  public wrap(name: string | Parent, value?: any): Parent {\n    const wrapper =\n      typeof name === 'string'\n        ? (this.scroll.create(name, value) as Parent)\n        : name;\n    if (this.parent != null) {\n      this.parent.insertBefore(wrapper, this.next || undefined);\n    }\n    if (typeof wrapper.appendChild !== 'function') {\n      throw new ParchmentError(`Cannot wrap ${name}`);\n    }\n    wrapper.appendChild(this);\n    return wrapper;\n  }\n}\n\nexport default ShadowBlot;\n", "import Scope from '../../scope.js';\nimport type { Leaf } from './blot.js';\nimport ShadowBlot from './shadow.js';\n\nclass LeafBlot extends ShadowBlot implements Leaf {\n  public static scope = Scope.INLINE_BLOT;\n\n  /**\n   * Returns the value represented by domNode if it is this Blot's type\n   * No checking that domNode can represent this Blot type is required so\n   * applications needing it should check externally before calling.\n   */\n  public static value(_domNode: Node): any {\n    return true;\n  }\n\n  /**\n   * Given location represented by node and offset from DOM Selection Range,\n   * return index to that location.\n   */\n  public index(node: Node, offset: number): number {\n    if (\n      this.domNode === node ||\n      this.domNode.compareDocumentPosition(node) &\n        Node.DOCUMENT_POSITION_CONTAINED_BY\n    ) {\n      return Math.min(offset, 1);\n    }\n    return -1;\n  }\n\n  /**\n   * Given index to location within blot, return node and offset representing\n   * that location, consumable by DOM Selection Range\n   */\n  public position(index: number, _inclusive?: boolean): [Node, number] {\n    const childNodes: Node[] = Array.from(this.parent.domNode.childNodes);\n    let offset = childNodes.indexOf(this.domNode);\n    if (index > 0) {\n      offset += 1;\n    }\n    return [this.parent.domNode, offset];\n  }\n\n  /**\n   * Return value represented by this blot\n   * Should not change without interaction from API or\n   * user change detectable by update()\n   */\n  public value(): any {\n    return {\n      [this.statics.blotName]: this.statics.value(this.domNode) || true,\n    };\n  }\n}\n\nexport default LeafBlot;\n", "import type LinkedNode from './linked-node.js';\n\nclass LinkedList<T extends LinkedNode> {\n  public head: T | null;\n  public tail: T | null;\n  public length: number;\n\n  constructor() {\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  public append(...nodes: T[]): void {\n    this.insertBefore(nodes[0], null);\n    if (nodes.length > 1) {\n      const rest = nodes.slice(1);\n      this.append(...rest);\n    }\n  }\n\n  public at(index: number): T | null {\n    const next = this.iterator();\n    let cur = next();\n    while (cur && index > 0) {\n      index -= 1;\n      cur = next();\n    }\n    return cur;\n  }\n\n  public contains(node: T): boolean {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      if (cur === node) {\n        return true;\n      }\n      cur = next();\n    }\n    return false;\n  }\n\n  public indexOf(node: T): number {\n    const next = this.iterator();\n    let cur = next();\n    let index = 0;\n    while (cur) {\n      if (cur === node) {\n        return index;\n      }\n      index += 1;\n      cur = next();\n    }\n    return -1;\n  }\n\n  public insertBefore(node: T | null, refNode: T | null): void {\n    if (node == null) {\n      return;\n    }\n    this.remove(node);\n    node.next = refNode;\n    if (refNode != null) {\n      node.prev = refNode.prev;\n      if (refNode.prev != null) {\n        refNode.prev.next = node;\n      }\n      refNode.prev = node;\n      if (refNode === this.head) {\n        this.head = node;\n      }\n    } else if (this.tail != null) {\n      this.tail.next = node;\n      node.prev = this.tail;\n      this.tail = node;\n    } else {\n      node.prev = null;\n      this.head = this.tail = node;\n    }\n    this.length += 1;\n  }\n\n  public offset(target: T): number {\n    let index = 0;\n    let cur = this.head;\n    while (cur != null) {\n      if (cur === target) {\n        return index;\n      }\n      index += cur.length();\n      cur = cur.next as T;\n    }\n    return -1;\n  }\n\n  public remove(node: T): void {\n    if (!this.contains(node)) {\n      return;\n    }\n    if (node.prev != null) {\n      node.prev.next = node.next;\n    }\n    if (node.next != null) {\n      node.next.prev = node.prev;\n    }\n    if (node === this.head) {\n      this.head = node.next as T;\n    }\n    if (node === this.tail) {\n      this.tail = node.prev as T;\n    }\n    this.length -= 1;\n  }\n\n  public iterator(curNode: T | null = this.head): () => T | null {\n    // TODO use yield when we can\n    return (): T | null => {\n      const ret = curNode;\n      if (curNode != null) {\n        curNode = curNode.next as T;\n      }\n      return ret;\n    };\n  }\n\n  public find(index: number, inclusive = false): [T | null, number] {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      const length = cur.length();\n      if (\n        index < length ||\n        (inclusive &&\n          index === length &&\n          (cur.next == null || cur.next.length() !== 0))\n      ) {\n        return [cur, index];\n      }\n      index -= length;\n      cur = next();\n    }\n    return [null, 0];\n  }\n\n  public forEach(callback: (cur: T) => void): void {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      callback(cur);\n      cur = next();\n    }\n  }\n\n  public forEachAt(\n    index: number,\n    length: number,\n    callback: (cur: T, offset: number, length: number) => void,\n  ): void {\n    if (length <= 0) {\n      return;\n    }\n    const [startNode, offset] = this.find(index);\n    let curIndex = index - offset;\n    const next = this.iterator(startNode);\n    let cur = next();\n    while (cur && curIndex < index + length) {\n      const curLength = cur.length();\n      if (index > curIndex) {\n        callback(\n          cur,\n          index - curIndex,\n          Math.min(length, curIndex + curLength - index),\n        );\n      } else {\n        callback(cur, 0, Math.min(curLength, index + length - curIndex));\n      }\n      curIndex += curLength;\n      cur = next();\n    }\n  }\n\n  public map(callback: (cur: T) => any): any[] {\n    return this.reduce((memo: T[], cur: T) => {\n      memo.push(callback(cur));\n      return memo;\n    }, []);\n  }\n\n  public reduce<M>(callback: (memo: M, cur: T) => M, memo: M): M {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      memo = callback(memo, cur);\n      cur = next();\n    }\n    return memo;\n  }\n}\n\nexport default LinkedList;\n", "import LinkedList from '../../collection/linked-list.js';\nimport ParchmentError from '../../error.js';\nimport Scope from '../../scope.js';\nimport type { Blot, BlotConstructor, Parent, Root } from './blot.js';\nimport ShadowBlot from './shadow.js';\n\nfunction makeAttachedBlot(node: Node, scroll: Root): Blot {\n  const found = scroll.find(node);\n  if (found) return found;\n  try {\n    return scroll.create(node);\n  } catch (e) {\n    const blot = scroll.create(Scope.INLINE);\n    Array.from(node.childNodes).forEach((child: Node) => {\n      blot.domNode.appendChild(child);\n    });\n    if (node.parentNode) {\n      node.parentNode.replaceChild(blot.domNode, node);\n    }\n    blot.attach();\n    return blot;\n  }\n}\n\nclass ParentBlot extends ShadowBlot implements Parent {\n  /**\n   * Whitelist array of Blots that can be direct children.\n   */\n  public static allowedChildren?: BlotConstructor[];\n\n  /**\n   * Default child blot to be inserted if this blot becomes empty.\n   */\n  public static defaultChild?: BlotConstructor;\n  public static uiClass = '';\n\n  public children!: LinkedList<Blot>;\n  public domNode!: HTMLElement;\n  public uiNode: HTMLElement | null = null;\n\n  constructor(scroll: Root, domNode: Node) {\n    super(scroll, domNode);\n    this.build();\n  }\n\n  public appendChild(other: Blot): void {\n    this.insertBefore(other);\n  }\n\n  public attach(): void {\n    super.attach();\n    this.children.forEach((child) => {\n      child.attach();\n    });\n  }\n\n  public attachUI(node: HTMLElement): void {\n    if (this.uiNode != null) {\n      this.uiNode.remove();\n    }\n    this.uiNode = node;\n    if (ParentBlot.uiClass) {\n      this.uiNode.classList.add(ParentBlot.uiClass);\n    }\n    this.uiNode.setAttribute('contenteditable', 'false');\n    this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n  }\n\n  /**\n   * Called during construction, should fill its own children LinkedList.\n   */\n  public build(): void {\n    this.children = new LinkedList<Blot>();\n    // Need to be reversed for if DOM nodes already in order\n    Array.from(this.domNode.childNodes)\n      .filter((node: Node) => node !== this.uiNode)\n      .reverse()\n      .forEach((node: Node) => {\n        try {\n          const child = makeAttachedBlot(node, this.scroll);\n          this.insertBefore(child, this.children.head || undefined);\n        } catch (err) {\n          if (err instanceof ParchmentError) {\n            return;\n          } else {\n            throw err;\n          }\n        }\n      });\n  }\n\n  public deleteAt(index: number, length: number): void {\n    if (index === 0 && length === this.length()) {\n      return this.remove();\n    }\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.deleteAt(offset, childLength);\n    });\n  }\n\n  public descendant<T extends Blot>(\n    criteria: new (...args: any[]) => T,\n    index: number,\n  ): [T | null, number];\n  public descendant(\n    criteria: (blot: Blot) => boolean,\n    index: number,\n  ): [Blot | null, number];\n  public descendant(criteria: any, index = 0): [Blot | null, number] {\n    const [child, offset] = this.children.find(index);\n    if (\n      (criteria.blotName == null && criteria(child)) ||\n      (criteria.blotName != null && child instanceof criteria)\n    ) {\n      return [child as any, offset];\n    } else if (child instanceof ParentBlot) {\n      return child.descendant(criteria, offset);\n    } else {\n      return [null, -1];\n    }\n  }\n\n  public descendants<T extends Blot>(\n    criteria: new (...args: any[]) => T,\n    index?: number,\n    length?: number,\n  ): T[];\n  public descendants(\n    criteria: (blot: Blot) => boolean,\n    index?: number,\n    length?: number,\n  ): Blot[];\n  public descendants(\n    criteria: any,\n    index = 0,\n    length: number = Number.MAX_VALUE,\n  ): Blot[] {\n    let descendants: Blot[] = [];\n    let lengthLeft = length;\n    this.children.forEachAt(\n      index,\n      length,\n      (child: Blot, childIndex: number, childLength: number) => {\n        if (\n          (criteria.blotName == null && criteria(child)) ||\n          (criteria.blotName != null && child instanceof criteria)\n        ) {\n          descendants.push(child);\n        }\n        if (child instanceof ParentBlot) {\n          descendants = descendants.concat(\n            child.descendants(criteria, childIndex, lengthLeft),\n          );\n        }\n        lengthLeft -= childLength;\n      },\n    );\n    return descendants;\n  }\n\n  public detach(): void {\n    this.children.forEach((child) => {\n      child.detach();\n    });\n    super.detach();\n  }\n\n  public enforceAllowedChildren(): void {\n    let done = false;\n    this.children.forEach((child: Blot) => {\n      if (done) {\n        return;\n      }\n      const allowed = this.statics.allowedChildren.some(\n        (def: BlotConstructor) => child instanceof def,\n      );\n      if (allowed) {\n        return;\n      }\n      if (child.statics.scope === Scope.BLOCK_BLOT) {\n        if (child.next != null) {\n          this.splitAfter(child);\n        }\n        if (child.prev != null) {\n          this.splitAfter(child.prev);\n        }\n        child.parent.unwrap();\n        done = true;\n      } else if (child instanceof ParentBlot) {\n        child.unwrap();\n      } else {\n        child.remove();\n      }\n    });\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.formatAt(offset, childLength, name, value);\n    });\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    const [child, offset] = this.children.find(index);\n    if (child) {\n      child.insertAt(offset, value, def);\n    } else {\n      const blot =\n        def == null\n          ? this.scroll.create('text', value)\n          : this.scroll.create(value, def);\n      this.appendChild(blot);\n    }\n  }\n\n  public insertBefore(childBlot: Blot, refBlot?: Blot | null): void {\n    if (childBlot.parent != null) {\n      childBlot.parent.children.remove(childBlot);\n    }\n    let refDomNode: Node | null = null;\n    this.children.insertBefore(childBlot, refBlot || null);\n    childBlot.parent = this;\n    if (refBlot != null) {\n      refDomNode = refBlot.domNode;\n    }\n    if (\n      this.domNode.parentNode !== childBlot.domNode ||\n      this.domNode.nextSibling !== refDomNode\n    ) {\n      this.domNode.insertBefore(childBlot.domNode, refDomNode);\n    }\n    childBlot.attach();\n  }\n\n  public length(): number {\n    return this.children.reduce((memo, child) => {\n      return memo + child.length();\n    }, 0);\n  }\n\n  public moveChildren(targetParent: Parent, refNode?: Blot | null): void {\n    this.children.forEach((child) => {\n      targetParent.insertBefore(child, refNode);\n    });\n  }\n\n  public optimize(context?: { [key: string]: any }): void {\n    super.optimize(context);\n    this.enforceAllowedChildren();\n    if (this.uiNode != null && this.uiNode !== this.domNode.firstChild) {\n      this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n    }\n    if (this.children.length === 0) {\n      if (this.statics.defaultChild != null) {\n        const child = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(child);\n        // TODO double check if necessary\n        // child.optimize(context);\n      } else {\n        this.remove();\n      }\n    }\n  }\n\n  public path(index: number, inclusive = false): [Blot, number][] {\n    const [child, offset] = this.children.find(index, inclusive);\n    const position: [Blot, number][] = [[this, index]];\n    if (child instanceof ParentBlot) {\n      return position.concat(child.path(offset, inclusive));\n    } else if (child != null) {\n      position.push([child, offset]);\n    }\n    return position;\n  }\n\n  public removeChild(child: Blot): void {\n    this.children.remove(child);\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement =\n      typeof name === 'string' ? this.scroll.create(name, value) : name;\n    if (replacement instanceof ParentBlot) {\n      this.moveChildren(replacement);\n    }\n    return super.replaceWith(replacement);\n  }\n\n  public split(index: number, force = false): Blot | null {\n    if (!force) {\n      if (index === 0) {\n        return this;\n      }\n      if (index === this.length()) {\n        return this.next;\n      }\n    }\n    const after = this.clone() as ParentBlot;\n    if (this.parent) {\n      this.parent.insertBefore(after, this.next || undefined);\n    }\n    this.children.forEachAt(index, this.length(), (child, offset, _length) => {\n      const split = child.split(offset, force);\n      if (split != null) {\n        after.appendChild(split);\n      }\n    });\n    return after;\n  }\n\n  public splitAfter(child: Blot): Parent {\n    const after = this.clone() as ParentBlot;\n    while (child.next != null) {\n      after.appendChild(child.next);\n    }\n    if (this.parent) {\n      this.parent.insertBefore(after, this.next || undefined);\n    }\n    return after;\n  }\n\n  public unwrap(): void {\n    if (this.parent) {\n      this.moveChildren(this.parent, this.next || undefined);\n    }\n    this.remove();\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    _context: { [key: string]: any },\n  ): void {\n    const addedNodes: Node[] = [];\n    const removedNodes: Node[] = [];\n    mutations.forEach((mutation) => {\n      if (mutation.target === this.domNode && mutation.type === 'childList') {\n        addedNodes.push(...mutation.addedNodes);\n        removedNodes.push(...mutation.removedNodes);\n      }\n    });\n    removedNodes.forEach((node: Node) => {\n      // Check node has actually been removed\n      // One exception is Chrome does not immediately remove IFRAMEs\n      // from DOM but MutationRecord is correct in its reported removal\n      if (\n        node.parentNode != null &&\n        // @ts-expect-error Fix me later\n        node.tagName !== 'IFRAME' &&\n        document.body.compareDocumentPosition(node) &\n          Node.DOCUMENT_POSITION_CONTAINED_BY\n      ) {\n        return;\n      }\n      const blot = this.scroll.find(node);\n      if (blot == null) {\n        return;\n      }\n      if (\n        blot.domNode.parentNode == null ||\n        blot.domNode.parentNode === this.domNode\n      ) {\n        blot.detach();\n      }\n    });\n    addedNodes\n      .filter((node) => {\n        return node.parentNode === this.domNode && node !== this.uiNode;\n      })\n      .sort((a, b) => {\n        if (a === b) {\n          return 0;\n        }\n        if (a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING) {\n          return 1;\n        }\n        return -1;\n      })\n      .forEach((node) => {\n        let refBlot: Blot | null = null;\n        if (node.nextSibling != null) {\n          refBlot = this.scroll.find(node.nextSibling);\n        }\n        const blot = makeAttachedBlot(node, this.scroll);\n        if (blot.next !== refBlot || blot.next == null) {\n          if (blot.parent != null) {\n            blot.parent.removeChild(this);\n          }\n          this.insertBefore(blot, refBlot || undefined);\n        }\n      });\n    this.enforceAllowedChildren();\n  }\n}\n\nexport default ParentBlot;\n", "import Attributor from '../attributor/attributor.js';\nimport AttributorStore from '../attributor/store.js';\nimport Scope from '../scope.js';\nimport type {\n  Blot,\n  BlotConstructor,\n  Formattable,\n  Parent,\n  Root,\n} from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\nimport ParentBlot from './abstract/parent.js';\n\n// Shallow object comparison\nfunction isEqual(\n  obj1: Record<string, unknown>,\n  obj2: Record<string, unknown>,\n): boolean {\n  if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n    return false;\n  }\n  for (const prop in obj1) {\n    if (obj1[prop] !== obj2[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nclass InlineBlot extends ParentBlot implements Formattable {\n  public static allowedChildren: BlotConstructor[] = [InlineBlot, LeafBlot];\n  public static blotName = 'inline';\n  public static scope = Scope.INLINE_BLOT;\n  public static tagName: string | string[] = 'SPAN';\n\n  static create(value?: unknown) {\n    return super.create(value) as HTMLElement;\n  }\n\n  public static formats(domNode: HTMLElement, scroll: Root): any {\n    const match = scroll.query(InlineBlot.blotName);\n    if (\n      match != null &&\n      domNode.tagName === (match as BlotConstructor).tagName\n    ) {\n      return undefined;\n    } else if (typeof this.tagName === 'string') {\n      return true;\n    } else if (Array.isArray(this.tagName)) {\n      return domNode.tagName.toLowerCase();\n    }\n    return undefined;\n  }\n\n  protected attributes: AttributorStore;\n\n  constructor(scroll: Root, domNode: Node) {\n    super(scroll, domNode);\n    this.attributes = new AttributorStore(this.domNode);\n  }\n\n  public format(name: string, value: any): void {\n    if (name === this.statics.blotName && !value) {\n      this.children.forEach((child) => {\n        if (!(child instanceof InlineBlot)) {\n          child = child.wrap(InlineBlot.blotName, true);\n        }\n        this.attributes.copy(child as InlineBlot);\n      });\n      this.unwrap();\n    } else {\n      const format = this.scroll.query(name, Scope.INLINE);\n      if (format == null) {\n        return;\n      }\n      if (format instanceof Attributor) {\n        this.attributes.attribute(format, value);\n      } else if (\n        value &&\n        (name !== this.statics.blotName || this.formats()[name] !== value)\n      ) {\n        this.replaceWith(name, value);\n      }\n    }\n  }\n\n  public formats(): { [index: string]: any } {\n    const formats = this.attributes.values();\n    const format = this.statics.formats(this.domNode, this.scroll);\n    if (format != null) {\n      formats[this.statics.blotName] = format;\n    }\n    return formats;\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    if (\n      this.formats()[name] != null ||\n      this.scroll.query(name, Scope.ATTRIBUTE)\n    ) {\n      const blot = this.isolate(index, length) as InlineBlot;\n      blot.format(name, value);\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  public optimize(context: { [key: string]: any }): void {\n    super.optimize(context);\n    const formats = this.formats();\n    if (Object.keys(formats).length === 0) {\n      return this.unwrap(); // unformatted span\n    }\n    const next = this.next;\n    if (\n      next instanceof InlineBlot &&\n      next.prev === this &&\n      isEqual(formats, next.formats())\n    ) {\n      next.moveChildren(this);\n      next.remove();\n    }\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement = super.replaceWith(name, value) as InlineBlot;\n    this.attributes.copy(replacement);\n    return replacement;\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    context: { [key: string]: any },\n  ): void {\n    super.update(mutations, context);\n    const attributeChanged = mutations.some(\n      (mutation) =>\n        mutation.target === this.domNode && mutation.type === 'attributes',\n    );\n    if (attributeChanged) {\n      this.attributes.build();\n    }\n  }\n\n  public wrap(name: string | Parent, value?: any): Parent {\n    const wrapper = super.wrap(name, value);\n    if (wrapper instanceof InlineBlot) {\n      this.attributes.move(wrapper);\n    }\n    return wrapper;\n  }\n}\n\nexport default InlineBlot;\n", "import Attributor from '../attributor/attributor.js';\nimport AttributorStore from '../attributor/store.js';\nimport Scope from '../scope.js';\nimport type {\n  Blot,\n  BlotConstructor,\n  Formattable,\n  Root,\n} from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\nimport ParentBlot from './abstract/parent.js';\nimport InlineBlot from './inline.js';\n\nclass BlockBlot extends ParentBlot implements Formattable {\n  public static blotName = 'block';\n  public static scope = Scope.BLOCK_BLOT;\n  public static tagName: string | string[] = 'P';\n  public static allowedChildren: BlotConstructor[] = [\n    InlineBlot,\n    BlockBlot,\n    LeafBlot,\n  ];\n\n  static create(value?: unknown) {\n    return super.create(value) as HTMLElement;\n  }\n\n  public static formats(domNode: HTMLElement, scroll: Root): any {\n    const match = scroll.query(BlockBlot.blotName);\n    if (\n      match != null &&\n      domNode.tagName === (match as BlotConstructor).tagName\n    ) {\n      return undefined;\n    } else if (typeof this.tagName === 'string') {\n      return true;\n    } else if (Array.isArray(this.tagName)) {\n      return domNode.tagName.toLowerCase();\n    }\n  }\n\n  protected attributes: AttributorStore;\n\n  constructor(scroll: Root, domNode: Node) {\n    super(scroll, domNode);\n    this.attributes = new AttributorStore(this.domNode);\n  }\n\n  public format(name: string, value: any): void {\n    const format = this.scroll.query(name, Scope.BLOCK);\n    if (format == null) {\n      return;\n    } else if (format instanceof Attributor) {\n      this.attributes.attribute(format, value);\n    } else if (name === this.statics.blotName && !value) {\n      this.replaceWith(BlockBlot.blotName);\n    } else if (\n      value &&\n      (name !== this.statics.blotName || this.formats()[name] !== value)\n    ) {\n      this.replaceWith(name, value);\n    }\n  }\n\n  public formats(): { [index: string]: any } {\n    const formats = this.attributes.values();\n    const format = this.statics.formats(this.domNode, this.scroll);\n    if (format != null) {\n      formats[this.statics.blotName] = format;\n    }\n    return formats;\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    if (this.scroll.query(name, Scope.BLOCK) != null) {\n      this.format(name, value);\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    if (def == null || this.scroll.query(value, Scope.INLINE) != null) {\n      // Insert text or inline\n      super.insertAt(index, value, def);\n    } else {\n      const after = this.split(index);\n      if (after != null) {\n        const blot = this.scroll.create(value, def);\n        after.parent.insertBefore(blot, after);\n      } else {\n        throw new Error('Attempt to insertAt after block boundaries');\n      }\n    }\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement = super.replaceWith(name, value) as BlockBlot;\n    this.attributes.copy(replacement);\n    return replacement;\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    context: { [key: string]: any },\n  ): void {\n    super.update(mutations, context);\n    const attributeChanged = mutations.some(\n      (mutation) =>\n        mutation.target === this.domNode && mutation.type === 'attributes',\n    );\n    if (attributeChanged) {\n      this.attributes.build();\n    }\n  }\n}\n\nexport default BlockBlot;\n", "import Scope from '../../scope.js';\nimport BlockBlot from '../block.js';\nimport ParentBlot from './parent.js';\n\nclass ContainerBlot extends ParentBlot {\n  public static blotName = 'container';\n  public static scope = Scope.BLOCK_BLOT;\n  public static tagName: string | string[];\n\n  public prev!: BlockBlot | ContainerBlot | null;\n  public next!: BlockBlot | ContainerBlot | null;\n\n  public checkMerge(): boolean {\n    return (\n      this.next !== null && this.next.statics.blotName === this.statics.blotName\n    );\n  }\n\n  public deleteAt(index: number, length: number): void {\n    super.deleteAt(index, length);\n    this.enforceAllowedChildren();\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    super.formatAt(index, length, name, value);\n    this.enforceAllowedChildren();\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    super.insertAt(index, value, def);\n    this.enforceAllowedChildren();\n  }\n\n  public optimize(context: { [key: string]: any }): void {\n    super.optimize(context);\n    if (this.children.length > 0 && this.next != null && this.checkMerge()) {\n      this.next.moveChildren(this);\n      this.next.remove();\n    }\n  }\n}\n\nexport default ContainerBlot;\n", "import type { Formattable, Root } from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\n\nclass EmbedBlot extends LeafBlot implements Formattable {\n  public static formats(_domNode: HTMLElement, _scroll: Root): any {\n    return undefined;\n  }\n\n  public format(name: string, value: any): void {\n    // super.formatAt wraps, which is what we want in general,\n    // but this allows subclasses to overwrite for formats\n    // that just apply to particular embeds\n    super.formatAt(0, this.length(), name, value);\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    if (index === 0 && length === this.length()) {\n      this.format(name, value);\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  public formats(): { [index: string]: any } {\n    return this.statics.formats(this.domNode, this.scroll);\n  }\n}\n\nexport default EmbedBlot;\n", "import Registry, { type RegistryDefinition } from '../registry.js';\nimport Scope from '../scope.js';\nimport type { Blot, BlotConstructor, Root } from './abstract/blot.js';\nimport ContainerBlot from './abstract/container.js';\nimport ParentBlot from './abstract/parent.js';\nimport BlockBlot from './block.js';\n\nconst OBSERVER_CONFIG = {\n  attributes: true,\n  characterData: true,\n  characterDataOldValue: true,\n  childList: true,\n  subtree: true,\n};\n\nconst MAX_OPTIMIZE_ITERATIONS = 100;\n\nclass ScrollBlot extends ParentBlot implements Root {\n  public static blotName = 'scroll';\n  public static defaultChild = BlockBlot;\n  public static allowedChildren: BlotConstructor[] = [BlockBlot, ContainerBlot];\n  public static scope = Scope.BLOCK_BLOT;\n  public static tagName = 'DIV';\n\n  public observer: MutationObserver;\n\n  constructor(\n    public registry: Registry,\n    node: HTMLDivElement,\n  ) {\n    // @ts-expect-error scroll is the root with no parent\n    super(null, node);\n    this.scroll = this;\n    this.build();\n    this.observer = new MutationObserver((mutations: MutationRecord[]) => {\n      this.update(mutations);\n    });\n    this.observer.observe(this.domNode, OBSERVER_CONFIG);\n    this.attach();\n  }\n\n  public create(input: Node | string | Scope, value?: any): Blot {\n    return this.registry.create(this, input, value);\n  }\n\n  public find(node: Node | null, bubble = false): Blot | null {\n    const blot = this.registry.find(node, bubble);\n    if (!blot) {\n      return null;\n    }\n    if (blot.scroll === this) {\n      return blot;\n    }\n    return bubble ? this.find(blot.scroll.domNode.parentNode, true) : null;\n  }\n\n  public query(\n    query: string | Node | Scope,\n    scope: Scope = Scope.ANY,\n  ): RegistryDefinition | null {\n    return this.registry.query(query, scope);\n  }\n\n  public register(...definitions: RegistryDefinition[]) {\n    return this.registry.register(...definitions);\n  }\n\n  public build(): void {\n    if (this.scroll == null) {\n      return;\n    }\n    super.build();\n  }\n\n  public detach(): void {\n    super.detach();\n    this.observer.disconnect();\n  }\n\n  public deleteAt(index: number, length: number): void {\n    this.update();\n    if (index === 0 && length === this.length()) {\n      this.children.forEach((child) => {\n        child.remove();\n      });\n    } else {\n      super.deleteAt(index, length);\n    }\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    this.update();\n    super.formatAt(index, length, name, value);\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    this.update();\n    super.insertAt(index, value, def);\n  }\n\n  public optimize(context?: { [key: string]: any }): void;\n  public optimize(\n    mutations: MutationRecord[],\n    context: { [key: string]: any },\n  ): void;\n  public optimize(mutations: any = [], context: any = {}): void {\n    super.optimize(context);\n    const mutationsMap = context.mutationsMap || new WeakMap();\n    // We must modify mutations directly, cannot make copy and then modify\n    let records = Array.from(this.observer.takeRecords());\n    // Array.push currently seems to be implemented by a non-tail recursive function\n    // so we cannot just mutations.push.apply(mutations, this.observer.takeRecords());\n    while (records.length > 0) {\n      mutations.push(records.pop());\n    }\n    const mark = (blot: Blot | null, markParent = true): void => {\n      if (blot == null || blot === this) {\n        return;\n      }\n      if (blot.domNode.parentNode == null) {\n        return;\n      }\n      if (!mutationsMap.has(blot.domNode)) {\n        mutationsMap.set(blot.domNode, []);\n      }\n      if (markParent) {\n        mark(blot.parent);\n      }\n    };\n    const optimize = (blot: Blot): void => {\n      // Post-order traversal\n      if (!mutationsMap.has(blot.domNode)) {\n        return;\n      }\n      if (blot instanceof ParentBlot) {\n        blot.children.forEach(optimize);\n      }\n      mutationsMap.delete(blot.domNode);\n      blot.optimize(context);\n    };\n    let remaining = mutations;\n    for (let i = 0; remaining.length > 0; i += 1) {\n      if (i >= MAX_OPTIMIZE_ITERATIONS) {\n        throw new Error('[Parchment] Maximum optimize iterations reached');\n      }\n      remaining.forEach((mutation: MutationRecord) => {\n        const blot = this.find(mutation.target, true);\n        if (blot == null) {\n          return;\n        }\n        if (blot.domNode === mutation.target) {\n          if (mutation.type === 'childList') {\n            mark(this.find(mutation.previousSibling, false));\n            Array.from(mutation.addedNodes).forEach((node: Node) => {\n              const child = this.find(node, false);\n              mark(child, false);\n              if (child instanceof ParentBlot) {\n                child.children.forEach((grandChild: Blot) => {\n                  mark(grandChild, false);\n                });\n              }\n            });\n          } else if (mutation.type === 'attributes') {\n            mark(blot.prev);\n          }\n        }\n        mark(blot);\n      });\n      this.children.forEach(optimize);\n      remaining = Array.from(this.observer.takeRecords());\n      records = remaining.slice();\n      while (records.length > 0) {\n        mutations.push(records.pop());\n      }\n    }\n  }\n\n  public update(\n    mutations?: MutationRecord[],\n    context: { [key: string]: any } = {},\n  ): void {\n    mutations = mutations || this.observer.takeRecords();\n    const mutationsMap = new WeakMap();\n    mutations\n      .map((mutation: MutationRecord) => {\n        const blot = this.find(mutation.target, true);\n        if (blot == null) {\n          return null;\n        }\n        if (mutationsMap.has(blot.domNode)) {\n          mutationsMap.get(blot.domNode).push(mutation);\n          return null;\n        } else {\n          mutationsMap.set(blot.domNode, [mutation]);\n          return blot;\n        }\n      })\n      .forEach((blot: Blot | null) => {\n        if (blot != null && blot !== this && mutationsMap.has(blot.domNode)) {\n          blot.update(mutationsMap.get(blot.domNode) || [], context);\n        }\n      });\n    context.mutationsMap = mutationsMap;\n    if (mutationsMap.has(this.domNode)) {\n      super.update(mutationsMap.get(this.domNode), context);\n    }\n    this.optimize(mutations, context);\n  }\n}\n\nexport default ScrollBlot;\n", "import Scope from '../scope.js';\nimport type { Blot, Leaf, Root } from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\n\nclass TextBlot extends LeafBlot implements Leaf {\n  public static readonly blotName = 'text';\n  public static scope = Scope.INLINE_BLOT;\n\n  public static create(value: string): Text {\n    return document.createTextNode(value);\n  }\n\n  public static value(domNode: Text): string {\n    return domNode.data;\n  }\n\n  public domNode!: Text;\n  protected text: string;\n\n  constructor(scroll: Root, node: Node) {\n    super(scroll, node);\n    this.text = this.statics.value(this.domNode);\n  }\n\n  public deleteAt(index: number, length: number): void {\n    this.domNode.data = this.text =\n      this.text.slice(0, index) + this.text.slice(index + length);\n  }\n\n  public index(node: Node, offset: number): number {\n    if (this.domNode === node) {\n      return offset;\n    }\n    return -1;\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    if (def == null) {\n      this.text = this.text.slice(0, index) + value + this.text.slice(index);\n      this.domNode.data = this.text;\n    } else {\n      super.insertAt(index, value, def);\n    }\n  }\n\n  public length(): number {\n    return this.text.length;\n  }\n\n  public optimize(context: { [key: string]: any }): void {\n    super.optimize(context);\n    this.text = this.statics.value(this.domNode);\n    if (this.text.length === 0) {\n      this.remove();\n    } else if (this.next instanceof TextBlot && this.next.prev === this) {\n      this.insertAt(this.length(), (this.next as TextBlot).value());\n      this.next.remove();\n    }\n  }\n\n  public position(index: number, _inclusive = false): [Node, number] {\n    return [this.domNode, index];\n  }\n\n  public split(index: number, force = false): Blot | null {\n    if (!force) {\n      if (index === 0) {\n        return this;\n      }\n      if (index === this.length()) {\n        return this.next;\n      }\n    }\n    const after = this.scroll.create(this.domNode.splitText(index));\n    this.parent.insertBefore(after, this.next || undefined);\n    this.text = this.statics.value(this.domNode);\n    return after;\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    _context: { [key: string]: any },\n  ): void {\n    if (\n      mutations.some((mutation) => {\n        return (\n          mutation.type === 'characterData' && mutation.target === this.domNode\n        );\n      })\n    ) {\n      this.text = this.statics.value(this.domNode);\n    }\n  }\n\n  public value(): string {\n    return this.text;\n  }\n}\n\nexport default TextBlot;\n"], "names": ["<PERSON><PERSON>", "match", "ClassAttributor", "StyleAttributor", "ParentBlot", "AttributorStore", "LeafBlot", "InlineBlot", "BlockBlot", "ContainerBlot"], "mappings": "AAAK,IAAA,0BAAAA,YACHA,OAAAA,OAAA,OAAQ,CAAR,IAAA,QACAA,OAAAA,OAAA,QAAU,EAAV,IAAA,SAEAA,OAAAA,OAAA,YAAa,EAAb,IAAA,aACAA,OAAAA,OAAA,OAAQ,EAAR,IAAA,QACAA,OAAAA,OAAA,SAAU,CAAV,IAAA,UACAA,OAAAA,OAAA,QAAS,EAAT,IAAA,SAEAA,OAAAA,OAAA,aAAa,EAAb,IAAA,cACAA,OAAAA,OAAA,cAAc,CAAd,IAAA,eACAA,OAAAA,OAAA,kBAAkB,CAAlB,IAAA,mBACAA,OAAAA,OAAA,mBAAmB,CAAnB,IAAA,oBAEAA,OAAAA,OAAA,MAAM,EAAN,IAAA,OAdGA,SAAA,SAAA,CAAA,CAAA;ACOL,MAAqB,WAAW;AAAA,EAQ9B,YACkB,UACA,SAChB,UAA6B,CAAA,GAC7B;AAHgB,SAAA,WAAA,UACA,KAAA,UAAA;AAGV,UAAA,eAAe,MAAM,OAAO,MAAM;AACnC,SAAA,QACH,QAAQ,SAAS;AAAA;AAAA,MAEZ,QAAQ,QAAQ,MAAM,QAAS;AAAA,QAChC,MAAM,WACR,QAAQ,aAAa,SACvB,KAAK,YAAY,QAAQ;AAAA,EAE7B;AAAA,EArBA,OAAc,KAAK,MAA6B;AACvC,WAAA,MAAM,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,SAAe,KAAK,IAAI;AAAA,EAClE;AAAA,EAqBO,IAAI,MAAmB,OAAqB;AACjD,WAAK,KAAK,OAAO,MAAM,KAAK,KAGvB,KAAA,aAAa,KAAK,SAAS,KAAK,GAC9B,MAHE;AAAA,EAIX;AAAA,EAEO,OAAO,OAAoB,OAAqB;AACjD,WAAA,KAAK,aAAa,OACb,KAEL,OAAO,SAAU,WACZ,KAAK,UAAU,QAAQ,MAAM,QAAQ,SAAS,EAAE,CAAC,IAAI,KAErD,KAAK,UAAU,QAAQ,KAAK,IAAI;AAAA,EAE3C;AAAA,EAEO,OAAO,MAAyB;AAChC,SAAA,gBAAgB,KAAK,OAAO;AAAA,EACnC;AAAA,EAEO,MAAM,MAAwB;AACnC,UAAM,QAAQ,KAAK,aAAa,KAAK,OAAO;AAC5C,WAAI,KAAK,OAAO,MAAM,KAAK,KAAK,QACvB,QAEF;AAAA,EACT;AACF;AC7DA,MAAqB,uBAAuB,MAAM;AAAA,EAKhD,YAAY,SAAiB;AAC3B,cAAU,iBAAiB,SAC3B,MAAM,OAAO,GACb,KAAK,UAAU,SACV,KAAA,OAAO,KAAK,YAAY;AAAA,EAC/B;AACF;ACMA,MAAqB,YAArB,MAAqB,UAAsC;AAAA,EAA3D,cAAA;AA0BE,SAAQ,aAA4C,IACpD,KAAQ,UAA8C,IACtD,KAAQ,OAA2C,IACnD,KAAQ,QAA+C;EAAC;AAAA,EA1BxD,OAAc,KAAK,MAAoB,SAAS,IAAoB;AAClE,QAAI,QAAQ;AACH,aAAA;AAET,QAAI,KAAK,MAAM,IAAI,IAAI;AACrB,aAAO,KAAK,MAAM,IAAI,IAAI,KAAK;AAEjC,QAAI,QAAQ;AACV,UAAI,aAA0B;AAC1B,UAAA;AACF,qBAAa,KAAK;AAAA,cACN;AAKL,eAAA;AAAA,MACT;AACO,aAAA,KAAK,KAAK,YAAY,MAAM;AAAA,IACrC;AACO,WAAA;AAAA,EACT;AAAA,EAOO,OAAO,QAAc,OAA8B,OAAmB;AACrE,UAAAC,SAAQ,KAAK,MAAM,KAAK;AAC9B,QAAIA,UAAS;AACX,YAAM,IAAI,eAAe,oBAAoB,KAAK,OAAO;AAE3D,UAAM,YAAYA,QACZ;AAAA;AAAA,MAEJ,iBAAiB,QAAQ,MAAM,aAAa,KAAK,YAC7C,QACA,UAAU,OAAO,KAAK;AAAA,OAEtB,OAAO,IAAI,UAAU,QAAQ,MAAc,KAAK;AACtD,qBAAS,MAAM,IAAI,KAAK,SAAS,IAAI,GAC9B;AAAA,EACT;AAAA,EAEO,KAAK,MAAmB,SAAS,IAAoB;AACnD,WAAA,UAAS,KAAK,MAAM,MAAM;AAAA,EACnC;AAAA,EAEO,MACL,OACA,QAAe,MAAM,KACM;AACvB,QAAAA;AAuBJ,WAtBI,OAAO,SAAU,WACnBA,SAAQ,KAAK,MAAM,KAAK,KAAK,KAAK,WAAW,KAAK,IAEzC,iBAAiB,QAAQ,MAAM,aAAa,KAAK,YAC1DA,SAAQ,KAAK,MAAM,OACV,OAAO,SAAU,WACtB,QAAQ,MAAM,QAAQ,MAAM,QAC9BA,SAAQ,KAAK,MAAM,QACV,QAAQ,MAAM,QAAQ,MAAM,WACrCA,SAAQ,KAAK,MAAM,UAEZ,iBAAiB,aACX,MAAM,aAAa,OAAO,KAAK,IAAI,MAAM,KAAK,EACvD,KAAK,CAAC,UACFA,SAAA,KAAK,QAAQ,IAAI,GACrB,EAAAA,OAIL,GACDA,SAAQA,UAAS,KAAK,KAAK,MAAM,OAAO,IAEtCA,UAAS,OACJ,OAGP,WAAWA,UACX,QAAQ,MAAM,QAAQA,OAAM,SAC5B,QAAQ,MAAM,OAAOA,OAAM,QAEpBA,SAEF;AAAA,EACT;AAAA,EAEO,YAAY,aAAyD;AACnE,WAAA,YAAY,IAAI,CAAC,eAAe;AACrC,YAAM,SAAS,cAAc,YACvB,SAAS,cAAc;AACzB,UAAA,CAAC,UAAU,CAAC;AACR,cAAA,IAAI,eAAe,oBAAoB;AACpC,UAAA,UAAU,WAAW,aAAa;AACrC,cAAA,IAAI,eAAe,gCAAgC;AAE3D,YAAM,MAAM,SACR,WAAW,WACX,SACE,WAAW,WACV;AACF,kBAAA,MAAM,GAAG,IAAI,YAEd,SACE,OAAO,WAAW,WAAY,aAC3B,KAAA,WAAW,WAAW,OAAO,IAAI,cAE/B,WACL,WAAW,cACR,KAAA,QAAQ,WAAW,SAAS,IAAI,aAEnC,WAAW,YACT,MAAM,QAAQ,WAAW,OAAO,IAClC,WAAW,UAAU,WAAW,QAAQ,IAAI,CAAC,YACpC,QAAQ,aAChB,IAEU,WAAA,UAAU,WAAW,QAAQ,YAAY,IAErC,MAAM,QAAQ,WAAW,OAAO,IAC7C,WAAW,UACX,CAAC,WAAW,OAAO,GACd,QAAQ,CAAC,QAAgB;AAChC,SAAI,KAAK,KAAK,GAAG,KAAK,QAAQ,WAAW,aAAa,UAC/C,KAAA,KAAK,GAAG,IAAI;AAAA,MACnB,CACD,KAGE;AAAA,IAAA,CACR;AAAA,EACH;AACF;AAxIgB,UAAA,4BAAY;AAD5B,IAAqB,WAArB;ACfA,SAAS,MAAM,MAAmB,QAA0B;AAE1D,UADkB,KAAK,aAAa,OAAO,KAAK,IAE7C,MAAM,KAAK,EACX,OAAO,CAAC,SAAS,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;AACtD;AAEA,MAAM,wBAAwB,WAAW;AAAA,EACvC,OAAc,KAAK,MAA6B;AACtC,YAAA,KAAK,aAAa,OAAO,KAAK,IACnC,MAAM,KAAK,EACX,IAAI,CAAC,SAAS,KAAK,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG,CAAC;AAAA,EACzD;AAAA,EAEO,IAAI,MAAmB,OAAqB;AACjD,WAAK,KAAK,OAAO,MAAM,KAAK,KAG5B,KAAK,OAAO,IAAI,GAChB,KAAK,UAAU,IAAI,GAAG,KAAK,OAAO,IAAI,KAAK,EAAE,GACtC,MAJE;AAAA,EAKX;AAAA,EAEO,OAAO,MAAyB;AAE7B,IADQ,MAAM,MAAM,KAAK,OAAO,EAChC,QAAQ,CAAC,SAAS;AACnB,WAAA,UAAU,OAAO,IAAI;AAAA,IAAA,CAC3B,GACG,KAAK,UAAU,WAAW,KAC5B,KAAK,gBAAgB,OAAO;AAAA,EAEhC;AAAA,EAEO,MAAM,MAAwB;AAEnC,UAAM,SADS,MAAM,MAAM,KAAK,OAAO,EAAE,CAAC,KAAK,IAC1B,MAAM,KAAK,QAAQ,SAAS,CAAC;AAClD,WAAO,KAAK,OAAO,MAAM,KAAK,IAAI,QAAQ;AAAA,EAC5C;AACF;AAEA,MAAA,oBAAe;ACxCf,SAAS,SAAS,MAAsB;AAChC,QAAA,QAAQ,KAAK,MAAM,GAAG,GACtB,OAAO,MACV,MAAM,CAAC,EACP,IAAI,CAAC,SAAiB,KAAK,CAAC,EAAE,YAAA,IAAgB,KAAK,MAAM,CAAC,CAAC,EAC3D,KAAK,EAAE;AACH,SAAA,MAAM,CAAC,IAAI;AACpB;AAEA,MAAM,wBAAwB,WAAW;AAAA,EACvC,OAAc,KAAK,MAA6B;AACtC,YAAA,KAAK,aAAa,OAAO,KAAK,IAAI,MAAM,GAAG,EAAE,IAAI,CAAC,UAC5C,MAAM,MAAM,GAAG,EAChB,CAAC,EAAE,MACf;AAAA,EACH;AAAA,EAEO,IAAI,MAAmB,OAAqB;AACjD,WAAK,KAAK,OAAO,MAAM,KAAK,KAI5B,KAAK,MAAM,SAAS,KAAK,OAAO,CAAC,IAAI,OAC9B,MAJE;AAAA,EAKX;AAAA,EAEO,OAAO,MAAyB;AAErC,SAAK,MAAM,SAAS,KAAK,OAAO,CAAC,IAAI,IAChC,KAAK,aAAa,OAAO,KAC5B,KAAK,gBAAgB,OAAO;AAAA,EAEhC;AAAA,EAEO,MAAM,MAAwB;AAEnC,UAAM,QAAQ,KAAK,MAAM,SAAS,KAAK,OAAO,CAAC;AAC/C,WAAO,KAAK,OAAO,MAAM,KAAK,IAAI,QAAQ;AAAA,EAC5C;AACF;AAEA,MAAA,oBAAe;ACpCf,MAAM,gBAAgB;AAAA,EAIpB,YAAY,SAAsB;AAHlC,SAAQ,aAA4C,IAIlD,KAAK,UAAU,SACf,KAAK,MAAM;AAAA,EACb;AAAA,EAEO,UAAU,WAAuB,OAAkB;AAExD,IAAI,QACE,UAAU,IAAI,KAAK,SAAS,KAAK,MAC/B,UAAU,MAAM,KAAK,OAAO,KAAK,OAC9B,KAAA,WAAW,UAAU,QAAQ,IAAI,YAE/B,OAAA,KAAK,WAAW,UAAU,QAAQ,MAInC,UAAA,OAAO,KAAK,OAAO,GACtB,OAAA,KAAK,WAAW,UAAU,QAAQ;AAAA,EAE7C;AAAA,EAEO,QAAc;AACnB,SAAK,aAAa;AAClB,UAAM,OAAO,SAAS,KAAK,KAAK,OAAO;AACvC,QAAI,QAAQ;AACV;AAEF,UAAM,aAAa,WAAW,KAAK,KAAK,OAAO,GACzC,UAAUC,kBAAgB,KAAK,KAAK,OAAO,GAC3C,SAASC,kBAAgB,KAAK,KAAK,OAAO;AAE7C,eAAA,OAAO,OAAO,EACd,OAAO,MAAM,EACb,QAAQ,CAAC,SAAS;AACjB,YAAM,OAAO,KAAK,OAAO,MAAM,MAAM,MAAM,SAAS;AACpD,MAAI,gBAAgB,eACb,KAAA,WAAW,KAAK,QAAQ,IAAI;AAAA,IACnC,CACD;AAAA,EACL;AAAA,EAEO,KAAK,QAA2B;AACrC,WAAO,KAAK,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AAC5C,YAAM,QAAQ,KAAK,WAAW,GAAG,EAAE,MAAM,KAAK,OAAO;AAC9C,aAAA,OAAO,KAAK,KAAK;AAAA,IAAA,CACzB;AAAA,EACH;AAAA,EAEO,KAAK,QAA2B;AACrC,SAAK,KAAK,MAAM,GAChB,OAAO,KAAK,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AAC5C,WAAK,WAAW,GAAG,EAAE,OAAO,KAAK,OAAO;AAAA,IAAA,CACzC,GACD,KAAK,aAAa;EACpB;AAAA,EAEO,SAAiC;AACtC,WAAO,OAAO,KAAK,KAAK,UAAU,EAAE;AAAA,MAClC,CAAC,YAAoC,UACxB,WAAA,IAAI,IAAI,KAAK,WAAW,IAAI,EAAE,MAAM,KAAK,OAAO,GACpD;AAAA,MAET,CAAC;AAAA,IAAA;AAAA,EAEL;AACF;AAEA,MAAA,oBAAe,iBCnET,cAAN,MAAM,YAA2B;AAAA,EA+C/B,YACS,QACA,SACP;AAFO,SAAA,SAAA,QACA,KAAA,UAAA,SAEE,SAAA,MAAM,IAAI,SAAS,IAAI,GAChC,KAAK,OAAO,MACZ,KAAK,OAAO;AAAA,EACd;AAAA,EA/CA,OAAc,OAAO,UAA0B;AACzC,QAAA,KAAK,WAAW;AACZ,YAAA,IAAI,eAAe,iCAAiC;AAExD,QAAA,MACA;AACJ,WAAI,MAAM,QAAQ,KAAK,OAAO,KACxB,OAAO,YAAa,YACtB,QAAQ,SAAS,eACb,SAAS,OAAO,EAAE,EAAE,SAAA,MAAe,UAC7B,QAAA,SAAS,OAAO,EAAE,MAEnB,OAAO,YAAa,aACrB,QAAA,WAEN,OAAO,SAAU,WACnB,OAAO,SAAS,cAAc,KAAK,QAAQ,QAAQ,CAAC,CAAC,IAC5C,SAAS,KAAK,QAAQ,QAAQ,KAAK,IAAI,KACzC,OAAA,SAAS,cAAc,KAAK,IAEnC,OAAO,SAAS,cAAc,KAAK,QAAQ,CAAC,CAAC,KAGxC,OAAA,SAAS,cAAc,KAAK,OAAO,GAExC,KAAK,aACF,KAAA,UAAU,IAAI,KAAK,SAAS,GAE5B;AAAA,EACT;AAAA;AAAA,EAQA,IAAI,UAAe;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EAUO,SAAe;AAAA,EAEtB;AAAA,EAEO,QAAc;AACnB,UAAM,UAAU,KAAK,QAAQ,UAAU,EAAK;AACrC,WAAA,KAAK,OAAO,OAAO,OAAO;AAAA,EACnC;AAAA,EAEO,SAAe;AAChB,IAAA,KAAK,UAAU,QACZ,KAAA,OAAO,YAAY,IAAI,GAErB,SAAA,MAAM,OAAO,KAAK,OAAO;AAAA,EACpC;AAAA,EAEO,SAAS,OAAe,QAAsB;AAEnD,IADa,KAAK,QAAQ,OAAO,MAAM,EAClC,OAAO;AAAA,EACd;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,UAAM,OAAO,KAAK,QAAQ,OAAO,MAAM;AACnC,QAAA,KAAK,OAAO,MAAM,MAAM,MAAM,IAAI,KAAK,QAAQ;AAC5C,WAAA,KAAK,MAAM,KAAK;AAAA,aACZ,KAAK,OAAO,MAAM,MAAM,MAAM,SAAS,KAAK,MAAM;AAC3D,YAAM,SAAS,KAAK,OAAO,OAAO,KAAK,QAAQ,KAAK;AAEpD,WAAK,KAAK,MAAM,GACT,OAAA,OAAO,MAAM,KAAK;AAAA,IAC3B;AAAA,EACF;AAAA,EAEO,SAAS,OAAe,OAAe,KAAiB;AAC7D,UAAM,OACJ,OAAO,OACH,KAAK,OAAO,OAAO,QAAQ,KAAK,IAChC,KAAK,OAAO,OAAO,OAAO,GAAG,GAC7B,MAAM,KAAK,MAAM,KAAK;AAC5B,SAAK,OAAO,aAAa,MAAM,OAAO,MAAS;AAAA,EACjD;AAAA,EAEO,QAAQ,OAAe,QAAsB;AAC5C,UAAA,SAAS,KAAK,MAAM,KAAK;AAC/B,QAAI,UAAU;AACN,YAAA,IAAI,MAAM,2BAA2B;AAE7C,kBAAO,MAAM,MAAM,GACZ;AAAA,EACT;AAAA,EAEO,SAAiB;AACf,WAAA;AAAA,EACT;AAAA,EAEO,OAAO,OAAa,KAAK,QAAgB;AAC9C,WAAI,KAAK,UAAU,QAAQ,SAAS,OAC3B,IAEF,KAAK,OAAO,SAAS,OAAO,IAAI,IAAI,KAAK,OAAO,OAAO,IAAI;AAAA,EACpE;AAAA,EAEO,SAAS,UAAyC;AAErD,IAAA,KAAK,QAAQ,qBACb,EAAE,KAAK,kBAAkB,KAAK,QAAQ,sBAEtC,KAAK,KAAK,KAAK,QAAQ,kBAAkB,QAAQ;AAAA,EAErD;AAAA,EAEO,SAAe;AAChB,IAAA,KAAK,QAAQ,cAAc,QAC7B,KAAK,QAAQ,WAAW,YAAY,KAAK,OAAO,GAElD,KAAK,OAAO;AAAA,EACd;AAAA,EAEO,YAAY,MAAqB,OAAmB;AACnD,UAAA,cACJ,OAAO,QAAS,WAAW,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI;AAC3D,WAAA,KAAK,UAAU,SACjB,KAAK,OAAO,aAAa,aAAa,KAAK,QAAQ,MAAS,GAC5D,KAAK,OAAO,IAEP;AAAA,EACT;AAAA,EAEO,MAAM,OAAe,QAA+B;AAClD,WAAA,UAAU,IAAI,OAAO,KAAK;AAAA,EACnC;AAAA,EAEO,OACL,YACA,UACM;AAAA,EAER;AAAA,EAEO,KAAK,MAAuB,OAAqB;AAChD,UAAA,UACJ,OAAO,QAAS,WACX,KAAK,OAAO,OAAO,MAAM,KAAK,IAC/B;AAIF,QAHA,KAAK,UAAU,QACjB,KAAK,OAAO,aAAa,SAAS,KAAK,QAAQ,MAAS,GAEtD,OAAO,QAAQ,eAAgB;AACjC,YAAM,IAAI,eAAe,eAAe,IAAI,EAAE;AAEhD,mBAAQ,YAAY,IAAI,GACjB;AAAA,EACT;AACF;AA7KE,YAAc,WAAW;AAD3B,IAAM,aAAN;ACPA,MAAM,YAAN,MAAM,kBAAiB,WAA2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhD,OAAc,MAAM,UAAqB;AAChC,WAAA;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,MAAM,MAAY,QAAwB;AAE7C,WAAA,KAAK,YAAY,QACjB,KAAK,QAAQ,wBAAwB,IAAI,IACvC,KAAK,iCAEA,KAAK,IAAI,QAAQ,CAAC,IAEpB;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMO,SAAS,OAAe,YAAsC;AAEnE,QAAI,SADuB,MAAM,KAAK,KAAK,OAAO,QAAQ,UAAU,EAC5C,QAAQ,KAAK,OAAO;AAC5C,WAAI,QAAQ,MACA,UAAA,IAEL,CAAC,KAAK,OAAO,SAAS,MAAM;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOO,QAAa;AACX,WAAA;AAAA,MACL,CAAC,KAAK,QAAQ,QAAQ,GAAG,KAAK,QAAQ,MAAM,KAAK,OAAO,KAAK;AAAA,IAAA;AAAA,EAEjE;AACF;AAjDE,UAAc,QAAQ,MAAM;AAD9B,IAAM,WAAN;AAoDA,MAAA,aAAe;ACtDf,MAAM,WAAiC;AAAA,EAKrC,cAAc;AACZ,SAAK,OAAO,MACZ,KAAK,OAAO,MACZ,KAAK,SAAS;AAAA,EAChB;AAAA,EAEO,UAAU,OAAkB;AAE7B,QADJ,KAAK,aAAa,MAAM,CAAC,GAAG,IAAI,GAC5B,MAAM,SAAS,GAAG;AACd,YAAA,OAAO,MAAM,MAAM,CAAC;AACrB,WAAA,OAAO,GAAG,IAAI;AAAA,IACrB;AAAA,EACF;AAAA,EAEO,GAAG,OAAyB;AAC3B,UAAA,OAAO,KAAK;AAClB,QAAI,MAAM;AACH,WAAA,OAAO,QAAQ;AACX,eAAA,GACT,MAAM,KAAK;AAEN,WAAA;AAAA,EACT;AAAA,EAEO,SAAS,MAAkB;AAC1B,UAAA,OAAO,KAAK;AAClB,QAAI,MAAM;AACV,WAAO,OAAK;AACV,UAAI,QAAQ;AACH,eAAA;AAET,YAAM,KAAK;AAAA,IACb;AACO,WAAA;AAAA,EACT;AAAA,EAEO,QAAQ,MAAiB;AACxB,UAAA,OAAO,KAAK;AAClB,QAAI,MAAM,QACN,QAAQ;AACZ,WAAO,OAAK;AACV,UAAI,QAAQ;AACH,eAAA;AAEA,eAAA,GACT,MAAM,KAAK;AAAA,IACb;AACO,WAAA;AAAA,EACT;AAAA,EAEO,aAAa,MAAgB,SAAyB;AAC3D,IAAI,QAAQ,SAGZ,KAAK,OAAO,IAAI,GAChB,KAAK,OAAO,SACR,WAAW,QACb,KAAK,OAAO,QAAQ,MAChB,QAAQ,QAAQ,SAClB,QAAQ,KAAK,OAAO,OAEtB,QAAQ,OAAO,MACX,YAAY,KAAK,SACnB,KAAK,OAAO,SAEL,KAAK,QAAQ,QACtB,KAAK,KAAK,OAAO,MACjB,KAAK,OAAO,KAAK,MACjB,KAAK,OAAO,SAEZ,KAAK,OAAO,MACP,KAAA,OAAO,KAAK,OAAO,OAE1B,KAAK,UAAU;AAAA,EACjB;AAAA,EAEO,OAAO,QAAmB;AAC/B,QAAI,QAAQ,GACR,MAAM,KAAK;AACf,WAAO,OAAO,QAAM;AAClB,UAAI,QAAQ;AACH,eAAA;AAET,eAAS,IAAI,UACb,MAAM,IAAI;AAAA,IACZ;AACO,WAAA;AAAA,EACT;AAAA,EAEO,OAAO,MAAe;AAC3B,IAAK,KAAK,SAAS,IAAI,MAGnB,KAAK,QAAQ,SACV,KAAA,KAAK,OAAO,KAAK,OAEpB,KAAK,QAAQ,SACV,KAAA,KAAK,OAAO,KAAK,OAEpB,SAAS,KAAK,SAChB,KAAK,OAAO,KAAK,OAEf,SAAS,KAAK,SAChB,KAAK,OAAO,KAAK,OAEnB,KAAK,UAAU;AAAA,EACjB;AAAA,EAEO,SAAS,UAAoB,KAAK,MAAsB;AAE7D,WAAO,MAAgB;AACrB,YAAM,MAAM;AACZ,aAAI,WAAW,SACb,UAAU,QAAQ,OAEb;AAAA,IAAA;AAAA,EAEX;AAAA,EAEO,KAAK,OAAe,YAAY,IAA2B;AAC1D,UAAA,OAAO,KAAK;AAClB,QAAI,MAAM;AACV,WAAO,OAAK;AACJ,YAAA,SAAS,IAAI;AACnB,UACE,QAAQ,UACP,aACC,UAAU,WACT,IAAI,QAAQ,QAAQ,IAAI,KAAK,OAAO,MAAM;AAEtC,eAAA,CAAC,KAAK,KAAK;AAEX,eAAA,QACT,MAAM,KAAK;AAAA,IACb;AACO,WAAA,CAAC,MAAM,CAAC;AAAA,EACjB;AAAA,EAEO,QAAQ,UAAkC;AACzC,UAAA,OAAO,KAAK;AAClB,QAAI,MAAM;AACV,WAAO;AACL,eAAS,GAAG,GACZ,MAAM,KAAK;AAAA,EAEf;AAAA,EAEO,UACL,OACA,QACA,UACM;AACN,QAAI,UAAU;AACZ;AAEF,UAAM,CAAC,WAAW,MAAM,IAAI,KAAK,KAAK,KAAK;AAC3C,QAAI,WAAW,QAAQ;AACjB,UAAA,OAAO,KAAK,SAAS,SAAS;AACpC,QAAI,MAAM;AACH,WAAA,OAAO,WAAW,QAAQ,UAAQ;AACjC,YAAA,YAAY,IAAI;AACtB,MAAI,QAAQ,WACV;AAAA,QACE;AAAA,QACA,QAAQ;AAAA,QACR,KAAK,IAAI,QAAQ,WAAW,YAAY,KAAK;AAAA,MAAA,IAGtC,SAAA,KAAK,GAAG,KAAK,IAAI,WAAW,QAAQ,SAAS,QAAQ,CAAC,GAErD,YAAA,WACZ,MAAM,KAAK;AAAA,IACb;AAAA,EACF;AAAA,EAEO,IAAI,UAAkC;AAC3C,WAAO,KAAK,OAAO,CAAC,MAAW,SACxB,KAAA,KAAK,SAAS,GAAG,CAAC,GAChB,OACN,CAAE,CAAA;AAAA,EACP;AAAA,EAEO,OAAU,UAAkC,MAAY;AACvD,UAAA,OAAO,KAAK;AAClB,QAAI,MAAM;AACV,WAAO;AACE,aAAA,SAAS,MAAM,GAAG,GACzB,MAAM,KAAK;AAEN,WAAA;AAAA,EACT;AACF;AChMA,SAAS,iBAAiB,MAAY,QAAoB;AAClD,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC1B,MAAA;AAAc,WAAA;AACd,MAAA;AACK,WAAA,OAAO,OAAO,IAAI;AAAA,UACf;AACV,UAAM,OAAO,OAAO,OAAO,MAAM,MAAM;AACvC,iBAAM,KAAK,KAAK,UAAU,EAAE,QAAQ,CAAC,UAAgB;AAC9C,WAAA,QAAQ,YAAY,KAAK;AAAA,IAAA,CAC/B,GACG,KAAK,cACP,KAAK,WAAW,aAAa,KAAK,SAAS,IAAI,GAEjD,KAAK,OAAO,GACL;AAAA,EACT;AACF;AAEA,MAAM,cAAN,MAAM,oBAAmB,WAA6B;AAAA,EAgBpD,YAAY,QAAc,SAAe;AACvC,UAAM,QAAQ,OAAO,GAHvB,KAAO,SAA6B,MAIlC,KAAK,MAAM;AAAA,EACb;AAAA,EAEO,YAAY,OAAmB;AACpC,SAAK,aAAa,KAAK;AAAA,EACzB;AAAA,EAEO,SAAe;AACpB,UAAM,OAAO,GACR,KAAA,SAAS,QAAQ,CAAC,UAAU;AAC/B,YAAM,OAAO;AAAA,IAAA,CACd;AAAA,EACH;AAAA,EAEO,SAAS,MAAyB;AACnC,IAAA,KAAK,UAAU,QACjB,KAAK,OAAO,UAEd,KAAK,SAAS,MACV,YAAW,WACb,KAAK,OAAO,UAAU,IAAI,YAAW,OAAO,GAEzC,KAAA,OAAO,aAAa,mBAAmB,OAAO,GACnD,KAAK,QAAQ,aAAa,KAAK,QAAQ,KAAK,QAAQ,UAAU;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAKO,QAAc;AACd,SAAA,WAAW,IAAI,cAEpB,MAAM,KAAK,KAAK,QAAQ,UAAU,EAC/B,OAAO,CAAC,SAAe,SAAS,KAAK,MAAM,EAC3C,QAAA,EACA,QAAQ,CAAC,SAAe;AACnB,UAAA;AACF,cAAM,QAAQ,iBAAiB,MAAM,KAAK,MAAM;AAChD,aAAK,aAAa,OAAO,KAAK,SAAS,QAAQ,MAAS;AAAA,eACjD,KAAK;AACZ,YAAI,eAAe;AACjB;AAEM,cAAA;AAAA,MAEV;AAAA,IAAA,CACD;AAAA,EACL;AAAA,EAEO,SAAS,OAAe,QAAsB;AACnD,QAAI,UAAU,KAAK,WAAW,KAAK;AACjC,aAAO,KAAK;AAEd,SAAK,SAAS,UAAU,OAAO,QAAQ,CAAC,OAAO,QAAQ,gBAAgB;AAC/D,YAAA,SAAS,QAAQ,WAAW;AAAA,IAAA,CACnC;AAAA,EACH;AAAA,EAUO,WAAW,UAAe,QAAQ,GAA0B;AACjE,UAAM,CAAC,OAAO,MAAM,IAAI,KAAK,SAAS,KAAK,KAAK;AAE7C,WAAA,SAAS,YAAY,QAAQ,SAAS,KAAK,KAC3C,SAAS,YAAY,QAAQ,iBAAiB,WAExC,CAAC,OAAc,MAAM,IACnB,iBAAiB,cACnB,MAAM,WAAW,UAAU,MAAM,IAEjC,CAAC,MAAM,EAAE;AAAA,EAEpB;AAAA,EAYO,YACL,UACA,QAAQ,GACR,SAAiB,OAAO,WAChB;AACR,QAAI,cAAsB,CAAA,GACtB,aAAa;AACjB,gBAAK,SAAS;AAAA,MACZ;AAAA,MACA;AAAA,MACA,CAAC,OAAa,YAAoB,gBAAwB;AAErD,SAAA,SAAS,YAAY,QAAQ,SAAS,KAAK,KAC3C,SAAS,YAAY,QAAQ,iBAAiB,aAE/C,YAAY,KAAK,KAAK,GAEpB,iBAAiB,gBACnB,cAAc,YAAY;AAAA,UACxB,MAAM,YAAY,UAAU,YAAY,UAAU;AAAA,QAAA,IAGxC,cAAA;AAAA,MAChB;AAAA,IAAA,GAEK;AAAA,EACT;AAAA,EAEO,SAAe;AACf,SAAA,SAAS,QAAQ,CAAC,UAAU;AAC/B,YAAM,OAAO;AAAA,IAAA,CACd,GACD,MAAM,OAAO;AAAA,EACf;AAAA,EAEO,yBAA+B;AACpC,QAAI,OAAO;AACN,SAAA,SAAS,QAAQ,CAAC,UAAgB;AAOrC,MANI,QAGY,KAAK,QAAQ,gBAAgB;AAAA,QAC3C,CAAC,QAAyB,iBAAiB;AAAA,MAAA,MAKzC,MAAM,QAAQ,UAAU,MAAM,cAC5B,MAAM,QAAQ,QAChB,KAAK,WAAW,KAAK,GAEnB,MAAM,QAAQ,QACX,KAAA,WAAW,MAAM,IAAI,GAE5B,MAAM,OAAO,UACN,OAAA,MACE,iBAAiB,cAC1B,MAAM,OAAO,IAEb,MAAM,OAAO;AAAA,IACf,CACD;AAAA,EACH;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,SAAK,SAAS,UAAU,OAAO,QAAQ,CAAC,OAAO,QAAQ,gBAAgB;AACrE,YAAM,SAAS,QAAQ,aAAa,MAAM,KAAK;AAAA,IAAA,CAChD;AAAA,EACH;AAAA,EAEO,SAAS,OAAe,OAAe,KAAiB;AAC7D,UAAM,CAAC,OAAO,MAAM,IAAI,KAAK,SAAS,KAAK,KAAK;AAChD,QAAI;AACI,YAAA,SAAS,QAAQ,OAAO,GAAG;AAAA,SAC5B;AACL,YAAM,OACJ,OAAO,OACH,KAAK,OAAO,OAAO,QAAQ,KAAK,IAChC,KAAK,OAAO,OAAO,OAAO,GAAG;AACnC,WAAK,YAAY,IAAI;AAAA,IACvB;AAAA,EACF;AAAA,EAEO,aAAa,WAAiB,SAA6B;AAC5D,IAAA,UAAU,UAAU,QACZ,UAAA,OAAO,SAAS,OAAO,SAAS;AAE5C,QAAI,aAA0B;AAC9B,SAAK,SAAS,aAAa,WAAW,WAAW,IAAI,GACrD,UAAU,SAAS,MACf,WAAW,SACb,aAAa,QAAQ,WAGrB,KAAK,QAAQ,eAAe,UAAU,WACtC,KAAK,QAAQ,gBAAgB,eAE7B,KAAK,QAAQ,aAAa,UAAU,SAAS,UAAU,GAEzD,UAAU,OAAO;AAAA,EACnB;AAAA,EAEO,SAAiB;AACtB,WAAO,KAAK,SAAS,OAAO,CAAC,MAAM,UAC1B,OAAO,MAAM,UACnB,CAAC;AAAA,EACN;AAAA,EAEO,aAAa,cAAsB,SAA6B;AAChE,SAAA,SAAS,QAAQ,CAAC,UAAU;AAClB,mBAAA,aAAa,OAAO,OAAO;AAAA,IAAA,CACzC;AAAA,EACH;AAAA,EAEO,SAAS,SAAwC;AAMlD,QALJ,MAAM,SAAS,OAAO,GACtB,KAAK,uBAAuB,GACxB,KAAK,UAAU,QAAQ,KAAK,WAAW,KAAK,QAAQ,cACtD,KAAK,QAAQ,aAAa,KAAK,QAAQ,KAAK,QAAQ,UAAU,GAE5D,KAAK,SAAS,WAAW;AACvB,UAAA,KAAK,QAAQ,gBAAgB,MAAM;AACrC,cAAM,QAAQ,KAAK,OAAO,OAAO,KAAK,QAAQ,aAAa,QAAQ;AACnE,aAAK,YAAY,KAAK;AAAA,MAAA;AAItB,aAAK,OAAO;AAAA,EAGlB;AAAA,EAEO,KAAK,OAAe,YAAY,IAAyB;AACxD,UAAA,CAAC,OAAO,MAAM,IAAI,KAAK,SAAS,KAAK,OAAO,SAAS,GACrD,WAA6B,CAAC,CAAC,MAAM,KAAK,CAAC;AACjD,WAAI,iBAAiB,cACZ,SAAS,OAAO,MAAM,KAAK,QAAQ,SAAS,CAAC,KAC3C,SAAS,QAClB,SAAS,KAAK,CAAC,OAAO,MAAM,CAAC,GAExB;AAAA,EACT;AAAA,EAEO,YAAY,OAAmB;AAC/B,SAAA,SAAS,OAAO,KAAK;AAAA,EAC5B;AAAA,EAEO,YAAY,MAAqB,OAAmB;AACnD,UAAA,cACJ,OAAO,QAAS,WAAW,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI;AAC/D,WAAI,uBAAuB,eACzB,KAAK,aAAa,WAAW,GAExB,MAAM,YAAY,WAAW;AAAA,EACtC;AAAA,EAEO,MAAM,OAAe,QAAQ,IAAoB;AACtD,QAAI,CAAC,OAAO;AACV,UAAI,UAAU;AACL,eAAA;AAEL,UAAA,UAAU,KAAK;AACjB,eAAO,KAAK;AAAA,IAEhB;AACM,UAAA,QAAQ,KAAK;AACnB,WAAI,KAAK,UACP,KAAK,OAAO,aAAa,OAAO,KAAK,QAAQ,MAAS,GAEnD,KAAA,SAAS,UAAU,OAAO,KAAK,UAAU,CAAC,OAAO,QAAQ,YAAY;AACxE,YAAM,QAAQ,MAAM,MAAM,QAAQ,KAAK;AACvC,MAAI,SAAS,QACX,MAAM,YAAY,KAAK;AAAA,IACzB,CACD,GACM;AAAA,EACT;AAAA,EAEO,WAAW,OAAqB;AAC/B,UAAA,QAAQ,KAAK;AACZ,WAAA,MAAM,QAAQ;AACb,YAAA,YAAY,MAAM,IAAI;AAE9B,WAAI,KAAK,UACP,KAAK,OAAO,aAAa,OAAO,KAAK,QAAQ,MAAS,GAEjD;AAAA,EACT;AAAA,EAEO,SAAe;AACpB,IAAI,KAAK,UACP,KAAK,aAAa,KAAK,QAAQ,KAAK,QAAQ,MAAS,GAEvD,KAAK,OAAO;AAAA,EACd;AAAA,EAEO,OACL,WACA,UACM;AACN,UAAM,aAAqB,CAAA,GACrB,eAAuB,CAAA;AACnB,cAAA,QAAQ,CAAC,aAAa;AAC9B,MAAI,SAAS,WAAW,KAAK,WAAW,SAAS,SAAS,gBAC7C,WAAA,KAAK,GAAG,SAAS,UAAU,GACzB,aAAA,KAAK,GAAG,SAAS,YAAY;AAAA,IAC5C,CACD,GACY,aAAA,QAAQ,CAAC,SAAe;AAInC,UACE,KAAK,cAAc;AAAA,MAEnB,KAAK,YAAY,YACjB,SAAS,KAAK,wBAAwB,IAAI,IACxC,KAAK;AAEP;AAEF,YAAM,OAAO,KAAK,OAAO,KAAK,IAAI;AAClC,MAAI,QAAQ,SAIV,KAAK,QAAQ,cAAc,QAC3B,KAAK,QAAQ,eAAe,KAAK,YAEjC,KAAK,OAAO;AAAA,IACd,CACD,GAEE,WAAA,OAAO,CAAC,SACA,KAAK,eAAe,KAAK,WAAW,SAAS,KAAK,MAC1D,EACA,KAAK,CAAC,GAAG,MACJ,MAAM,IACD,IAEL,EAAE,wBAAwB,CAAC,IAAI,KAAK,8BAC/B,IAEF,EACR,EACA,QAAQ,CAAC,SAAS;AACjB,UAAI,UAAuB;AACvB,MAAA,KAAK,eAAe,SACtB,UAAU,KAAK,OAAO,KAAK,KAAK,WAAW;AAE7C,YAAM,OAAO,iBAAiB,MAAM,KAAK,MAAM;AAC/C,OAAI,KAAK,SAAS,WAAW,KAAK,QAAQ,UACpC,KAAK,UAAU,QACZ,KAAA,OAAO,YAAY,IAAI,GAEzB,KAAA,aAAa,MAAM,WAAW,MAAS;AAAA,IAC9C,CACD,GACH,KAAK,uBAAuB;AAAA,EAC9B;AACF;AA3WE,YAAc,UAAU;AAV1B,IAAM,aAAN;AAuXA,MAAA,eAAe;ACjYf,SAAS,QACP,MACA,MACS;AACL,MAAA,OAAO,KAAK,IAAI,EAAE,WAAW,OAAO,KAAK,IAAI,EAAE;AAC1C,WAAA;AAET,aAAW,QAAQ;AACjB,QAAI,KAAK,IAAI,MAAM,KAAK,IAAI;AACnB,aAAA;AAGJ,SAAA;AACT;AAEA,MAAM,cAAN,MAAM,oBAAmBC,aAAkC;AAAA,EAMzD,OAAO,OAAO,OAAiB;AACtB,WAAA,MAAM,OAAO,KAAK;AAAA,EAC3B;AAAA,EAEA,OAAc,QAAQ,SAAsB,QAAmB;AAC7D,UAAMH,SAAQ,OAAO,MAAM,YAAW,QAAQ;AAC9C,QACE,EAAAA,UAAS,QACT,QAAQ,YAAaA,OAA0B,UAGtC;AAAA,UAAA,OAAO,KAAK,WAAY;AAC1B,eAAA;AACE,UAAA,MAAM,QAAQ,KAAK,OAAO;AAC5B,eAAA,QAAQ,QAAQ;;EAG3B;AAAA,EAIA,YAAY,QAAc,SAAe;AACvC,UAAM,QAAQ,OAAO,GACrB,KAAK,aAAa,IAAII,kBAAgB,KAAK,OAAO;AAAA,EACpD;AAAA,EAEO,OAAO,MAAc,OAAkB;AAC5C,QAAI,SAAS,KAAK,QAAQ,YAAY,CAAC;AAChC,WAAA,SAAS,QAAQ,CAAC,UAAU;AAC3B,QAAE,iBAAiB,gBACrB,QAAQ,MAAM,KAAK,YAAW,UAAU,EAAI,IAEzC,KAAA,WAAW,KAAK,KAAmB;AAAA,MAAA,CACzC,GACD,KAAK,OAAO;AAAA,SACP;AACL,YAAM,SAAS,KAAK,OAAO,MAAM,MAAM,MAAM,MAAM;AACnD,UAAI,UAAU;AACZ;AAEF,MAAI,kBAAkB,aACf,KAAA,WAAW,UAAU,QAAQ,KAAK,IAEvC,UACC,SAAS,KAAK,QAAQ,YAAY,KAAK,QAAQ,EAAE,IAAI,MAAM,UAEvD,KAAA,YAAY,MAAM,KAAK;AAAA,IAEhC;AAAA,EACF;AAAA,EAEO,UAAoC;AACnC,UAAA,UAAU,KAAK,WAAW,OAAO,GACjC,SAAS,KAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,MAAM;AAC7D,WAAI,UAAU,SACJ,QAAA,KAAK,QAAQ,QAAQ,IAAI,SAE5B;AAAA,EACT;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,IACE,KAAK,UAAU,IAAI,KAAK,QACxB,KAAK,OAAO,MAAM,MAAM,MAAM,SAAS,IAE1B,KAAK,QAAQ,OAAO,MAAM,EAClC,OAAO,MAAM,KAAK,IAEvB,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK;AAAA,EAE7C;AAAA,EAEO,SAAS,SAAuC;AACrD,UAAM,SAAS,OAAO;AAChB,UAAA,UAAU,KAAK;AACrB,QAAI,OAAO,KAAK,OAAO,EAAE,WAAW;AAClC,aAAO,KAAK;AAEd,UAAM,OAAO,KAAK;AAEhB,IAAA,gBAAgB,eAChB,KAAK,SAAS,QACd,QAAQ,SAAS,KAAK,QAAQ,CAAC,MAE/B,KAAK,aAAa,IAAI,GACtB,KAAK,OAAO;AAAA,EAEhB;AAAA,EAEO,YAAY,MAAqB,OAAmB;AACzD,UAAM,cAAc,MAAM,YAAY,MAAM,KAAK;AAC5C,gBAAA,WAAW,KAAK,WAAW,GACzB;AAAA,EACT;AAAA,EAEO,OACL,WACA,SACM;AACA,UAAA,OAAO,WAAW,OAAO,GACN,UAAU;AAAA,MACjC,CAAC,aACC,SAAS,WAAW,KAAK,WAAW,SAAS,SAAS;AAAA,IAAA,KAGxD,KAAK,WAAW;EAEpB;AAAA,EAEO,KAAK,MAAuB,OAAqB;AACtD,UAAM,UAAU,MAAM,KAAK,MAAM,KAAK;AACtC,WAAI,mBAAmB,eAChB,KAAA,WAAW,KAAK,OAAO,GAEvB;AAAA,EACT;AACF;AA9HgB,YAAA,kBAAqC,CAAC,aAAYC,UAAQ,GACxE,YAAc,WAAW,UACzB,YAAc,QAAQ,MAAM,aAC5B,YAAc,UAA6B;AAJ7C,IAAM,aAAN;AAiIA,MAAA,eAAe,YCjJT,aAAN,MAAM,mBAAkBF,aAAkC;AAAA,EAUxD,OAAO,OAAO,OAAiB;AACtB,WAAA,MAAM,OAAO,KAAK;AAAA,EAC3B;AAAA,EAEA,OAAc,QAAQ,SAAsB,QAAmB;AAC7D,UAAMH,SAAQ,OAAO,MAAM,WAAU,QAAQ;AAC7C,QACE,EAAAA,UAAS,QACT,QAAQ,YAAaA,OAA0B,UAGtC;AAAA,UAAA,OAAO,KAAK,WAAY;AAC1B,eAAA;AACE,UAAA,MAAM,QAAQ,KAAK,OAAO;AAC5B,eAAA,QAAQ,QAAQ;;EAE3B;AAAA,EAIA,YAAY,QAAc,SAAe;AACvC,UAAM,QAAQ,OAAO,GACrB,KAAK,aAAa,IAAII,kBAAgB,KAAK,OAAO;AAAA,EACpD;AAAA,EAEO,OAAO,MAAc,OAAkB;AAC5C,UAAM,SAAS,KAAK,OAAO,MAAM,MAAM,MAAM,KAAK;AAClD,IAAI,UAAU,SAEH,kBAAkB,aACtB,KAAA,WAAW,UAAU,QAAQ,KAAK,IAC9B,SAAS,KAAK,QAAQ,YAAY,CAAC,QACvC,KAAA,YAAY,WAAU,QAAQ,IAEnC,UACC,SAAS,KAAK,QAAQ,YAAY,KAAK,QAAQ,EAAE,IAAI,MAAM,UAEvD,KAAA,YAAY,MAAM,KAAK;AAAA,EAEhC;AAAA,EAEO,UAAoC;AACnC,UAAA,UAAU,KAAK,WAAW,OAAO,GACjC,SAAS,KAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,MAAM;AAC7D,WAAI,UAAU,SACJ,QAAA,KAAK,QAAQ,QAAQ,IAAI,SAE5B;AAAA,EACT;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,IAAI,KAAK,OAAO,MAAM,MAAM,MAAM,KAAK,KAAK,OACrC,KAAA,OAAO,MAAM,KAAK,IAEvB,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK;AAAA,EAE7C;AAAA,EAEO,SAAS,OAAe,OAAe,KAAiB;AACzD,QAAA,OAAO,QAAQ,KAAK,OAAO,MAAM,OAAO,MAAM,MAAM,KAAK;AAErD,YAAA,SAAS,OAAO,OAAO,GAAG;AAAA,SAC3B;AACC,YAAA,QAAQ,KAAK,MAAM,KAAK;AAC9B,UAAI,SAAS,MAAM;AACjB,cAAM,OAAO,KAAK,OAAO,OAAO,OAAO,GAAG;AACpC,cAAA,OAAO,aAAa,MAAM,KAAK;AAAA,MAAA;AAE/B,cAAA,IAAI,MAAM,4CAA4C;AAAA,IAEhE;AAAA,EACF;AAAA,EAEO,YAAY,MAAqB,OAAmB;AACzD,UAAM,cAAc,MAAM,YAAY,MAAM,KAAK;AAC5C,gBAAA,WAAW,KAAK,WAAW,GACzB;AAAA,EACT;AAAA,EAEO,OACL,WACA,SACM;AACA,UAAA,OAAO,WAAW,OAAO,GACN,UAAU;AAAA,MACjC,CAAC,aACC,SAAS,WAAW,KAAK,WAAW,SAAS,SAAS;AAAA,IAAA,KAGxD,KAAK,WAAW;EAEpB;AACF;AA1GE,WAAc,WAAW,SACzB,WAAc,QAAQ,MAAM,YAC5B,WAAc,UAA6B,KAC3C,WAAc,kBAAqC;AAAA,EACjDE;AAAAA,EACA;AAAA,EACAD;AAAA;AAPJ,IAAM,YAAN;AA6GA,MAAA,cAAe,WCtHT,iBAAN,MAAM,uBAAsBF,aAAW;AAAA,EAQ9B,aAAsB;AAEzB,WAAA,KAAK,SAAS,QAAQ,KAAK,KAAK,QAAQ,aAAa,KAAK,QAAQ;AAAA,EAEtE;AAAA,EAEO,SAAS,OAAe,QAAsB;AAC7C,UAAA,SAAS,OAAO,MAAM,GAC5B,KAAK,uBAAuB;AAAA,EAC9B;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,UAAM,SAAS,OAAO,QAAQ,MAAM,KAAK,GACzC,KAAK,uBAAuB;AAAA,EAC9B;AAAA,EAEO,SAAS,OAAe,OAAe,KAAiB;AACvD,UAAA,SAAS,OAAO,OAAO,GAAG,GAChC,KAAK,uBAAuB;AAAA,EAC9B;AAAA,EAEO,SAAS,SAAuC;AACrD,UAAM,SAAS,OAAO,GAClB,KAAK,SAAS,SAAS,KAAK,KAAK,QAAQ,QAAQ,KAAK,iBACnD,KAAA,KAAK,aAAa,IAAI,GAC3B,KAAK,KAAK;EAEd;AACF;AAxCE,eAAc,WAAW,aACzB,eAAc,QAAQ,MAAM;AAF9B,IAAM,gBAAN;AA2CA,MAAA,kBAAe;AC5Cf,MAAM,kBAAkBE,WAAgC;AAAA,EACtD,OAAc,QAAQ,UAAuB,SAAoB;AAAA,EAEjE;AAAA,EAEO,OAAO,MAAc,OAAkB;AAI5C,UAAM,SAAS,GAAG,KAAK,UAAU,MAAM,KAAK;AAAA,EAC9C;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,IAAI,UAAU,KAAK,WAAW,KAAK,WAC5B,KAAA,OAAO,MAAM,KAAK,IAEvB,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK;AAAA,EAE7C;AAAA,EAEO,UAAoC;AACzC,WAAO,KAAK,QAAQ,QAAQ,KAAK,SAAS,KAAK,MAAM;AAAA,EACvD;AACF;AAEA,MAAA,cAAe,WC1BT,kBAAkB;AAAA,EACtB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,uBAAuB;AAAA,EACvB,WAAW;AAAA,EACX,SAAS;AACX,GAEM,0BAA0B,KAE1B,cAAN,MAAM,oBAAmBF,aAA2B;AAAA,EASlD,YACS,UACP,MACA;AAEA,UAAM,MAAM,IAAI,GAJT,KAAA,WAAA,UAKP,KAAK,SAAS,MACd,KAAK,MAAM,GACX,KAAK,WAAW,IAAI,iBAAiB,CAAC,cAAgC;AACpE,WAAK,OAAO,SAAS;AAAA,IAAA,CACtB,GACD,KAAK,SAAS,QAAQ,KAAK,SAAS,eAAe,GACnD,KAAK,OAAO;AAAA,EACd;AAAA,EAEO,OAAO,OAA8B,OAAmB;AAC7D,WAAO,KAAK,SAAS,OAAO,MAAM,OAAO,KAAK;AAAA,EAChD;AAAA,EAEO,KAAK,MAAmB,SAAS,IAAoB;AAC1D,UAAM,OAAO,KAAK,SAAS,KAAK,MAAM,MAAM;AAC5C,WAAK,OAGD,KAAK,WAAW,OACX,OAEF,SAAS,KAAK,KAAK,KAAK,OAAO,QAAQ,YAAY,EAAI,IAAI,OALzD;AAAA,EAMX;AAAA,EAEO,MACL,OACA,QAAe,MAAM,KACM;AAC3B,WAAO,KAAK,SAAS,MAAM,OAAO,KAAK;AAAA,EACzC;AAAA,EAEO,YAAY,aAAmC;AACpD,WAAO,KAAK,SAAS,SAAS,GAAG,WAAW;AAAA,EAC9C;AAAA,EAEO,QAAc;AACf,IAAA,KAAK,UAAU,QAGnB,MAAM,MAAM;AAAA,EACd;AAAA,EAEO,SAAe;AACpB,UAAM,OAAO,GACb,KAAK,SAAS;EAChB;AAAA,EAEO,SAAS,OAAe,QAAsB;AACnD,SAAK,OAAO,GACR,UAAU,KAAK,WAAW,KAAK,WAC5B,KAAA,SAAS,QAAQ,CAAC,UAAU;AAC/B,YAAM,OAAO;AAAA,IAAA,CACd,IAEK,MAAA,SAAS,OAAO,MAAM;AAAA,EAEhC;AAAA,EAEO,SACL,OACA,QACA,MACA,OACM;AACN,SAAK,OAAO,GACZ,MAAM,SAAS,OAAO,QAAQ,MAAM,KAAK;AAAA,EAC3C;AAAA,EAEO,SAAS,OAAe,OAAe,KAAiB;AAC7D,SAAK,OAAO,GACN,MAAA,SAAS,OAAO,OAAO,GAAG;AAAA,EAClC;AAAA,EAOO,SAAS,YAAiB,IAAI,UAAe,CAAA,GAAU;AAC5D,UAAM,SAAS,OAAO;AACtB,UAAM,eAAe,QAAQ,gBAAgB,oBAAI,QAAQ;AAEzD,QAAI,UAAU,MAAM,KAAK,KAAK,SAAS,aAAa;AAG7C,WAAA,QAAQ,SAAS;AACZ,gBAAA,KAAK,QAAQ,IAAK,CAAA;AAE9B,UAAM,OAAO,CAAC,MAAmB,aAAa,OAAe;AACvD,MAAA,QAAQ,QAAQ,SAAS,QAGzB,KAAK,QAAQ,cAAc,SAG1B,aAAa,IAAI,KAAK,OAAO,KAChC,aAAa,IAAI,KAAK,SAAS,CAAE,CAAA,GAE/B,cACF,KAAK,KAAK,MAAM;AAAA,IAClB,GAEI,WAAW,CAAC,SAAqB;AAErC,MAAK,aAAa,IAAI,KAAK,OAAO,MAG9B,gBAAgBA,gBACb,KAAA,SAAS,QAAQ,QAAQ,GAEnB,aAAA,OAAO,KAAK,OAAO,GAChC,KAAK,SAAS,OAAO;AAAA,IAAA;AAEvB,QAAI,YAAY;AAChB,aAAS,IAAI,GAAG,UAAU,SAAS,GAAG,KAAK,GAAG;AAC5C,UAAI,KAAK;AACD,cAAA,IAAI,MAAM,iDAAiD;AA4B5D,WA1BG,UAAA,QAAQ,CAAC,aAA6B;AAC9C,cAAM,OAAO,KAAK,KAAK,SAAS,QAAQ,EAAI;AAC5C,QAAI,QAAQ,SAGR,KAAK,YAAY,SAAS,WACxB,SAAS,SAAS,eACpB,KAAK,KAAK,KAAK,SAAS,iBAAiB,EAAK,CAAC,GAC/C,MAAM,KAAK,SAAS,UAAU,EAAE,QAAQ,CAAC,SAAe;AACtD,gBAAM,QAAQ,KAAK,KAAK,MAAM,EAAK;AACnC,eAAK,OAAO,EAAK,GACb,iBAAiBA,gBACb,MAAA,SAAS,QAAQ,CAAC,eAAqB;AAC3C,iBAAK,YAAY,EAAK;AAAA,UAAA,CACvB;AAAA,QACH,CACD,KACQ,SAAS,SAAS,gBAC3B,KAAK,KAAK,IAAI,IAGlB,KAAK,IAAI;AAAA,MAAA,CACV,GACI,KAAA,SAAS,QAAQ,QAAQ,GAC9B,YAAY,MAAM,KAAK,KAAK,SAAS,aAAa,GAClD,UAAU,UAAU,SACb,QAAQ,SAAS;AACZ,kBAAA,KAAK,QAAQ,IAAK,CAAA;AAAA,IAEhC;AAAA,EACF;AAAA,EAEO,OACL,WACA,UAAkC,IAC5B;AACM,gBAAA,aAAa,KAAK,SAAS,YAAY;AAC7C,UAAA,mCAAmB;AAEtB,cAAA,IAAI,CAAC,aAA6B;AACjC,YAAM,OAAO,KAAK,KAAK,SAAS,QAAQ,EAAI;AAC5C,aAAI,QAAQ,OACH,OAEL,aAAa,IAAI,KAAK,OAAO,KAC/B,aAAa,IAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,GACrC,SAEP,aAAa,IAAI,KAAK,SAAS,CAAC,QAAQ,CAAC,GAClC;AAAA,IACT,CACD,EACA,QAAQ,CAAC,SAAsB;AAC1B,MAAA,QAAQ,QAAQ,SAAS,QAAQ,aAAa,IAAI,KAAK,OAAO,KAC3D,KAAA,OAAO,aAAa,IAAI,KAAK,OAAO,KAAK,IAAI,OAAO;AAAA,IAC3D,CACD,GACH,QAAQ,eAAe,cACnB,aAAa,IAAI,KAAK,OAAO,KAC/B,MAAM,OAAO,aAAa,IAAI,KAAK,OAAO,GAAG,OAAO,GAEjD,KAAA,SAAS,WAAW,OAAO;AAAA,EAClC;AACF;AAnME,YAAc,WAAW,UACzB,YAAc,eAAeI,aACf,YAAA,kBAAqC,CAACA,aAAWC,eAAa,GAC5E,YAAc,QAAQ,MAAM,YAC5B,YAAc,UAAU;AAL1B,IAAM,aAAN;AAsMA,MAAA,eAAe,YCnNT,YAAN,MAAM,kBAAiBH,WAAyB;AAAA,EAI9C,OAAc,OAAO,OAAqB;AACjC,WAAA,SAAS,eAAe,KAAK;AAAA,EACtC;AAAA,EAEA,OAAc,MAAM,SAAuB;AACzC,WAAO,QAAQ;AAAA,EACjB;AAAA,EAKA,YAAY,QAAc,MAAY;AACpC,UAAM,QAAQ,IAAI,GAClB,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,EAC7C;AAAA,EAEO,SAAS,OAAe,QAAsB;AACnD,SAAK,QAAQ,OAAO,KAAK,OACvB,KAAK,KAAK,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,QAAQ,MAAM;AAAA,EAC9D;AAAA,EAEO,MAAM,MAAY,QAAwB;AAC3C,WAAA,KAAK,YAAY,OACZ,SAEF;AAAA,EACT;AAAA,EAEO,SAAS,OAAe,OAAe,KAAiB;AAC7D,IAAI,OAAO,QACJ,KAAA,OAAO,KAAK,KAAK,MAAM,GAAG,KAAK,IAAI,QAAQ,KAAK,KAAK,MAAM,KAAK,GAChE,KAAA,QAAQ,OAAO,KAAK,QAEnB,MAAA,SAAS,OAAO,OAAO,GAAG;AAAA,EAEpC;AAAA,EAEO,SAAiB;AACtB,WAAO,KAAK,KAAK;AAAA,EACnB;AAAA,EAEO,SAAS,SAAuC;AACrD,UAAM,SAAS,OAAO,GACtB,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,GACvC,KAAK,KAAK,WAAW,IACvB,KAAK,OAAO,IACH,KAAK,gBAAgB,aAAY,KAAK,KAAK,SAAS,SAC7D,KAAK,SAAS,KAAK,OAAA,GAAW,KAAK,KAAkB,OAAO,GAC5D,KAAK,KAAK;EAEd;AAAA,EAEO,SAAS,OAAe,aAAa,IAAuB;AAC1D,WAAA,CAAC,KAAK,SAAS,KAAK;AAAA,EAC7B;AAAA,EAEO,MAAM,OAAe,QAAQ,IAAoB;AACtD,QAAI,CAAC,OAAO;AACV,UAAI,UAAU;AACL,eAAA;AAEL,UAAA,UAAU,KAAK;AACjB,eAAO,KAAK;AAAA,IAEhB;AACM,UAAA,QAAQ,KAAK,OAAO,OAAO,KAAK,QAAQ,UAAU,KAAK,CAAC;AAC9D,gBAAK,OAAO,aAAa,OAAO,KAAK,QAAQ,MAAS,GACtD,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,GACpC;AAAA,EACT;AAAA,EAEO,OACL,WACA,UACM;AAEJ,IAAA,UAAU,KAAK,CAAC,aAEZ,SAAS,SAAS,mBAAmB,SAAS,WAAW,KAAK,OAEjE,MAED,KAAK,OAAO,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,EAE/C;AAAA,EAEO,QAAgB;AACrB,WAAO,KAAK;AAAA,EACd;AACF;AA5FE,UAAuB,WAAW,QAClC,UAAc,QAAQ,MAAM;AAF9B,IAAM,WAAN;AA+FA,MAAA,aAAe;"}