{"version": 3, "file": "parchment.umd.cjs", "sources": ["../src/scope.ts", "../src/attributor/attributor.ts", "../src/error.ts", "../src/registry.ts", "../src/attributor/class.ts", "../src/attributor/style.ts", "../src/attributor/store.ts", "../src/blot/abstract/shadow.ts", "../src/blot/abstract/leaf.ts", "../src/collection/linked-list.ts", "../src/blot/abstract/parent.ts", "../src/blot/inline.ts", "../src/blot/block.ts", "../src/blot/abstract/container.ts", "../src/blot/embed.ts", "../src/blot/scroll.ts", "../src/blot/text.ts"], "sourcesContent": ["enum Scope {\n  TYPE = (1 << 2) - 1, // 0011 Lower two bits\n  LEVEL = ((1 << 2) - 1) << 2, // 1100 Higher two bits\n\n  ATTRIBUTE = (1 << 0) | LEVEL, // 1101\n  BLOT = (1 << 1) | LEVEL, // 1110\n  INLINE = (1 << 2) | TYPE, // 0111\n  BLOCK = (1 << 3) | TYPE, // 1011\n\n  BLOCK_BLOT = BLOCK & BLOT, // 1010\n  INLINE_BLOT = INLINE & BLOT, // 0110\n  BLOCK_ATTRIBUTE = BLOCK & ATTRIBUTE, // 1001\n  INLINE_ATTRIBUTE = INLINE & ATTRIBUTE, // 0101\n\n  ANY = TYPE | LEVEL,\n}\n\nexport default Scope;\n", "import Scope from '../scope.js';\n\nexport interface AttributorOptions {\n  scope?: Scope;\n  whitelist?: string[];\n}\n\nexport default class Attributor {\n  public static keys(node: HTMLElement): string[] {\n    return Array.from(node.attributes).map((item: Attr) => item.name);\n  }\n\n  public scope: Scope;\n  public whitelist: string[] | undefined;\n\n  constructor(\n    public readonly attrName: string,\n    public readonly keyName: string,\n    options: AttributorOptions = {},\n  ) {\n    const attributeBit = Scope.TYPE & Scope.ATTRIBUTE;\n    this.scope =\n      options.scope != null\n        ? // Ignore type bits, force attribute bit\n          (options.scope & Scope.LEVEL) | attributeBit\n        : Scope.ATTRIBUTE;\n    if (options.whitelist != null) {\n      this.whitelist = options.whitelist;\n    }\n  }\n\n  public add(node: HTMLElement, value: any): boolean {\n    if (!this.canAdd(node, value)) {\n      return false;\n    }\n    node.setAttribute(this.keyName, value);\n    return true;\n  }\n\n  public canAdd(_node: HTMLElement, value: any): boolean {\n    if (this.whitelist == null) {\n      return true;\n    }\n    if (typeof value === 'string') {\n      return this.whitelist.indexOf(value.replace(/[\"']/g, '')) > -1;\n    } else {\n      return this.whitelist.indexOf(value) > -1;\n    }\n  }\n\n  public remove(node: HTMLElement): void {\n    node.removeAttribute(this.keyName);\n  }\n\n  public value(node: HTMLElement): any {\n    const value = node.getAttribute(this.keyName);\n    if (this.canAdd(node, value) && value) {\n      return value;\n    }\n    return '';\n  }\n}\n", "export default class ParchmentError extends Error {\n  public message: string;\n  public name: string;\n  public stack!: string;\n\n  constructor(message: string) {\n    message = '[Parchment] ' + message;\n    super(message);\n    this.message = message;\n    this.name = this.constructor.name;\n  }\n}\n", "import Attributor from './attributor/attributor.js';\nimport {\n  type Blot,\n  type BlotConstructor,\n  type Root,\n} from './blot/abstract/blot.js';\nimport ParchmentError from './error.js';\nimport Scope from './scope.js';\n\nexport type RegistryDefinition = Attributor | BlotConstructor;\n\nexport interface RegistryInterface {\n  create(scroll: Root, input: Node | string | Scope, value?: any): Blot;\n  query(query: string | Node | Scope, scope: Scope): RegistryDefinition | null;\n  register(...definitions: any[]): any;\n}\n\nexport default class Registry implements RegistryInterface {\n  public static blots = new WeakMap<Node, Blot>();\n\n  public static find(node?: Node | null, bubble = false): Blot | null {\n    if (node == null) {\n      return null;\n    }\n    if (this.blots.has(node)) {\n      return this.blots.get(node) || null;\n    }\n    if (bubble) {\n      let parentNode: Node | null = null;\n      try {\n        parentNode = node.parentNode;\n      } catch (err) {\n        // Probably hit a permission denied error.\n        // A known case is in Firefox, event targets can be anonymous DIVs\n        // inside an input element.\n        // https://bugzilla.mozilla.org/show_bug.cgi?id=208427\n        return null;\n      }\n      return this.find(parentNode, bubble);\n    }\n    return null;\n  }\n\n  private attributes: { [key: string]: Attributor } = {};\n  private classes: { [key: string]: BlotConstructor } = {};\n  private tags: { [key: string]: BlotConstructor } = {};\n  private types: { [key: string]: RegistryDefinition } = {};\n\n  public create(scroll: Root, input: Node | string | Scope, value?: any): Blot {\n    const match = this.query(input);\n    if (match == null) {\n      throw new ParchmentError(`Unable to create ${input} blot`);\n    }\n    const blotClass = match as BlotConstructor;\n    const node =\n      // @ts-expect-error Fix me later\n      input instanceof Node || input.nodeType === Node.TEXT_NODE\n        ? input\n        : blotClass.create(value);\n\n    const blot = new blotClass(scroll, node as Node, value);\n    Registry.blots.set(blot.domNode, blot);\n    return blot;\n  }\n\n  public find(node: Node | null, bubble = false): Blot | null {\n    return Registry.find(node, bubble);\n  }\n\n  public query(\n    query: string | Node | Scope,\n    scope: Scope = Scope.ANY,\n  ): RegistryDefinition | null {\n    let match;\n    if (typeof query === 'string') {\n      match = this.types[query] || this.attributes[query];\n      // @ts-expect-error Fix me later\n    } else if (query instanceof Text || query.nodeType === Node.TEXT_NODE) {\n      match = this.types.text;\n    } else if (typeof query === 'number') {\n      if (query & Scope.LEVEL & Scope.BLOCK) {\n        match = this.types.block;\n      } else if (query & Scope.LEVEL & Scope.INLINE) {\n        match = this.types.inline;\n      }\n    } else if (query instanceof Element) {\n      const names = (query.getAttribute('class') || '').split(/\\s+/);\n      names.some((name) => {\n        match = this.classes[name];\n        if (match) {\n          return true;\n        }\n        return false;\n      });\n      match = match || this.tags[query.tagName];\n    }\n    if (match == null) {\n      return null;\n    }\n    if (\n      'scope' in match &&\n      scope & Scope.LEVEL & match.scope &&\n      scope & Scope.TYPE & match.scope\n    ) {\n      return match;\n    }\n    return null;\n  }\n\n  public register(...definitions: RegistryDefinition[]): RegistryDefinition[] {\n    return definitions.map((definition) => {\n      const isBlot = 'blotName' in definition;\n      const isAttr = 'attrName' in definition;\n      if (!isBlot && !isAttr) {\n        throw new ParchmentError('Invalid definition');\n      } else if (isBlot && definition.blotName === 'abstract') {\n        throw new ParchmentError('Cannot register abstract class');\n      }\n      const key = isBlot\n        ? definition.blotName\n        : isAttr\n          ? definition.attrName\n          : (undefined as never); // already handled by above checks\n      this.types[key] = definition;\n\n      if (isAttr) {\n        if (typeof definition.keyName === 'string') {\n          this.attributes[definition.keyName] = definition;\n        }\n      } else if (isBlot) {\n        if (definition.className) {\n          this.classes[definition.className] = definition;\n        }\n        if (definition.tagName) {\n          if (Array.isArray(definition.tagName)) {\n            definition.tagName = definition.tagName.map((tagName: string) => {\n              return tagName.toUpperCase();\n            });\n          } else {\n            definition.tagName = definition.tagName.toUpperCase();\n          }\n          const tagNames = Array.isArray(definition.tagName)\n            ? definition.tagName\n            : [definition.tagName];\n          tagNames.forEach((tag: string) => {\n            if (this.tags[tag] == null || definition.className == null) {\n              this.tags[tag] = definition;\n            }\n          });\n        }\n      }\n      return definition;\n    });\n  }\n}\n", "import Attributor from './attributor.js';\n\nfunction match(node: HTMLElement, prefix: string): string[] {\n  const className = node.getAttribute('class') || '';\n  return className\n    .split(/\\s+/)\n    .filter((name) => name.indexOf(`${prefix}-`) === 0);\n}\n\nclass ClassAttributor extends Attributor {\n  public static keys(node: HTMLElement): string[] {\n    return (node.getAttribute('class') || '')\n      .split(/\\s+/)\n      .map((name) => name.split('-').slice(0, -1).join('-'));\n  }\n\n  public add(node: HTMLElement, value: any): boolean {\n    if (!this.canAdd(node, value)) {\n      return false;\n    }\n    this.remove(node);\n    node.classList.add(`${this.keyName}-${value}`);\n    return true;\n  }\n\n  public remove(node: HTMLElement): void {\n    const matches = match(node, this.keyName);\n    matches.forEach((name) => {\n      node.classList.remove(name);\n    });\n    if (node.classList.length === 0) {\n      node.removeAttribute('class');\n    }\n  }\n\n  public value(node: HTMLElement): any {\n    const result = match(node, this.keyName)[0] || '';\n    const value = result.slice(this.keyName.length + 1); // +1 for hyphen\n    return this.canAdd(node, value) ? value : '';\n  }\n}\n\nexport default ClassAttributor;\n", "import Attributor from './attributor.js';\n\nfunction camelize(name: string): string {\n  const parts = name.split('-');\n  const rest = parts\n    .slice(1)\n    .map((part: string) => part[0].toUpperCase() + part.slice(1))\n    .join('');\n  return parts[0] + rest;\n}\n\nclass StyleAttributor extends Attributor {\n  public static keys(node: HTMLElement): string[] {\n    return (node.getAttribute('style') || '').split(';').map((value) => {\n      const arr = value.split(':');\n      return arr[0].trim();\n    });\n  }\n\n  public add(node: HTMLElement, value: any): boolean {\n    if (!this.canAdd(node, value)) {\n      return false;\n    }\n    // @ts-expect-error Fix me later\n    node.style[camelize(this.keyName)] = value;\n    return true;\n  }\n\n  public remove(node: HTMLElement): void {\n    // @ts-expect-error Fix me later\n    node.style[camelize(this.keyName)] = '';\n    if (!node.getAttribute('style')) {\n      node.removeAttribute('style');\n    }\n  }\n\n  public value(node: HTMLElement): any {\n    // @ts-expect-error Fix me later\n    const value = node.style[camelize(this.keyName)];\n    return this.canAdd(node, value) ? value : '';\n  }\n}\n\nexport default StyleAttributor;\n", "import type { Formattable } from '../blot/abstract/blot.js';\nimport Registry from '../registry.js';\nimport Scope from '../scope.js';\nimport Attributor from './attributor.js';\nimport ClassAttributor from './class.js';\nimport StyleAttributor from './style.js';\n\nclass AttributorStore {\n  private attributes: { [key: string]: Attributor } = {};\n  private domNode: HTMLElement;\n\n  constructor(domNode: HTMLElement) {\n    this.domNode = domNode;\n    this.build();\n  }\n\n  public attribute(attribute: Attributor, value: any): void {\n    // verb\n    if (value) {\n      if (attribute.add(this.domNode, value)) {\n        if (attribute.value(this.domNode) != null) {\n          this.attributes[attribute.attrName] = attribute;\n        } else {\n          delete this.attributes[attribute.attrName];\n        }\n      }\n    } else {\n      attribute.remove(this.domNode);\n      delete this.attributes[attribute.attrName];\n    }\n  }\n\n  public build(): void {\n    this.attributes = {};\n    const blot = Registry.find(this.domNode);\n    if (blot == null) {\n      return;\n    }\n    const attributes = Attributor.keys(this.domNode);\n    const classes = ClassAttributor.keys(this.domNode);\n    const styles = StyleAttributor.keys(this.domNode);\n    attributes\n      .concat(classes)\n      .concat(styles)\n      .forEach((name) => {\n        const attr = blot.scroll.query(name, Scope.ATTRIBUTE);\n        if (attr instanceof Attributor) {\n          this.attributes[attr.attrName] = attr;\n        }\n      });\n  }\n\n  public copy(target: Formattable): void {\n    Object.keys(this.attributes).forEach((key) => {\n      const value = this.attributes[key].value(this.domNode);\n      target.format(key, value);\n    });\n  }\n\n  public move(target: Formattable): void {\n    this.copy(target);\n    Object.keys(this.attributes).forEach((key) => {\n      this.attributes[key].remove(this.domNode);\n    });\n    this.attributes = {};\n  }\n\n  public values(): { [key: string]: any } {\n    return Object.keys(this.attributes).reduce(\n      (attributes: { [key: string]: any }, name: string) => {\n        attributes[name] = this.attributes[name].value(this.domNode);\n        return attributes;\n      },\n      {},\n    );\n  }\n}\n\nexport default AttributorStore;\n", "import ParchmentError from '../../error.js';\nimport Registry from '../../registry.js';\nimport Scope from '../../scope.js';\nimport type {\n  Blot,\n  BlotConstructor,\n  Formattable,\n  Parent,\n  Root,\n} from './blot.js';\n\nclass ShadowBlot implements Blot {\n  public static blotName = 'abstract';\n  public static className: string;\n  public static requiredContainer: BlotConstructor;\n  public static scope: Scope;\n  public static tagName: string | string[];\n\n  public static create(rawValue?: unknown): Node {\n    if (this.tagName == null) {\n      throw new ParchmentError('Blot definition missing tagName');\n    }\n    let node: HTMLElement;\n    let value: string | number | undefined;\n    if (Array.isArray(this.tagName)) {\n      if (typeof rawValue === 'string') {\n        value = rawValue.toUpperCase();\n        if (parseInt(value, 10).toString() === value) {\n          value = parseInt(value, 10);\n        }\n      } else if (typeof rawValue === 'number') {\n        value = rawValue;\n      }\n      if (typeof value === 'number') {\n        node = document.createElement(this.tagName[value - 1]);\n      } else if (value && this.tagName.indexOf(value) > -1) {\n        node = document.createElement(value);\n      } else {\n        node = document.createElement(this.tagName[0]);\n      }\n    } else {\n      node = document.createElement(this.tagName);\n    }\n    if (this.className) {\n      node.classList.add(this.className);\n    }\n    return node;\n  }\n\n  public prev: Blot | null;\n  public next: Blot | null;\n  // @ts-expect-error Fix me later\n  public parent: Parent;\n\n  // Hack for accessing inherited static methods\n  get statics(): any {\n    return this.constructor;\n  }\n  constructor(\n    public scroll: Root,\n    public domNode: Node,\n  ) {\n    Registry.blots.set(domNode, this);\n    this.prev = null;\n    this.next = null;\n  }\n\n  public attach(): void {\n    // Nothing to do\n  }\n\n  public clone(): Blot {\n    const domNode = this.domNode.cloneNode(false);\n    return this.scroll.create(domNode);\n  }\n\n  public detach(): void {\n    if (this.parent != null) {\n      this.parent.removeChild(this);\n    }\n    Registry.blots.delete(this.domNode);\n  }\n\n  public deleteAt(index: number, length: number): void {\n    const blot = this.isolate(index, length);\n    blot.remove();\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    const blot = this.isolate(index, length);\n    if (this.scroll.query(name, Scope.BLOT) != null && value) {\n      blot.wrap(name, value);\n    } else if (this.scroll.query(name, Scope.ATTRIBUTE) != null) {\n      const parent = this.scroll.create(this.statics.scope) as Parent &\n        Formattable;\n      blot.wrap(parent);\n      parent.format(name, value);\n    }\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    const blot =\n      def == null\n        ? this.scroll.create('text', value)\n        : this.scroll.create(value, def);\n    const ref = this.split(index);\n    this.parent.insertBefore(blot, ref || undefined);\n  }\n\n  public isolate(index: number, length: number): Blot {\n    const target = this.split(index);\n    if (target == null) {\n      throw new Error('Attempt to isolate at end');\n    }\n    target.split(length);\n    return target;\n  }\n\n  public length(): number {\n    return 1;\n  }\n\n  public offset(root: Blot = this.parent): number {\n    if (this.parent == null || this === root) {\n      return 0;\n    }\n    return this.parent.children.offset(this) + this.parent.offset(root);\n  }\n\n  public optimize(_context?: { [key: string]: any }): void {\n    if (\n      this.statics.requiredContainer &&\n      !(this.parent instanceof this.statics.requiredContainer)\n    ) {\n      this.wrap(this.statics.requiredContainer.blotName);\n    }\n  }\n\n  public remove(): void {\n    if (this.domNode.parentNode != null) {\n      this.domNode.parentNode.removeChild(this.domNode);\n    }\n    this.detach();\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement =\n      typeof name === 'string' ? this.scroll.create(name, value) : name;\n    if (this.parent != null) {\n      this.parent.insertBefore(replacement, this.next || undefined);\n      this.remove();\n    }\n    return replacement;\n  }\n\n  public split(index: number, _force?: boolean): Blot | null {\n    return index === 0 ? this : this.next;\n  }\n\n  public update(\n    _mutations: MutationRecord[],\n    _context: { [key: string]: any },\n  ): void {\n    // Nothing to do by default\n  }\n\n  public wrap(name: string | Parent, value?: any): Parent {\n    const wrapper =\n      typeof name === 'string'\n        ? (this.scroll.create(name, value) as Parent)\n        : name;\n    if (this.parent != null) {\n      this.parent.insertBefore(wrapper, this.next || undefined);\n    }\n    if (typeof wrapper.appendChild !== 'function') {\n      throw new ParchmentError(`Cannot wrap ${name}`);\n    }\n    wrapper.appendChild(this);\n    return wrapper;\n  }\n}\n\nexport default ShadowBlot;\n", "import Scope from '../../scope.js';\nimport type { Leaf } from './blot.js';\nimport ShadowBlot from './shadow.js';\n\nclass LeafBlot extends ShadowBlot implements Leaf {\n  public static scope = Scope.INLINE_BLOT;\n\n  /**\n   * Returns the value represented by domNode if it is this Blot's type\n   * No checking that domNode can represent this Blot type is required so\n   * applications needing it should check externally before calling.\n   */\n  public static value(_domNode: Node): any {\n    return true;\n  }\n\n  /**\n   * Given location represented by node and offset from DOM Selection Range,\n   * return index to that location.\n   */\n  public index(node: Node, offset: number): number {\n    if (\n      this.domNode === node ||\n      this.domNode.compareDocumentPosition(node) &\n        Node.DOCUMENT_POSITION_CONTAINED_BY\n    ) {\n      return Math.min(offset, 1);\n    }\n    return -1;\n  }\n\n  /**\n   * Given index to location within blot, return node and offset representing\n   * that location, consumable by DOM Selection Range\n   */\n  public position(index: number, _inclusive?: boolean): [Node, number] {\n    const childNodes: Node[] = Array.from(this.parent.domNode.childNodes);\n    let offset = childNodes.indexOf(this.domNode);\n    if (index > 0) {\n      offset += 1;\n    }\n    return [this.parent.domNode, offset];\n  }\n\n  /**\n   * Return value represented by this blot\n   * Should not change without interaction from API or\n   * user change detectable by update()\n   */\n  public value(): any {\n    return {\n      [this.statics.blotName]: this.statics.value(this.domNode) || true,\n    };\n  }\n}\n\nexport default LeafBlot;\n", "import type LinkedNode from './linked-node.js';\n\nclass LinkedList<T extends LinkedNode> {\n  public head: T | null;\n  public tail: T | null;\n  public length: number;\n\n  constructor() {\n    this.head = null;\n    this.tail = null;\n    this.length = 0;\n  }\n\n  public append(...nodes: T[]): void {\n    this.insertBefore(nodes[0], null);\n    if (nodes.length > 1) {\n      const rest = nodes.slice(1);\n      this.append(...rest);\n    }\n  }\n\n  public at(index: number): T | null {\n    const next = this.iterator();\n    let cur = next();\n    while (cur && index > 0) {\n      index -= 1;\n      cur = next();\n    }\n    return cur;\n  }\n\n  public contains(node: T): boolean {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      if (cur === node) {\n        return true;\n      }\n      cur = next();\n    }\n    return false;\n  }\n\n  public indexOf(node: T): number {\n    const next = this.iterator();\n    let cur = next();\n    let index = 0;\n    while (cur) {\n      if (cur === node) {\n        return index;\n      }\n      index += 1;\n      cur = next();\n    }\n    return -1;\n  }\n\n  public insertBefore(node: T | null, refNode: T | null): void {\n    if (node == null) {\n      return;\n    }\n    this.remove(node);\n    node.next = refNode;\n    if (refNode != null) {\n      node.prev = refNode.prev;\n      if (refNode.prev != null) {\n        refNode.prev.next = node;\n      }\n      refNode.prev = node;\n      if (refNode === this.head) {\n        this.head = node;\n      }\n    } else if (this.tail != null) {\n      this.tail.next = node;\n      node.prev = this.tail;\n      this.tail = node;\n    } else {\n      node.prev = null;\n      this.head = this.tail = node;\n    }\n    this.length += 1;\n  }\n\n  public offset(target: T): number {\n    let index = 0;\n    let cur = this.head;\n    while (cur != null) {\n      if (cur === target) {\n        return index;\n      }\n      index += cur.length();\n      cur = cur.next as T;\n    }\n    return -1;\n  }\n\n  public remove(node: T): void {\n    if (!this.contains(node)) {\n      return;\n    }\n    if (node.prev != null) {\n      node.prev.next = node.next;\n    }\n    if (node.next != null) {\n      node.next.prev = node.prev;\n    }\n    if (node === this.head) {\n      this.head = node.next as T;\n    }\n    if (node === this.tail) {\n      this.tail = node.prev as T;\n    }\n    this.length -= 1;\n  }\n\n  public iterator(curNode: T | null = this.head): () => T | null {\n    // TODO use yield when we can\n    return (): T | null => {\n      const ret = curNode;\n      if (curNode != null) {\n        curNode = curNode.next as T;\n      }\n      return ret;\n    };\n  }\n\n  public find(index: number, inclusive = false): [T | null, number] {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      const length = cur.length();\n      if (\n        index < length ||\n        (inclusive &&\n          index === length &&\n          (cur.next == null || cur.next.length() !== 0))\n      ) {\n        return [cur, index];\n      }\n      index -= length;\n      cur = next();\n    }\n    return [null, 0];\n  }\n\n  public forEach(callback: (cur: T) => void): void {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      callback(cur);\n      cur = next();\n    }\n  }\n\n  public forEachAt(\n    index: number,\n    length: number,\n    callback: (cur: T, offset: number, length: number) => void,\n  ): void {\n    if (length <= 0) {\n      return;\n    }\n    const [startNode, offset] = this.find(index);\n    let curIndex = index - offset;\n    const next = this.iterator(startNode);\n    let cur = next();\n    while (cur && curIndex < index + length) {\n      const curLength = cur.length();\n      if (index > curIndex) {\n        callback(\n          cur,\n          index - curIndex,\n          Math.min(length, curIndex + curLength - index),\n        );\n      } else {\n        callback(cur, 0, Math.min(curLength, index + length - curIndex));\n      }\n      curIndex += curLength;\n      cur = next();\n    }\n  }\n\n  public map(callback: (cur: T) => any): any[] {\n    return this.reduce((memo: T[], cur: T) => {\n      memo.push(callback(cur));\n      return memo;\n    }, []);\n  }\n\n  public reduce<M>(callback: (memo: M, cur: T) => M, memo: M): M {\n    const next = this.iterator();\n    let cur = next();\n    while (cur) {\n      memo = callback(memo, cur);\n      cur = next();\n    }\n    return memo;\n  }\n}\n\nexport default LinkedList;\n", "import LinkedList from '../../collection/linked-list.js';\nimport ParchmentError from '../../error.js';\nimport Scope from '../../scope.js';\nimport type { Blot, BlotConstructor, Parent, Root } from './blot.js';\nimport ShadowBlot from './shadow.js';\n\nfunction makeAttachedBlot(node: Node, scroll: Root): Blot {\n  const found = scroll.find(node);\n  if (found) return found;\n  try {\n    return scroll.create(node);\n  } catch (e) {\n    const blot = scroll.create(Scope.INLINE);\n    Array.from(node.childNodes).forEach((child: Node) => {\n      blot.domNode.appendChild(child);\n    });\n    if (node.parentNode) {\n      node.parentNode.replaceChild(blot.domNode, node);\n    }\n    blot.attach();\n    return blot;\n  }\n}\n\nclass ParentBlot extends ShadowBlot implements Parent {\n  /**\n   * Whitelist array of Blots that can be direct children.\n   */\n  public static allowedChildren?: BlotConstructor[];\n\n  /**\n   * Default child blot to be inserted if this blot becomes empty.\n   */\n  public static defaultChild?: BlotConstructor;\n  public static uiClass = '';\n\n  public children!: LinkedList<Blot>;\n  public domNode!: HTMLElement;\n  public uiNode: HTMLElement | null = null;\n\n  constructor(scroll: Root, domNode: Node) {\n    super(scroll, domNode);\n    this.build();\n  }\n\n  public appendChild(other: Blot): void {\n    this.insertBefore(other);\n  }\n\n  public attach(): void {\n    super.attach();\n    this.children.forEach((child) => {\n      child.attach();\n    });\n  }\n\n  public attachUI(node: HTMLElement): void {\n    if (this.uiNode != null) {\n      this.uiNode.remove();\n    }\n    this.uiNode = node;\n    if (ParentBlot.uiClass) {\n      this.uiNode.classList.add(ParentBlot.uiClass);\n    }\n    this.uiNode.setAttribute('contenteditable', 'false');\n    this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n  }\n\n  /**\n   * Called during construction, should fill its own children LinkedList.\n   */\n  public build(): void {\n    this.children = new LinkedList<Blot>();\n    // Need to be reversed for if DOM nodes already in order\n    Array.from(this.domNode.childNodes)\n      .filter((node: Node) => node !== this.uiNode)\n      .reverse()\n      .forEach((node: Node) => {\n        try {\n          const child = makeAttachedBlot(node, this.scroll);\n          this.insertBefore(child, this.children.head || undefined);\n        } catch (err) {\n          if (err instanceof ParchmentError) {\n            return;\n          } else {\n            throw err;\n          }\n        }\n      });\n  }\n\n  public deleteAt(index: number, length: number): void {\n    if (index === 0 && length === this.length()) {\n      return this.remove();\n    }\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.deleteAt(offset, childLength);\n    });\n  }\n\n  public descendant<T extends Blot>(\n    criteria: new (...args: any[]) => T,\n    index: number,\n  ): [T | null, number];\n  public descendant(\n    criteria: (blot: Blot) => boolean,\n    index: number,\n  ): [Blot | null, number];\n  public descendant(criteria: any, index = 0): [Blot | null, number] {\n    const [child, offset] = this.children.find(index);\n    if (\n      (criteria.blotName == null && criteria(child)) ||\n      (criteria.blotName != null && child instanceof criteria)\n    ) {\n      return [child as any, offset];\n    } else if (child instanceof ParentBlot) {\n      return child.descendant(criteria, offset);\n    } else {\n      return [null, -1];\n    }\n  }\n\n  public descendants<T extends Blot>(\n    criteria: new (...args: any[]) => T,\n    index?: number,\n    length?: number,\n  ): T[];\n  public descendants(\n    criteria: (blot: Blot) => boolean,\n    index?: number,\n    length?: number,\n  ): Blot[];\n  public descendants(\n    criteria: any,\n    index = 0,\n    length: number = Number.MAX_VALUE,\n  ): Blot[] {\n    let descendants: Blot[] = [];\n    let lengthLeft = length;\n    this.children.forEachAt(\n      index,\n      length,\n      (child: Blot, childIndex: number, childLength: number) => {\n        if (\n          (criteria.blotName == null && criteria(child)) ||\n          (criteria.blotName != null && child instanceof criteria)\n        ) {\n          descendants.push(child);\n        }\n        if (child instanceof ParentBlot) {\n          descendants = descendants.concat(\n            child.descendants(criteria, childIndex, lengthLeft),\n          );\n        }\n        lengthLeft -= childLength;\n      },\n    );\n    return descendants;\n  }\n\n  public detach(): void {\n    this.children.forEach((child) => {\n      child.detach();\n    });\n    super.detach();\n  }\n\n  public enforceAllowedChildren(): void {\n    let done = false;\n    this.children.forEach((child: Blot) => {\n      if (done) {\n        return;\n      }\n      const allowed = this.statics.allowedChildren.some(\n        (def: BlotConstructor) => child instanceof def,\n      );\n      if (allowed) {\n        return;\n      }\n      if (child.statics.scope === Scope.BLOCK_BLOT) {\n        if (child.next != null) {\n          this.splitAfter(child);\n        }\n        if (child.prev != null) {\n          this.splitAfter(child.prev);\n        }\n        child.parent.unwrap();\n        done = true;\n      } else if (child instanceof ParentBlot) {\n        child.unwrap();\n      } else {\n        child.remove();\n      }\n    });\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    this.children.forEachAt(index, length, (child, offset, childLength) => {\n      child.formatAt(offset, childLength, name, value);\n    });\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    const [child, offset] = this.children.find(index);\n    if (child) {\n      child.insertAt(offset, value, def);\n    } else {\n      const blot =\n        def == null\n          ? this.scroll.create('text', value)\n          : this.scroll.create(value, def);\n      this.appendChild(blot);\n    }\n  }\n\n  public insertBefore(childBlot: Blot, refBlot?: Blot | null): void {\n    if (childBlot.parent != null) {\n      childBlot.parent.children.remove(childBlot);\n    }\n    let refDomNode: Node | null = null;\n    this.children.insertBefore(childBlot, refBlot || null);\n    childBlot.parent = this;\n    if (refBlot != null) {\n      refDomNode = refBlot.domNode;\n    }\n    if (\n      this.domNode.parentNode !== childBlot.domNode ||\n      this.domNode.nextSibling !== refDomNode\n    ) {\n      this.domNode.insertBefore(childBlot.domNode, refDomNode);\n    }\n    childBlot.attach();\n  }\n\n  public length(): number {\n    return this.children.reduce((memo, child) => {\n      return memo + child.length();\n    }, 0);\n  }\n\n  public moveChildren(targetParent: Parent, refNode?: Blot | null): void {\n    this.children.forEach((child) => {\n      targetParent.insertBefore(child, refNode);\n    });\n  }\n\n  public optimize(context?: { [key: string]: any }): void {\n    super.optimize(context);\n    this.enforceAllowedChildren();\n    if (this.uiNode != null && this.uiNode !== this.domNode.firstChild) {\n      this.domNode.insertBefore(this.uiNode, this.domNode.firstChild);\n    }\n    if (this.children.length === 0) {\n      if (this.statics.defaultChild != null) {\n        const child = this.scroll.create(this.statics.defaultChild.blotName);\n        this.appendChild(child);\n        // TODO double check if necessary\n        // child.optimize(context);\n      } else {\n        this.remove();\n      }\n    }\n  }\n\n  public path(index: number, inclusive = false): [Blot, number][] {\n    const [child, offset] = this.children.find(index, inclusive);\n    const position: [Blot, number][] = [[this, index]];\n    if (child instanceof ParentBlot) {\n      return position.concat(child.path(offset, inclusive));\n    } else if (child != null) {\n      position.push([child, offset]);\n    }\n    return position;\n  }\n\n  public removeChild(child: Blot): void {\n    this.children.remove(child);\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement =\n      typeof name === 'string' ? this.scroll.create(name, value) : name;\n    if (replacement instanceof ParentBlot) {\n      this.moveChildren(replacement);\n    }\n    return super.replaceWith(replacement);\n  }\n\n  public split(index: number, force = false): Blot | null {\n    if (!force) {\n      if (index === 0) {\n        return this;\n      }\n      if (index === this.length()) {\n        return this.next;\n      }\n    }\n    const after = this.clone() as ParentBlot;\n    if (this.parent) {\n      this.parent.insertBefore(after, this.next || undefined);\n    }\n    this.children.forEachAt(index, this.length(), (child, offset, _length) => {\n      const split = child.split(offset, force);\n      if (split != null) {\n        after.appendChild(split);\n      }\n    });\n    return after;\n  }\n\n  public splitAfter(child: Blot): Parent {\n    const after = this.clone() as ParentBlot;\n    while (child.next != null) {\n      after.appendChild(child.next);\n    }\n    if (this.parent) {\n      this.parent.insertBefore(after, this.next || undefined);\n    }\n    return after;\n  }\n\n  public unwrap(): void {\n    if (this.parent) {\n      this.moveChildren(this.parent, this.next || undefined);\n    }\n    this.remove();\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    _context: { [key: string]: any },\n  ): void {\n    const addedNodes: Node[] = [];\n    const removedNodes: Node[] = [];\n    mutations.forEach((mutation) => {\n      if (mutation.target === this.domNode && mutation.type === 'childList') {\n        addedNodes.push(...mutation.addedNodes);\n        removedNodes.push(...mutation.removedNodes);\n      }\n    });\n    removedNodes.forEach((node: Node) => {\n      // Check node has actually been removed\n      // One exception is Chrome does not immediately remove IFRAMEs\n      // from DOM but MutationRecord is correct in its reported removal\n      if (\n        node.parentNode != null &&\n        // @ts-expect-error Fix me later\n        node.tagName !== 'IFRAME' &&\n        document.body.compareDocumentPosition(node) &\n          Node.DOCUMENT_POSITION_CONTAINED_BY\n      ) {\n        return;\n      }\n      const blot = this.scroll.find(node);\n      if (blot == null) {\n        return;\n      }\n      if (\n        blot.domNode.parentNode == null ||\n        blot.domNode.parentNode === this.domNode\n      ) {\n        blot.detach();\n      }\n    });\n    addedNodes\n      .filter((node) => {\n        return node.parentNode === this.domNode && node !== this.uiNode;\n      })\n      .sort((a, b) => {\n        if (a === b) {\n          return 0;\n        }\n        if (a.compareDocumentPosition(b) & Node.DOCUMENT_POSITION_FOLLOWING) {\n          return 1;\n        }\n        return -1;\n      })\n      .forEach((node) => {\n        let refBlot: Blot | null = null;\n        if (node.nextSibling != null) {\n          refBlot = this.scroll.find(node.nextSibling);\n        }\n        const blot = makeAttachedBlot(node, this.scroll);\n        if (blot.next !== refBlot || blot.next == null) {\n          if (blot.parent != null) {\n            blot.parent.removeChild(this);\n          }\n          this.insertBefore(blot, refBlot || undefined);\n        }\n      });\n    this.enforceAllowedChildren();\n  }\n}\n\nexport default ParentBlot;\n", "import Attributor from '../attributor/attributor.js';\nimport AttributorStore from '../attributor/store.js';\nimport Scope from '../scope.js';\nimport type {\n  Blot,\n  BlotConstructor,\n  Formattable,\n  Parent,\n  Root,\n} from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\nimport ParentBlot from './abstract/parent.js';\n\n// Shallow object comparison\nfunction isEqual(\n  obj1: Record<string, unknown>,\n  obj2: Record<string, unknown>,\n): boolean {\n  if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n    return false;\n  }\n  for (const prop in obj1) {\n    if (obj1[prop] !== obj2[prop]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nclass InlineBlot extends ParentBlot implements Formattable {\n  public static allowedChildren: BlotConstructor[] = [InlineBlot, LeafBlot];\n  public static blotName = 'inline';\n  public static scope = Scope.INLINE_BLOT;\n  public static tagName: string | string[] = 'SPAN';\n\n  static create(value?: unknown) {\n    return super.create(value) as HTMLElement;\n  }\n\n  public static formats(domNode: HTMLElement, scroll: Root): any {\n    const match = scroll.query(InlineBlot.blotName);\n    if (\n      match != null &&\n      domNode.tagName === (match as BlotConstructor).tagName\n    ) {\n      return undefined;\n    } else if (typeof this.tagName === 'string') {\n      return true;\n    } else if (Array.isArray(this.tagName)) {\n      return domNode.tagName.toLowerCase();\n    }\n    return undefined;\n  }\n\n  protected attributes: AttributorStore;\n\n  constructor(scroll: Root, domNode: Node) {\n    super(scroll, domNode);\n    this.attributes = new AttributorStore(this.domNode);\n  }\n\n  public format(name: string, value: any): void {\n    if (name === this.statics.blotName && !value) {\n      this.children.forEach((child) => {\n        if (!(child instanceof InlineBlot)) {\n          child = child.wrap(InlineBlot.blotName, true);\n        }\n        this.attributes.copy(child as InlineBlot);\n      });\n      this.unwrap();\n    } else {\n      const format = this.scroll.query(name, Scope.INLINE);\n      if (format == null) {\n        return;\n      }\n      if (format instanceof Attributor) {\n        this.attributes.attribute(format, value);\n      } else if (\n        value &&\n        (name !== this.statics.blotName || this.formats()[name] !== value)\n      ) {\n        this.replaceWith(name, value);\n      }\n    }\n  }\n\n  public formats(): { [index: string]: any } {\n    const formats = this.attributes.values();\n    const format = this.statics.formats(this.domNode, this.scroll);\n    if (format != null) {\n      formats[this.statics.blotName] = format;\n    }\n    return formats;\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    if (\n      this.formats()[name] != null ||\n      this.scroll.query(name, Scope.ATTRIBUTE)\n    ) {\n      const blot = this.isolate(index, length) as InlineBlot;\n      blot.format(name, value);\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  public optimize(context: { [key: string]: any }): void {\n    super.optimize(context);\n    const formats = this.formats();\n    if (Object.keys(formats).length === 0) {\n      return this.unwrap(); // unformatted span\n    }\n    const next = this.next;\n    if (\n      next instanceof InlineBlot &&\n      next.prev === this &&\n      isEqual(formats, next.formats())\n    ) {\n      next.moveChildren(this);\n      next.remove();\n    }\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement = super.replaceWith(name, value) as InlineBlot;\n    this.attributes.copy(replacement);\n    return replacement;\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    context: { [key: string]: any },\n  ): void {\n    super.update(mutations, context);\n    const attributeChanged = mutations.some(\n      (mutation) =>\n        mutation.target === this.domNode && mutation.type === 'attributes',\n    );\n    if (attributeChanged) {\n      this.attributes.build();\n    }\n  }\n\n  public wrap(name: string | Parent, value?: any): Parent {\n    const wrapper = super.wrap(name, value);\n    if (wrapper instanceof InlineBlot) {\n      this.attributes.move(wrapper);\n    }\n    return wrapper;\n  }\n}\n\nexport default InlineBlot;\n", "import Attributor from '../attributor/attributor.js';\nimport AttributorStore from '../attributor/store.js';\nimport Scope from '../scope.js';\nimport type {\n  Blot,\n  BlotConstructor,\n  Formattable,\n  Root,\n} from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\nimport ParentBlot from './abstract/parent.js';\nimport InlineBlot from './inline.js';\n\nclass BlockBlot extends ParentBlot implements Formattable {\n  public static blotName = 'block';\n  public static scope = Scope.BLOCK_BLOT;\n  public static tagName: string | string[] = 'P';\n  public static allowedChildren: BlotConstructor[] = [\n    InlineBlot,\n    BlockBlot,\n    LeafBlot,\n  ];\n\n  static create(value?: unknown) {\n    return super.create(value) as HTMLElement;\n  }\n\n  public static formats(domNode: HTMLElement, scroll: Root): any {\n    const match = scroll.query(BlockBlot.blotName);\n    if (\n      match != null &&\n      domNode.tagName === (match as BlotConstructor).tagName\n    ) {\n      return undefined;\n    } else if (typeof this.tagName === 'string') {\n      return true;\n    } else if (Array.isArray(this.tagName)) {\n      return domNode.tagName.toLowerCase();\n    }\n  }\n\n  protected attributes: AttributorStore;\n\n  constructor(scroll: Root, domNode: Node) {\n    super(scroll, domNode);\n    this.attributes = new AttributorStore(this.domNode);\n  }\n\n  public format(name: string, value: any): void {\n    const format = this.scroll.query(name, Scope.BLOCK);\n    if (format == null) {\n      return;\n    } else if (format instanceof Attributor) {\n      this.attributes.attribute(format, value);\n    } else if (name === this.statics.blotName && !value) {\n      this.replaceWith(BlockBlot.blotName);\n    } else if (\n      value &&\n      (name !== this.statics.blotName || this.formats()[name] !== value)\n    ) {\n      this.replaceWith(name, value);\n    }\n  }\n\n  public formats(): { [index: string]: any } {\n    const formats = this.attributes.values();\n    const format = this.statics.formats(this.domNode, this.scroll);\n    if (format != null) {\n      formats[this.statics.blotName] = format;\n    }\n    return formats;\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    if (this.scroll.query(name, Scope.BLOCK) != null) {\n      this.format(name, value);\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    if (def == null || this.scroll.query(value, Scope.INLINE) != null) {\n      // Insert text or inline\n      super.insertAt(index, value, def);\n    } else {\n      const after = this.split(index);\n      if (after != null) {\n        const blot = this.scroll.create(value, def);\n        after.parent.insertBefore(blot, after);\n      } else {\n        throw new Error('Attempt to insertAt after block boundaries');\n      }\n    }\n  }\n\n  public replaceWith(name: string | Blot, value?: any): Blot {\n    const replacement = super.replaceWith(name, value) as BlockBlot;\n    this.attributes.copy(replacement);\n    return replacement;\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    context: { [key: string]: any },\n  ): void {\n    super.update(mutations, context);\n    const attributeChanged = mutations.some(\n      (mutation) =>\n        mutation.target === this.domNode && mutation.type === 'attributes',\n    );\n    if (attributeChanged) {\n      this.attributes.build();\n    }\n  }\n}\n\nexport default BlockBlot;\n", "import Scope from '../../scope.js';\nimport BlockBlot from '../block.js';\nimport ParentBlot from './parent.js';\n\nclass ContainerBlot extends ParentBlot {\n  public static blotName = 'container';\n  public static scope = Scope.BLOCK_BLOT;\n  public static tagName: string | string[];\n\n  public prev!: BlockBlot | ContainerBlot | null;\n  public next!: BlockBlot | ContainerBlot | null;\n\n  public checkMerge(): boolean {\n    return (\n      this.next !== null && this.next.statics.blotName === this.statics.blotName\n    );\n  }\n\n  public deleteAt(index: number, length: number): void {\n    super.deleteAt(index, length);\n    this.enforceAllowedChildren();\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    super.formatAt(index, length, name, value);\n    this.enforceAllowedChildren();\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    super.insertAt(index, value, def);\n    this.enforceAllowedChildren();\n  }\n\n  public optimize(context: { [key: string]: any }): void {\n    super.optimize(context);\n    if (this.children.length > 0 && this.next != null && this.checkMerge()) {\n      this.next.moveChildren(this);\n      this.next.remove();\n    }\n  }\n}\n\nexport default ContainerBlot;\n", "import type { Formattable, Root } from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\n\nclass EmbedBlot extends LeafBlot implements Formattable {\n  public static formats(_domNode: HTMLElement, _scroll: Root): any {\n    return undefined;\n  }\n\n  public format(name: string, value: any): void {\n    // super.formatAt wraps, which is what we want in general,\n    // but this allows subclasses to overwrite for formats\n    // that just apply to particular embeds\n    super.formatAt(0, this.length(), name, value);\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    if (index === 0 && length === this.length()) {\n      this.format(name, value);\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  public formats(): { [index: string]: any } {\n    return this.statics.formats(this.domNode, this.scroll);\n  }\n}\n\nexport default EmbedBlot;\n", "import Registry, { type RegistryDefinition } from '../registry.js';\nimport Scope from '../scope.js';\nimport type { Blot, BlotConstructor, Root } from './abstract/blot.js';\nimport ContainerBlot from './abstract/container.js';\nimport ParentBlot from './abstract/parent.js';\nimport BlockBlot from './block.js';\n\nconst OBSERVER_CONFIG = {\n  attributes: true,\n  characterData: true,\n  characterDataOldValue: true,\n  childList: true,\n  subtree: true,\n};\n\nconst MAX_OPTIMIZE_ITERATIONS = 100;\n\nclass ScrollBlot extends ParentBlot implements Root {\n  public static blotName = 'scroll';\n  public static defaultChild = BlockBlot;\n  public static allowedChildren: BlotConstructor[] = [BlockBlot, ContainerBlot];\n  public static scope = Scope.BLOCK_BLOT;\n  public static tagName = 'DIV';\n\n  public observer: MutationObserver;\n\n  constructor(\n    public registry: Registry,\n    node: HTMLDivElement,\n  ) {\n    // @ts-expect-error scroll is the root with no parent\n    super(null, node);\n    this.scroll = this;\n    this.build();\n    this.observer = new MutationObserver((mutations: MutationRecord[]) => {\n      this.update(mutations);\n    });\n    this.observer.observe(this.domNode, OBSERVER_CONFIG);\n    this.attach();\n  }\n\n  public create(input: Node | string | Scope, value?: any): Blot {\n    return this.registry.create(this, input, value);\n  }\n\n  public find(node: Node | null, bubble = false): Blot | null {\n    const blot = this.registry.find(node, bubble);\n    if (!blot) {\n      return null;\n    }\n    if (blot.scroll === this) {\n      return blot;\n    }\n    return bubble ? this.find(blot.scroll.domNode.parentNode, true) : null;\n  }\n\n  public query(\n    query: string | Node | Scope,\n    scope: Scope = Scope.ANY,\n  ): RegistryDefinition | null {\n    return this.registry.query(query, scope);\n  }\n\n  public register(...definitions: RegistryDefinition[]) {\n    return this.registry.register(...definitions);\n  }\n\n  public build(): void {\n    if (this.scroll == null) {\n      return;\n    }\n    super.build();\n  }\n\n  public detach(): void {\n    super.detach();\n    this.observer.disconnect();\n  }\n\n  public deleteAt(index: number, length: number): void {\n    this.update();\n    if (index === 0 && length === this.length()) {\n      this.children.forEach((child) => {\n        child.remove();\n      });\n    } else {\n      super.deleteAt(index, length);\n    }\n  }\n\n  public formatAt(\n    index: number,\n    length: number,\n    name: string,\n    value: any,\n  ): void {\n    this.update();\n    super.formatAt(index, length, name, value);\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    this.update();\n    super.insertAt(index, value, def);\n  }\n\n  public optimize(context?: { [key: string]: any }): void;\n  public optimize(\n    mutations: MutationRecord[],\n    context: { [key: string]: any },\n  ): void;\n  public optimize(mutations: any = [], context: any = {}): void {\n    super.optimize(context);\n    const mutationsMap = context.mutationsMap || new WeakMap();\n    // We must modify mutations directly, cannot make copy and then modify\n    let records = Array.from(this.observer.takeRecords());\n    // Array.push currently seems to be implemented by a non-tail recursive function\n    // so we cannot just mutations.push.apply(mutations, this.observer.takeRecords());\n    while (records.length > 0) {\n      mutations.push(records.pop());\n    }\n    const mark = (blot: Blot | null, markParent = true): void => {\n      if (blot == null || blot === this) {\n        return;\n      }\n      if (blot.domNode.parentNode == null) {\n        return;\n      }\n      if (!mutationsMap.has(blot.domNode)) {\n        mutationsMap.set(blot.domNode, []);\n      }\n      if (markParent) {\n        mark(blot.parent);\n      }\n    };\n    const optimize = (blot: Blot): void => {\n      // Post-order traversal\n      if (!mutationsMap.has(blot.domNode)) {\n        return;\n      }\n      if (blot instanceof ParentBlot) {\n        blot.children.forEach(optimize);\n      }\n      mutationsMap.delete(blot.domNode);\n      blot.optimize(context);\n    };\n    let remaining = mutations;\n    for (let i = 0; remaining.length > 0; i += 1) {\n      if (i >= MAX_OPTIMIZE_ITERATIONS) {\n        throw new Error('[Parchment] Maximum optimize iterations reached');\n      }\n      remaining.forEach((mutation: MutationRecord) => {\n        const blot = this.find(mutation.target, true);\n        if (blot == null) {\n          return;\n        }\n        if (blot.domNode === mutation.target) {\n          if (mutation.type === 'childList') {\n            mark(this.find(mutation.previousSibling, false));\n            Array.from(mutation.addedNodes).forEach((node: Node) => {\n              const child = this.find(node, false);\n              mark(child, false);\n              if (child instanceof ParentBlot) {\n                child.children.forEach((grandChild: Blot) => {\n                  mark(grandChild, false);\n                });\n              }\n            });\n          } else if (mutation.type === 'attributes') {\n            mark(blot.prev);\n          }\n        }\n        mark(blot);\n      });\n      this.children.forEach(optimize);\n      remaining = Array.from(this.observer.takeRecords());\n      records = remaining.slice();\n      while (records.length > 0) {\n        mutations.push(records.pop());\n      }\n    }\n  }\n\n  public update(\n    mutations?: MutationRecord[],\n    context: { [key: string]: any } = {},\n  ): void {\n    mutations = mutations || this.observer.takeRecords();\n    const mutationsMap = new WeakMap();\n    mutations\n      .map((mutation: MutationRecord) => {\n        const blot = this.find(mutation.target, true);\n        if (blot == null) {\n          return null;\n        }\n        if (mutationsMap.has(blot.domNode)) {\n          mutationsMap.get(blot.domNode).push(mutation);\n          return null;\n        } else {\n          mutationsMap.set(blot.domNode, [mutation]);\n          return blot;\n        }\n      })\n      .forEach((blot: Blot | null) => {\n        if (blot != null && blot !== this && mutationsMap.has(blot.domNode)) {\n          blot.update(mutationsMap.get(blot.domNode) || [], context);\n        }\n      });\n    context.mutationsMap = mutationsMap;\n    if (mutationsMap.has(this.domNode)) {\n      super.update(mutationsMap.get(this.domNode), context);\n    }\n    this.optimize(mutations, context);\n  }\n}\n\nexport default ScrollBlot;\n", "import Scope from '../scope.js';\nimport type { Blot, Leaf, Root } from './abstract/blot.js';\nimport LeafBlot from './abstract/leaf.js';\n\nclass TextBlot extends LeafBlot implements Leaf {\n  public static readonly blotName = 'text';\n  public static scope = Scope.INLINE_BLOT;\n\n  public static create(value: string): Text {\n    return document.createTextNode(value);\n  }\n\n  public static value(domNode: Text): string {\n    return domNode.data;\n  }\n\n  public domNode!: Text;\n  protected text: string;\n\n  constructor(scroll: Root, node: Node) {\n    super(scroll, node);\n    this.text = this.statics.value(this.domNode);\n  }\n\n  public deleteAt(index: number, length: number): void {\n    this.domNode.data = this.text =\n      this.text.slice(0, index) + this.text.slice(index + length);\n  }\n\n  public index(node: Node, offset: number): number {\n    if (this.domNode === node) {\n      return offset;\n    }\n    return -1;\n  }\n\n  public insertAt(index: number, value: string, def?: any): void {\n    if (def == null) {\n      this.text = this.text.slice(0, index) + value + this.text.slice(index);\n      this.domNode.data = this.text;\n    } else {\n      super.insertAt(index, value, def);\n    }\n  }\n\n  public length(): number {\n    return this.text.length;\n  }\n\n  public optimize(context: { [key: string]: any }): void {\n    super.optimize(context);\n    this.text = this.statics.value(this.domNode);\n    if (this.text.length === 0) {\n      this.remove();\n    } else if (this.next instanceof TextBlot && this.next.prev === this) {\n      this.insertAt(this.length(), (this.next as TextBlot).value());\n      this.next.remove();\n    }\n  }\n\n  public position(index: number, _inclusive = false): [Node, number] {\n    return [this.domNode, index];\n  }\n\n  public split(index: number, force = false): Blot | null {\n    if (!force) {\n      if (index === 0) {\n        return this;\n      }\n      if (index === this.length()) {\n        return this.next;\n      }\n    }\n    const after = this.scroll.create(this.domNode.splitText(index));\n    this.parent.insertBefore(after, this.next || undefined);\n    this.text = this.statics.value(this.domNode);\n    return after;\n  }\n\n  public update(\n    mutations: MutationRecord[],\n    _context: { [key: string]: any },\n  ): void {\n    if (\n      mutations.some((mutation) => {\n        return (\n          mutation.type === 'characterData' && mutation.target === this.domNode\n        );\n      })\n    ) {\n      this.text = this.statics.value(this.domNode);\n    }\n  }\n\n  public value(): string {\n    return this.text;\n  }\n}\n\nexport default TextBlot;\n"], "names": ["<PERSON><PERSON>", "match", "ClassAttributor", "StyleAttributor", "ParentBlot", "AttributorStore", "LeafBlot", "InlineBlot", "BlockBlot", "ContainerBlot"], "mappings": "oRAAK,IAAA,OAAAA,SACHA,OAAAA,OAAA,KAAQ,CAAR,EAAA,OACAA,OAAAA,OAAA,MAAU,EAAV,EAAA,QAEAA,OAAAA,OAAA,UAAa,EAAb,EAAA,YACAA,OAAAA,OAAA,KAAQ,EAAR,EAAA,OACAA,OAAAA,OAAA,OAAU,CAAV,EAAA,SACAA,OAAAA,OAAA,MAAS,EAAT,EAAA,QAEAA,OAAAA,OAAA,WAAa,EAAb,EAAA,aACAA,OAAAA,OAAA,YAAc,CAAd,EAAA,cACAA,OAAAA,OAAA,gBAAkB,CAAlB,EAAA,kBACAA,OAAAA,OAAA,iBAAmB,CAAnB,EAAA,mBAEAA,OAAAA,OAAA,IAAM,EAAN,EAAA,MAdGA,SAAA,OAAA,CAAA,CAAA,ECOL,MAAqB,UAAW,CAQ9B,YACkB,SACA,QAChB,QAA6B,CAAA,EAC7B,CAHgB,KAAA,SAAA,SACA,KAAA,QAAA,QAGV,MAAA,aAAe,MAAM,KAAO,MAAM,UACnC,KAAA,MACH,QAAQ,OAAS,KAEZ,QAAQ,MAAQ,MAAM,MAAS,aAChC,MAAM,UACR,QAAQ,WAAa,OACvB,KAAK,UAAY,QAAQ,UAE7B,CArBA,OAAc,KAAK,KAA6B,CACvC,OAAA,MAAM,KAAK,KAAK,UAAU,EAAE,IAAK,MAAe,KAAK,IAAI,CAClE,CAqBO,IAAI,KAAmB,MAAqB,CACjD,OAAK,KAAK,OAAO,KAAM,KAAK,GAGvB,KAAA,aAAa,KAAK,QAAS,KAAK,EAC9B,IAHE,EAIX,CAEO,OAAO,MAAoB,MAAqB,CACjD,OAAA,KAAK,WAAa,KACb,GAEL,OAAO,OAAU,SACZ,KAAK,UAAU,QAAQ,MAAM,QAAQ,QAAS,EAAE,CAAC,EAAI,GAErD,KAAK,UAAU,QAAQ,KAAK,EAAI,EAE3C,CAEO,OAAO,KAAyB,CAChC,KAAA,gBAAgB,KAAK,OAAO,CACnC,CAEO,MAAM,KAAwB,CACnC,MAAM,MAAQ,KAAK,aAAa,KAAK,OAAO,EAC5C,OAAI,KAAK,OAAO,KAAM,KAAK,GAAK,MACvB,MAEF,EACT,CACF,CC7DA,MAAqB,uBAAuB,KAAM,CAKhD,YAAY,QAAiB,CAC3B,QAAU,eAAiB,QAC3B,MAAM,OAAO,EACb,KAAK,QAAU,QACV,KAAA,KAAO,KAAK,YAAY,IAC/B,CACF,CCMA,MAAqB,UAArB,MAAqB,SAAsC,CAA3D,aAAA,CA0BE,KAAQ,WAA4C,GACpD,KAAQ,QAA8C,GACtD,KAAQ,KAA2C,GACnD,KAAQ,MAA+C,EAAC,CA1BxD,OAAc,KAAK,KAAoB,OAAS,GAAoB,CAClE,GAAI,MAAQ,KACH,OAAA,KAET,GAAI,KAAK,MAAM,IAAI,IAAI,EACrB,OAAO,KAAK,MAAM,IAAI,IAAI,GAAK,KAEjC,GAAI,OAAQ,CACV,IAAI,WAA0B,KAC1B,GAAA,CACF,WAAa,KAAK,gBACN,CAKL,OAAA,IACT,CACO,OAAA,KAAK,KAAK,WAAY,MAAM,CACrC,CACO,OAAA,IACT,CAOO,OAAO,OAAc,MAA8B,MAAmB,CACrE,MAAAC,OAAQ,KAAK,MAAM,KAAK,EAC9B,GAAIA,QAAS,KACX,MAAM,IAAI,eAAe,oBAAoB,KAAK,OAAO,EAE3D,MAAM,UAAYA,OACZ,KAEJ,iBAAiB,MAAQ,MAAM,WAAa,KAAK,UAC7C,MACA,UAAU,OAAO,KAAK,EAEtB,KAAO,IAAI,UAAU,OAAQ,KAAc,KAAK,EACtD,iBAAS,MAAM,IAAI,KAAK,QAAS,IAAI,EAC9B,IACT,CAEO,KAAK,KAAmB,OAAS,GAAoB,CACnD,OAAA,UAAS,KAAK,KAAM,MAAM,CACnC,CAEO,MACL,MACA,MAAe,MAAM,IACM,CACvB,IAAAA,OAuBJ,OAtBI,OAAO,OAAU,SACnBA,OAAQ,KAAK,MAAM,KAAK,GAAK,KAAK,WAAW,KAAK,EAEzC,iBAAiB,MAAQ,MAAM,WAAa,KAAK,UAC1DA,OAAQ,KAAK,MAAM,KACV,OAAO,OAAU,SACtB,MAAQ,MAAM,MAAQ,MAAM,MAC9BA,OAAQ,KAAK,MAAM,MACV,MAAQ,MAAM,MAAQ,MAAM,SACrCA,OAAQ,KAAK,MAAM,QAEZ,iBAAiB,WACX,MAAM,aAAa,OAAO,GAAK,IAAI,MAAM,KAAK,EACvD,KAAM,OACFA,OAAA,KAAK,QAAQ,IAAI,EACrB,EAAAA,OAIL,EACDA,OAAQA,QAAS,KAAK,KAAK,MAAM,OAAO,GAEtCA,QAAS,KACJ,KAGP,UAAWA,QACX,MAAQ,MAAM,MAAQA,OAAM,OAC5B,MAAQ,MAAM,KAAOA,OAAM,MAEpBA,OAEF,IACT,CAEO,YAAY,YAAyD,CACnE,OAAA,YAAY,IAAK,YAAe,CACrC,MAAM,OAAS,aAAc,WACvB,OAAS,aAAc,WACzB,GAAA,CAAC,QAAU,CAAC,OACR,MAAA,IAAI,eAAe,oBAAoB,EACpC,GAAA,QAAU,WAAW,WAAa,WACrC,MAAA,IAAI,eAAe,gCAAgC,EAE3D,MAAM,IAAM,OACR,WAAW,SACX,OACE,WAAW,SACV,OACF,YAAA,MAAM,GAAG,EAAI,WAEd,OACE,OAAO,WAAW,SAAY,WAC3B,KAAA,WAAW,WAAW,OAAO,EAAI,YAE/B,SACL,WAAW,YACR,KAAA,QAAQ,WAAW,SAAS,EAAI,YAEnC,WAAW,UACT,MAAM,QAAQ,WAAW,OAAO,EAClC,WAAW,QAAU,WAAW,QAAQ,IAAK,SACpC,QAAQ,aAChB,EAEU,WAAA,QAAU,WAAW,QAAQ,YAAY,GAErC,MAAM,QAAQ,WAAW,OAAO,EAC7C,WAAW,QACX,CAAC,WAAW,OAAO,GACd,QAAS,KAAgB,EAC5B,KAAK,KAAK,GAAG,GAAK,MAAQ,WAAW,WAAa,QAC/C,KAAA,KAAK,GAAG,EAAI,WACnB,CACD,IAGE,UAAA,CACR,CACH,CACF,EAxIgB,UAAA,UAAY,QAD5B,IAAqB,SAArB,UCfA,SAAS,MAAM,KAAmB,OAA0B,CAE1D,OADkB,KAAK,aAAa,OAAO,GAAK,IAE7C,MAAM,KAAK,EACX,OAAQ,MAAS,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAM,CAAC,CACtD,CAEA,MAAM,wBAAwB,UAAW,CACvC,OAAc,KAAK,KAA6B,CACtC,OAAA,KAAK,aAAa,OAAO,GAAK,IACnC,MAAM,KAAK,EACX,IAAK,MAAS,KAAK,MAAM,GAAG,EAAE,MAAM,EAAG,EAAE,EAAE,KAAK,GAAG,CAAC,CACzD,CAEO,IAAI,KAAmB,MAAqB,CACjD,OAAK,KAAK,OAAO,KAAM,KAAK,GAG5B,KAAK,OAAO,IAAI,EAChB,KAAK,UAAU,IAAI,GAAG,KAAK,OAAO,IAAI,KAAK,EAAE,EACtC,IAJE,EAKX,CAEO,OAAO,KAAyB,CACrB,MAAM,KAAM,KAAK,OAAO,EAChC,QAAS,MAAS,CACnB,KAAA,UAAU,OAAO,IAAI,CAAA,CAC3B,EACG,KAAK,UAAU,SAAW,GAC5B,KAAK,gBAAgB,OAAO,CAEhC,CAEO,MAAM,KAAwB,CAEnC,MAAM,OADS,MAAM,KAAM,KAAK,OAAO,EAAE,CAAC,GAAK,IAC1B,MAAM,KAAK,QAAQ,OAAS,CAAC,EAClD,OAAO,KAAK,OAAO,KAAM,KAAK,EAAI,MAAQ,EAC5C,CACF,CAEA,MAAA,kBAAe,gBCxCf,SAAS,SAAS,KAAsB,CAChC,MAAA,MAAQ,KAAK,MAAM,GAAG,EACtB,KAAO,MACV,MAAM,CAAC,EACP,IAAK,MAAiB,KAAK,CAAC,EAAE,YAAA,EAAgB,KAAK,MAAM,CAAC,CAAC,EAC3D,KAAK,EAAE,EACH,OAAA,MAAM,CAAC,EAAI,IACpB,CAEA,MAAM,wBAAwB,UAAW,CACvC,OAAc,KAAK,KAA6B,CACtC,OAAA,KAAK,aAAa,OAAO,GAAK,IAAI,MAAM,GAAG,EAAE,IAAK,OAC5C,MAAM,MAAM,GAAG,EAChB,CAAC,EAAE,MACf,CACH,CAEO,IAAI,KAAmB,MAAqB,CACjD,OAAK,KAAK,OAAO,KAAM,KAAK,GAI5B,KAAK,MAAM,SAAS,KAAK,OAAO,CAAC,EAAI,MAC9B,IAJE,EAKX,CAEO,OAAO,KAAyB,CAErC,KAAK,MAAM,SAAS,KAAK,OAAO,CAAC,EAAI,GAChC,KAAK,aAAa,OAAO,GAC5B,KAAK,gBAAgB,OAAO,CAEhC,CAEO,MAAM,KAAwB,CAEnC,MAAM,MAAQ,KAAK,MAAM,SAAS,KAAK,OAAO,CAAC,EAC/C,OAAO,KAAK,OAAO,KAAM,KAAK,EAAI,MAAQ,EAC5C,CACF,CAEA,MAAA,kBAAe,gBCpCf,MAAM,eAAgB,CAIpB,YAAY,QAAsB,CAHlC,KAAQ,WAA4C,GAIlD,KAAK,QAAU,QACf,KAAK,MAAM,CACb,CAEO,UAAU,UAAuB,MAAkB,CAEpD,MACE,UAAU,IAAI,KAAK,QAAS,KAAK,IAC/B,UAAU,MAAM,KAAK,OAAO,GAAK,KAC9B,KAAA,WAAW,UAAU,QAAQ,EAAI,UAE/B,OAAA,KAAK,WAAW,UAAU,QAAQ,IAInC,UAAA,OAAO,KAAK,OAAO,EACtB,OAAA,KAAK,WAAW,UAAU,QAAQ,EAE7C,CAEO,OAAc,CACnB,KAAK,WAAa,GAClB,MAAM,KAAO,SAAS,KAAK,KAAK,OAAO,EACvC,GAAI,MAAQ,KACV,OAEF,MAAM,WAAa,WAAW,KAAK,KAAK,OAAO,EACzC,QAAUC,kBAAgB,KAAK,KAAK,OAAO,EAC3C,OAASC,kBAAgB,KAAK,KAAK,OAAO,EAE7C,WAAA,OAAO,OAAO,EACd,OAAO,MAAM,EACb,QAAS,MAAS,CACjB,MAAM,KAAO,KAAK,OAAO,MAAM,KAAM,MAAM,SAAS,EAChD,gBAAgB,aACb,KAAA,WAAW,KAAK,QAAQ,EAAI,KACnC,CACD,CACL,CAEO,KAAK,OAA2B,CACrC,OAAO,KAAK,KAAK,UAAU,EAAE,QAAS,KAAQ,CAC5C,MAAM,MAAQ,KAAK,WAAW,GAAG,EAAE,MAAM,KAAK,OAAO,EAC9C,OAAA,OAAO,IAAK,KAAK,CAAA,CACzB,CACH,CAEO,KAAK,OAA2B,CACrC,KAAK,KAAK,MAAM,EAChB,OAAO,KAAK,KAAK,UAAU,EAAE,QAAS,KAAQ,CAC5C,KAAK,WAAW,GAAG,EAAE,OAAO,KAAK,OAAO,CAAA,CACzC,EACD,KAAK,WAAa,EACpB,CAEO,QAAiC,CACtC,OAAO,OAAO,KAAK,KAAK,UAAU,EAAE,OAClC,CAAC,WAAoC,QACxB,WAAA,IAAI,EAAI,KAAK,WAAW,IAAI,EAAE,MAAM,KAAK,OAAO,EACpD,YAET,CAAC,CAAA,CAEL,CACF,CAEA,MAAA,kBAAe,gBCnET,YAAN,MAAM,WAA2B,CA+C/B,YACS,OACA,QACP,CAFO,KAAA,OAAA,OACA,KAAA,QAAA,QAEE,SAAA,MAAM,IAAI,QAAS,IAAI,EAChC,KAAK,KAAO,KACZ,KAAK,KAAO,IACd,CA/CA,OAAc,OAAO,SAA0B,CACzC,GAAA,KAAK,SAAW,KACZ,MAAA,IAAI,eAAe,iCAAiC,EAExD,IAAA,KACA,MACJ,OAAI,MAAM,QAAQ,KAAK,OAAO,GACxB,OAAO,UAAa,UACtB,MAAQ,SAAS,cACb,SAAS,MAAO,EAAE,EAAE,SAAA,IAAe,QAC7B,MAAA,SAAS,MAAO,EAAE,IAEnB,OAAO,UAAa,WACrB,MAAA,UAEN,OAAO,OAAU,SACnB,KAAO,SAAS,cAAc,KAAK,QAAQ,MAAQ,CAAC,CAAC,EAC5C,OAAS,KAAK,QAAQ,QAAQ,KAAK,EAAI,GACzC,KAAA,SAAS,cAAc,KAAK,EAEnC,KAAO,SAAS,cAAc,KAAK,QAAQ,CAAC,CAAC,GAGxC,KAAA,SAAS,cAAc,KAAK,OAAO,EAExC,KAAK,WACF,KAAA,UAAU,IAAI,KAAK,SAAS,EAE5B,IACT,CAQA,IAAI,SAAe,CACjB,OAAO,KAAK,WACd,CAUO,QAAe,CAEtB,CAEO,OAAc,CACnB,MAAM,QAAU,KAAK,QAAQ,UAAU,EAAK,EACrC,OAAA,KAAK,OAAO,OAAO,OAAO,CACnC,CAEO,QAAe,CAChB,KAAK,QAAU,MACZ,KAAA,OAAO,YAAY,IAAI,EAErB,SAAA,MAAM,OAAO,KAAK,OAAO,CACpC,CAEO,SAAS,MAAe,OAAsB,CACtC,KAAK,QAAQ,MAAO,MAAM,EAClC,OAAO,CACd,CAEO,SACL,MACA,OACA,KACA,MACM,CACN,MAAM,KAAO,KAAK,QAAQ,MAAO,MAAM,EACnC,GAAA,KAAK,OAAO,MAAM,KAAM,MAAM,IAAI,GAAK,MAAQ,MAC5C,KAAA,KAAK,KAAM,KAAK,UACZ,KAAK,OAAO,MAAM,KAAM,MAAM,SAAS,GAAK,KAAM,CAC3D,MAAM,OAAS,KAAK,OAAO,OAAO,KAAK,QAAQ,KAAK,EAEpD,KAAK,KAAK,MAAM,EACT,OAAA,OAAO,KAAM,KAAK,CAC3B,CACF,CAEO,SAAS,MAAe,MAAe,IAAiB,CAC7D,MAAM,KACJ,KAAO,KACH,KAAK,OAAO,OAAO,OAAQ,KAAK,EAChC,KAAK,OAAO,OAAO,MAAO,GAAG,EAC7B,IAAM,KAAK,MAAM,KAAK,EAC5B,KAAK,OAAO,aAAa,KAAM,KAAO,MAAS,CACjD,CAEO,QAAQ,MAAe,OAAsB,CAC5C,MAAA,OAAS,KAAK,MAAM,KAAK,EAC/B,GAAI,QAAU,KACN,MAAA,IAAI,MAAM,2BAA2B,EAE7C,cAAO,MAAM,MAAM,EACZ,MACT,CAEO,QAAiB,CACf,MAAA,EACT,CAEO,OAAO,KAAa,KAAK,OAAgB,CAC9C,OAAI,KAAK,QAAU,MAAQ,OAAS,KAC3B,EAEF,KAAK,OAAO,SAAS,OAAO,IAAI,EAAI,KAAK,OAAO,OAAO,IAAI,CACpE,CAEO,SAAS,SAAyC,CAErD,KAAK,QAAQ,mBACb,EAAE,KAAK,kBAAkB,KAAK,QAAQ,oBAEtC,KAAK,KAAK,KAAK,QAAQ,kBAAkB,QAAQ,CAErD,CAEO,QAAe,CAChB,KAAK,QAAQ,YAAc,MAC7B,KAAK,QAAQ,WAAW,YAAY,KAAK,OAAO,EAElD,KAAK,OAAO,CACd,CAEO,YAAY,KAAqB,MAAmB,CACnD,MAAA,YACJ,OAAO,MAAS,SAAW,KAAK,OAAO,OAAO,KAAM,KAAK,EAAI,KAC3D,OAAA,KAAK,QAAU,OACjB,KAAK,OAAO,aAAa,YAAa,KAAK,MAAQ,MAAS,EAC5D,KAAK,OAAO,GAEP,WACT,CAEO,MAAM,MAAe,OAA+B,CAClD,OAAA,QAAU,EAAI,KAAO,KAAK,IACnC,CAEO,OACL,WACA,SACM,CAER,CAEO,KAAK,KAAuB,MAAqB,CAChD,MAAA,QACJ,OAAO,MAAS,SACX,KAAK,OAAO,OAAO,KAAM,KAAK,EAC/B,KAIF,GAHA,KAAK,QAAU,MACjB,KAAK,OAAO,aAAa,QAAS,KAAK,MAAQ,MAAS,EAEtD,OAAO,QAAQ,aAAgB,WACjC,MAAM,IAAI,eAAe,eAAe,IAAI,EAAE,EAEhD,eAAQ,YAAY,IAAI,EACjB,OACT,CACF,EA7KE,YAAc,SAAW,WAD3B,IAAM,WAAN,YCPA,MAAM,UAAN,MAAM,kBAAiB,UAA2B,CAQhD,OAAc,MAAM,SAAqB,CAChC,MAAA,EACT,CAMO,MAAM,KAAY,OAAwB,CAE7C,OAAA,KAAK,UAAY,MACjB,KAAK,QAAQ,wBAAwB,IAAI,EACvC,KAAK,+BAEA,KAAK,IAAI,OAAQ,CAAC,EAEpB,EACT,CAMO,SAAS,MAAe,WAAsC,CAEnE,IAAI,OADuB,MAAM,KAAK,KAAK,OAAO,QAAQ,UAAU,EAC5C,QAAQ,KAAK,OAAO,EAC5C,OAAI,MAAQ,IACA,QAAA,GAEL,CAAC,KAAK,OAAO,QAAS,MAAM,CACrC,CAOO,OAAa,CACX,MAAA,CACL,CAAC,KAAK,QAAQ,QAAQ,EAAG,KAAK,QAAQ,MAAM,KAAK,OAAO,GAAK,EAAA,CAEjE,CACF,EAjDE,UAAc,MAAQ,MAAM,YAD9B,IAAM,SAAN,UAoDA,MAAA,WAAe,SCtDf,MAAM,UAAiC,CAKrC,aAAc,CACZ,KAAK,KAAO,KACZ,KAAK,KAAO,KACZ,KAAK,OAAS,CAChB,CAEO,UAAU,MAAkB,CAE7B,GADJ,KAAK,aAAa,MAAM,CAAC,EAAG,IAAI,EAC5B,MAAM,OAAS,EAAG,CACd,MAAA,KAAO,MAAM,MAAM,CAAC,EACrB,KAAA,OAAO,GAAG,IAAI,CACrB,CACF,CAEO,GAAG,MAAyB,CAC3B,MAAA,KAAO,KAAK,WAClB,IAAI,IAAM,OACH,KAAA,KAAO,MAAQ,GACX,OAAA,EACT,IAAM,KAAK,EAEN,OAAA,GACT,CAEO,SAAS,KAAkB,CAC1B,MAAA,KAAO,KAAK,WAClB,IAAI,IAAM,OACV,KAAO,KAAK,CACV,GAAI,MAAQ,KACH,MAAA,GAET,IAAM,KAAK,CACb,CACO,MAAA,EACT,CAEO,QAAQ,KAAiB,CACxB,MAAA,KAAO,KAAK,WAClB,IAAI,IAAM,OACN,MAAQ,EACZ,KAAO,KAAK,CACV,GAAI,MAAQ,KACH,OAAA,MAEA,OAAA,EACT,IAAM,KAAK,CACb,CACO,MAAA,EACT,CAEO,aAAa,KAAgB,QAAyB,CACvD,MAAQ,OAGZ,KAAK,OAAO,IAAI,EAChB,KAAK,KAAO,QACR,SAAW,MACb,KAAK,KAAO,QAAQ,KAChB,QAAQ,MAAQ,OAClB,QAAQ,KAAK,KAAO,MAEtB,QAAQ,KAAO,KACX,UAAY,KAAK,OACnB,KAAK,KAAO,OAEL,KAAK,MAAQ,MACtB,KAAK,KAAK,KAAO,KACjB,KAAK,KAAO,KAAK,KACjB,KAAK,KAAO,OAEZ,KAAK,KAAO,KACP,KAAA,KAAO,KAAK,KAAO,MAE1B,KAAK,QAAU,EACjB,CAEO,OAAO,OAAmB,CAC/B,IAAI,MAAQ,EACR,IAAM,KAAK,KACf,KAAO,KAAO,MAAM,CAClB,GAAI,MAAQ,OACH,OAAA,MAET,OAAS,IAAI,SACb,IAAM,IAAI,IACZ,CACO,MAAA,EACT,CAEO,OAAO,KAAe,CACtB,KAAK,SAAS,IAAI,IAGnB,KAAK,MAAQ,OACV,KAAA,KAAK,KAAO,KAAK,MAEpB,KAAK,MAAQ,OACV,KAAA,KAAK,KAAO,KAAK,MAEpB,OAAS,KAAK,OAChB,KAAK,KAAO,KAAK,MAEf,OAAS,KAAK,OAChB,KAAK,KAAO,KAAK,MAEnB,KAAK,QAAU,EACjB,CAEO,SAAS,QAAoB,KAAK,KAAsB,CAE7D,MAAO,IAAgB,CACrB,MAAM,IAAM,QACZ,OAAI,SAAW,OACb,QAAU,QAAQ,MAEb,GAAA,CAEX,CAEO,KAAK,MAAe,UAAY,GAA2B,CAC1D,MAAA,KAAO,KAAK,WAClB,IAAI,IAAM,OACV,KAAO,KAAK,CACJ,MAAA,OAAS,IAAI,SACnB,GACE,MAAQ,QACP,WACC,QAAU,SACT,IAAI,MAAQ,MAAQ,IAAI,KAAK,OAAO,IAAM,GAEtC,MAAA,CAAC,IAAK,KAAK,EAEX,OAAA,OACT,IAAM,KAAK,CACb,CACO,MAAA,CAAC,KAAM,CAAC,CACjB,CAEO,QAAQ,SAAkC,CACzC,MAAA,KAAO,KAAK,WAClB,IAAI,IAAM,OACV,KAAO,KACL,SAAS,GAAG,EACZ,IAAM,KAAK,CAEf,CAEO,UACL,MACA,OACA,SACM,CACN,GAAI,QAAU,EACZ,OAEF,KAAM,CAAC,UAAW,MAAM,EAAI,KAAK,KAAK,KAAK,EAC3C,IAAI,SAAW,MAAQ,OACjB,MAAA,KAAO,KAAK,SAAS,SAAS,EACpC,IAAI,IAAM,OACH,KAAA,KAAO,SAAW,MAAQ,QAAQ,CACjC,MAAA,UAAY,IAAI,SAClB,MAAQ,SACV,SACE,IACA,MAAQ,SACR,KAAK,IAAI,OAAQ,SAAW,UAAY,KAAK,CAAA,EAGtC,SAAA,IAAK,EAAG,KAAK,IAAI,UAAW,MAAQ,OAAS,QAAQ,CAAC,EAErD,UAAA,UACZ,IAAM,KAAK,CACb,CACF,CAEO,IAAI,SAAkC,CAC3C,OAAO,KAAK,OAAO,CAAC,KAAW,OACxB,KAAA,KAAK,SAAS,GAAG,CAAC,EAChB,MACN,CAAE,CAAA,CACP,CAEO,OAAU,SAAkC,KAAY,CACvD,MAAA,KAAO,KAAK,WAClB,IAAI,IAAM,OACV,KAAO,KACE,KAAA,SAAS,KAAM,GAAG,EACzB,IAAM,KAAK,EAEN,OAAA,IACT,CACF,CChMA,SAAS,iBAAiB,KAAY,OAAoB,CAClD,MAAA,MAAQ,OAAO,KAAK,IAAI,EAC1B,GAAA,MAAc,OAAA,MACd,GAAA,CACK,OAAA,OAAO,OAAO,IAAI,OACf,CACV,MAAM,KAAO,OAAO,OAAO,MAAM,MAAM,EACvC,aAAM,KAAK,KAAK,UAAU,EAAE,QAAS,OAAgB,CAC9C,KAAA,QAAQ,YAAY,KAAK,CAAA,CAC/B,EACG,KAAK,YACP,KAAK,WAAW,aAAa,KAAK,QAAS,IAAI,EAEjD,KAAK,OAAO,EACL,IACT,CACF,CAEA,MAAM,YAAN,MAAM,oBAAmB,UAA6B,CAgBpD,YAAY,OAAc,QAAe,CACvC,MAAM,OAAQ,OAAO,EAHvB,KAAO,OAA6B,KAIlC,KAAK,MAAM,CACb,CAEO,YAAY,MAAmB,CACpC,KAAK,aAAa,KAAK,CACzB,CAEO,QAAe,CACpB,MAAM,OAAO,EACR,KAAA,SAAS,QAAS,OAAU,CAC/B,MAAM,OAAO,CAAA,CACd,CACH,CAEO,SAAS,KAAyB,CACnC,KAAK,QAAU,MACjB,KAAK,OAAO,SAEd,KAAK,OAAS,KACV,YAAW,SACb,KAAK,OAAO,UAAU,IAAI,YAAW,OAAO,EAEzC,KAAA,OAAO,aAAa,kBAAmB,OAAO,EACnD,KAAK,QAAQ,aAAa,KAAK,OAAQ,KAAK,QAAQ,UAAU,CAChE,CAKO,OAAc,CACd,KAAA,SAAW,IAAI,WAEpB,MAAM,KAAK,KAAK,QAAQ,UAAU,EAC/B,OAAQ,MAAe,OAAS,KAAK,MAAM,EAC3C,QAAA,EACA,QAAS,MAAe,CACnB,GAAA,CACF,MAAM,MAAQ,iBAAiB,KAAM,KAAK,MAAM,EAChD,KAAK,aAAa,MAAO,KAAK,SAAS,MAAQ,MAAS,QACjD,IAAK,CACZ,GAAI,eAAe,eACjB,OAEM,MAAA,GAEV,CAAA,CACD,CACL,CAEO,SAAS,MAAe,OAAsB,CACnD,GAAI,QAAU,GAAK,SAAW,KAAK,SACjC,OAAO,KAAK,SAEd,KAAK,SAAS,UAAU,MAAO,OAAQ,CAAC,MAAO,OAAQ,cAAgB,CAC/D,MAAA,SAAS,OAAQ,WAAW,CAAA,CACnC,CACH,CAUO,WAAW,SAAe,MAAQ,EAA0B,CACjE,KAAM,CAAC,MAAO,MAAM,EAAI,KAAK,SAAS,KAAK,KAAK,EAE7C,OAAA,SAAS,UAAY,MAAQ,SAAS,KAAK,GAC3C,SAAS,UAAY,MAAQ,iBAAiB,SAExC,CAAC,MAAc,MAAM,EACnB,iBAAiB,YACnB,MAAM,WAAW,SAAU,MAAM,EAEjC,CAAC,KAAM,EAAE,CAEpB,CAYO,YACL,SACA,MAAQ,EACR,OAAiB,OAAO,UAChB,CACR,IAAI,YAAsB,CAAA,EACtB,WAAa,OACjB,YAAK,SAAS,UACZ,MACA,OACA,CAAC,MAAa,WAAoB,cAAwB,EAErD,SAAS,UAAY,MAAQ,SAAS,KAAK,GAC3C,SAAS,UAAY,MAAQ,iBAAiB,WAE/C,YAAY,KAAK,KAAK,EAEpB,iBAAiB,cACnB,YAAc,YAAY,OACxB,MAAM,YAAY,SAAU,WAAY,UAAU,CAAA,GAGxC,YAAA,WAChB,CAAA,EAEK,WACT,CAEO,QAAe,CACf,KAAA,SAAS,QAAS,OAAU,CAC/B,MAAM,OAAO,CAAA,CACd,EACD,MAAM,OAAO,CACf,CAEO,wBAA+B,CACpC,IAAI,KAAO,GACN,KAAA,SAAS,QAAS,OAAgB,CACjC,MAGY,KAAK,QAAQ,gBAAgB,KAC1C,KAAyB,iBAAiB,GAAA,IAKzC,MAAM,QAAQ,QAAU,MAAM,YAC5B,MAAM,MAAQ,MAChB,KAAK,WAAW,KAAK,EAEnB,MAAM,MAAQ,MACX,KAAA,WAAW,MAAM,IAAI,EAE5B,MAAM,OAAO,SACN,KAAA,IACE,iBAAiB,YAC1B,MAAM,OAAO,EAEb,MAAM,OAAO,EACf,CACD,CACH,CAEO,SACL,MACA,OACA,KACA,MACM,CACN,KAAK,SAAS,UAAU,MAAO,OAAQ,CAAC,MAAO,OAAQ,cAAgB,CACrE,MAAM,SAAS,OAAQ,YAAa,KAAM,KAAK,CAAA,CAChD,CACH,CAEO,SAAS,MAAe,MAAe,IAAiB,CAC7D,KAAM,CAAC,MAAO,MAAM,EAAI,KAAK,SAAS,KAAK,KAAK,EAChD,GAAI,MACI,MAAA,SAAS,OAAQ,MAAO,GAAG,MAC5B,CACL,MAAM,KACJ,KAAO,KACH,KAAK,OAAO,OAAO,OAAQ,KAAK,EAChC,KAAK,OAAO,OAAO,MAAO,GAAG,EACnC,KAAK,YAAY,IAAI,CACvB,CACF,CAEO,aAAa,UAAiB,QAA6B,CAC5D,UAAU,QAAU,MACZ,UAAA,OAAO,SAAS,OAAO,SAAS,EAE5C,IAAI,WAA0B,KAC9B,KAAK,SAAS,aAAa,UAAW,SAAW,IAAI,EACrD,UAAU,OAAS,KACf,SAAW,OACb,WAAa,QAAQ,UAGrB,KAAK,QAAQ,aAAe,UAAU,SACtC,KAAK,QAAQ,cAAgB,aAE7B,KAAK,QAAQ,aAAa,UAAU,QAAS,UAAU,EAEzD,UAAU,OAAO,CACnB,CAEO,QAAiB,CACtB,OAAO,KAAK,SAAS,OAAO,CAAC,KAAM,QAC1B,KAAO,MAAM,SACnB,CAAC,CACN,CAEO,aAAa,aAAsB,QAA6B,CAChE,KAAA,SAAS,QAAS,OAAU,CAClB,aAAA,aAAa,MAAO,OAAO,CAAA,CACzC,CACH,CAEO,SAAS,QAAwC,CAMlD,GALJ,MAAM,SAAS,OAAO,EACtB,KAAK,uBAAuB,EACxB,KAAK,QAAU,MAAQ,KAAK,SAAW,KAAK,QAAQ,YACtD,KAAK,QAAQ,aAAa,KAAK,OAAQ,KAAK,QAAQ,UAAU,EAE5D,KAAK,SAAS,SAAW,EACvB,GAAA,KAAK,QAAQ,cAAgB,KAAM,CACrC,MAAM,MAAQ,KAAK,OAAO,OAAO,KAAK,QAAQ,aAAa,QAAQ,EACnE,KAAK,YAAY,KAAK,CAAA,MAItB,KAAK,OAAO,CAGlB,CAEO,KAAK,MAAe,UAAY,GAAyB,CACxD,KAAA,CAAC,MAAO,MAAM,EAAI,KAAK,SAAS,KAAK,MAAO,SAAS,EACrD,SAA6B,CAAC,CAAC,KAAM,KAAK,CAAC,EACjD,OAAI,iBAAiB,YACZ,SAAS,OAAO,MAAM,KAAK,OAAQ,SAAS,CAAC,GAC3C,OAAS,MAClB,SAAS,KAAK,CAAC,MAAO,MAAM,CAAC,EAExB,SACT,CAEO,YAAY,MAAmB,CAC/B,KAAA,SAAS,OAAO,KAAK,CAC5B,CAEO,YAAY,KAAqB,MAAmB,CACnD,MAAA,YACJ,OAAO,MAAS,SAAW,KAAK,OAAO,OAAO,KAAM,KAAK,EAAI,KAC/D,OAAI,uBAAuB,aACzB,KAAK,aAAa,WAAW,EAExB,MAAM,YAAY,WAAW,CACtC,CAEO,MAAM,MAAe,MAAQ,GAAoB,CACtD,GAAI,CAAC,MAAO,CACV,GAAI,QAAU,EACL,OAAA,KAEL,GAAA,QAAU,KAAK,SACjB,OAAO,KAAK,IAEhB,CACM,MAAA,MAAQ,KAAK,QACnB,OAAI,KAAK,QACP,KAAK,OAAO,aAAa,MAAO,KAAK,MAAQ,MAAS,EAEnD,KAAA,SAAS,UAAU,MAAO,KAAK,SAAU,CAAC,MAAO,OAAQ,UAAY,CACxE,MAAM,MAAQ,MAAM,MAAM,OAAQ,KAAK,EACnC,OAAS,MACX,MAAM,YAAY,KAAK,CACzB,CACD,EACM,KACT,CAEO,WAAW,MAAqB,CAC/B,MAAA,MAAQ,KAAK,QACZ,KAAA,MAAM,MAAQ,MACb,MAAA,YAAY,MAAM,IAAI,EAE9B,OAAI,KAAK,QACP,KAAK,OAAO,aAAa,MAAO,KAAK,MAAQ,MAAS,EAEjD,KACT,CAEO,QAAe,CAChB,KAAK,QACP,KAAK,aAAa,KAAK,OAAQ,KAAK,MAAQ,MAAS,EAEvD,KAAK,OAAO,CACd,CAEO,OACL,UACA,SACM,CACN,MAAM,WAAqB,CAAA,EACrB,aAAuB,CAAA,EACnB,UAAA,QAAS,UAAa,CAC1B,SAAS,SAAW,KAAK,SAAW,SAAS,OAAS,cAC7C,WAAA,KAAK,GAAG,SAAS,UAAU,EACzB,aAAA,KAAK,GAAG,SAAS,YAAY,EAC5C,CACD,EACY,aAAA,QAAS,MAAe,CAInC,GACE,KAAK,YAAc,MAEnB,KAAK,UAAY,UACjB,SAAS,KAAK,wBAAwB,IAAI,EACxC,KAAK,+BAEP,OAEF,MAAM,KAAO,KAAK,OAAO,KAAK,IAAI,EAC9B,MAAQ,OAIV,KAAK,QAAQ,YAAc,MAC3B,KAAK,QAAQ,aAAe,KAAK,UAEjC,KAAK,OAAO,CACd,CACD,EAEE,WAAA,OAAQ,MACA,KAAK,aAAe,KAAK,SAAW,OAAS,KAAK,MAC1D,EACA,KAAK,CAAC,EAAG,IACJ,IAAM,EACD,EAEL,EAAE,wBAAwB,CAAC,EAAI,KAAK,4BAC/B,EAEF,EACR,EACA,QAAS,MAAS,CACjB,IAAI,QAAuB,KACvB,KAAK,aAAe,OACtB,QAAU,KAAK,OAAO,KAAK,KAAK,WAAW,GAE7C,MAAM,KAAO,iBAAiB,KAAM,KAAK,MAAM,GAC3C,KAAK,OAAS,SAAW,KAAK,MAAQ,QACpC,KAAK,QAAU,MACZ,KAAA,OAAO,YAAY,IAAI,EAEzB,KAAA,aAAa,KAAM,SAAW,MAAS,EAC9C,CACD,EACH,KAAK,uBAAuB,CAC9B,CACF,EA3WE,YAAc,QAAU,GAV1B,IAAM,WAAN,YAuXA,MAAA,aAAe,WCjYf,SAAS,QACP,KACA,KACS,CACL,GAAA,OAAO,KAAK,IAAI,EAAE,SAAW,OAAO,KAAK,IAAI,EAAE,OAC1C,MAAA,GAET,UAAW,QAAQ,KACjB,GAAI,KAAK,IAAI,IAAM,KAAK,IAAI,EACnB,MAAA,GAGJ,MAAA,EACT,CAEA,MAAM,YAAN,MAAM,oBAAmBC,YAAkC,CAMzD,OAAO,OAAO,MAAiB,CACtB,OAAA,MAAM,OAAO,KAAK,CAC3B,CAEA,OAAc,QAAQ,QAAsB,OAAmB,CAC7D,MAAMH,OAAQ,OAAO,MAAM,YAAW,QAAQ,EAC9C,GACE,EAAAA,QAAS,MACT,QAAQ,UAAaA,OAA0B,SAGtC,IAAA,OAAO,KAAK,SAAY,SAC1B,MAAA,GACE,GAAA,MAAM,QAAQ,KAAK,OAAO,EAC5B,OAAA,QAAQ,QAAQ,cAG3B,CAIA,YAAY,OAAc,QAAe,CACvC,MAAM,OAAQ,OAAO,EACrB,KAAK,WAAa,IAAII,kBAAgB,KAAK,OAAO,CACpD,CAEO,OAAO,KAAc,MAAkB,CAC5C,GAAI,OAAS,KAAK,QAAQ,UAAY,CAAC,MAChC,KAAA,SAAS,QAAS,OAAU,CACzB,iBAAiB,cACrB,MAAQ,MAAM,KAAK,YAAW,SAAU,EAAI,GAEzC,KAAA,WAAW,KAAK,KAAmB,CAAA,CACzC,EACD,KAAK,OAAO,MACP,CACL,MAAM,OAAS,KAAK,OAAO,MAAM,KAAM,MAAM,MAAM,EACnD,GAAI,QAAU,KACZ,OAEE,kBAAkB,WACf,KAAA,WAAW,UAAU,OAAQ,KAAK,EAEvC,QACC,OAAS,KAAK,QAAQ,UAAY,KAAK,QAAQ,EAAE,IAAI,IAAM,QAEvD,KAAA,YAAY,KAAM,KAAK,CAEhC,CACF,CAEO,SAAoC,CACnC,MAAA,QAAU,KAAK,WAAW,OAAO,EACjC,OAAS,KAAK,QAAQ,QAAQ,KAAK,QAAS,KAAK,MAAM,EAC7D,OAAI,QAAU,OACJ,QAAA,KAAK,QAAQ,QAAQ,EAAI,QAE5B,OACT,CAEO,SACL,MACA,OACA,KACA,MACM,CAEJ,KAAK,UAAU,IAAI,GAAK,MACxB,KAAK,OAAO,MAAM,KAAM,MAAM,SAAS,EAE1B,KAAK,QAAQ,MAAO,MAAM,EAClC,OAAO,KAAM,KAAK,EAEvB,MAAM,SAAS,MAAO,OAAQ,KAAM,KAAK,CAE7C,CAEO,SAAS,QAAuC,CACrD,MAAM,SAAS,OAAO,EAChB,MAAA,QAAU,KAAK,UACrB,GAAI,OAAO,KAAK,OAAO,EAAE,SAAW,EAClC,OAAO,KAAK,SAEd,MAAM,KAAO,KAAK,KAEhB,gBAAgB,aAChB,KAAK,OAAS,MACd,QAAQ,QAAS,KAAK,QAAQ,CAAC,IAE/B,KAAK,aAAa,IAAI,EACtB,KAAK,OAAO,EAEhB,CAEO,YAAY,KAAqB,MAAmB,CACzD,MAAM,YAAc,MAAM,YAAY,KAAM,KAAK,EAC5C,YAAA,WAAW,KAAK,WAAW,EACzB,WACT,CAEO,OACL,UACA,QACM,CACA,MAAA,OAAO,UAAW,OAAO,EACN,UAAU,KAChC,UACC,SAAS,SAAW,KAAK,SAAW,SAAS,OAAS,YAAA,GAGxD,KAAK,WAAW,OAEpB,CAEO,KAAK,KAAuB,MAAqB,CACtD,MAAM,QAAU,MAAM,KAAK,KAAM,KAAK,EACtC,OAAI,mBAAmB,aAChB,KAAA,WAAW,KAAK,OAAO,EAEvB,OACT,CACF,EA9HgB,YAAA,gBAAqC,CAAC,YAAYC,UAAQ,EACxE,YAAc,SAAW,SACzB,YAAc,MAAQ,MAAM,YAC5B,YAAc,QAA6B,OAJ7C,IAAM,WAAN,YAiIA,MAAA,aAAe,WCjJT,WAAN,MAAM,mBAAkBF,YAAkC,CAUxD,OAAO,OAAO,MAAiB,CACtB,OAAA,MAAM,OAAO,KAAK,CAC3B,CAEA,OAAc,QAAQ,QAAsB,OAAmB,CAC7D,MAAMH,OAAQ,OAAO,MAAM,WAAU,QAAQ,EAC7C,GACE,EAAAA,QAAS,MACT,QAAQ,UAAaA,OAA0B,SAGtC,IAAA,OAAO,KAAK,SAAY,SAC1B,MAAA,GACE,GAAA,MAAM,QAAQ,KAAK,OAAO,EAC5B,OAAA,QAAQ,QAAQ,cAE3B,CAIA,YAAY,OAAc,QAAe,CACvC,MAAM,OAAQ,OAAO,EACrB,KAAK,WAAa,IAAII,kBAAgB,KAAK,OAAO,CACpD,CAEO,OAAO,KAAc,MAAkB,CAC5C,MAAM,OAAS,KAAK,OAAO,MAAM,KAAM,MAAM,KAAK,EAC9C,QAAU,OAEH,kBAAkB,WACtB,KAAA,WAAW,UAAU,OAAQ,KAAK,EAC9B,OAAS,KAAK,QAAQ,UAAY,CAAC,MACvC,KAAA,YAAY,WAAU,QAAQ,EAEnC,QACC,OAAS,KAAK,QAAQ,UAAY,KAAK,QAAQ,EAAE,IAAI,IAAM,QAEvD,KAAA,YAAY,KAAM,KAAK,EAEhC,CAEO,SAAoC,CACnC,MAAA,QAAU,KAAK,WAAW,OAAO,EACjC,OAAS,KAAK,QAAQ,QAAQ,KAAK,QAAS,KAAK,MAAM,EAC7D,OAAI,QAAU,OACJ,QAAA,KAAK,QAAQ,QAAQ,EAAI,QAE5B,OACT,CAEO,SACL,MACA,OACA,KACA,MACM,CACF,KAAK,OAAO,MAAM,KAAM,MAAM,KAAK,GAAK,KACrC,KAAA,OAAO,KAAM,KAAK,EAEvB,MAAM,SAAS,MAAO,OAAQ,KAAM,KAAK,CAE7C,CAEO,SAAS,MAAe,MAAe,IAAiB,CACzD,GAAA,KAAO,MAAQ,KAAK,OAAO,MAAM,MAAO,MAAM,MAAM,GAAK,KAErD,MAAA,SAAS,MAAO,MAAO,GAAG,MAC3B,CACC,MAAA,MAAQ,KAAK,MAAM,KAAK,EAC9B,GAAI,OAAS,KAAM,CACjB,MAAM,KAAO,KAAK,OAAO,OAAO,MAAO,GAAG,EACpC,MAAA,OAAO,aAAa,KAAM,KAAK,CAAA,KAE/B,OAAA,IAAI,MAAM,4CAA4C,CAEhE,CACF,CAEO,YAAY,KAAqB,MAAmB,CACzD,MAAM,YAAc,MAAM,YAAY,KAAM,KAAK,EAC5C,YAAA,WAAW,KAAK,WAAW,EACzB,WACT,CAEO,OACL,UACA,QACM,CACA,MAAA,OAAO,UAAW,OAAO,EACN,UAAU,KAChC,UACC,SAAS,SAAW,KAAK,SAAW,SAAS,OAAS,YAAA,GAGxD,KAAK,WAAW,OAEpB,CACF,EA1GE,WAAc,SAAW,QACzB,WAAc,MAAQ,MAAM,WAC5B,WAAc,QAA6B,IAC3C,WAAc,gBAAqC,CACjDE,aACA,WACAD,UAAA,EAPJ,IAAM,UAAN,WA6GA,MAAA,YAAe,UCtHT,eAAN,MAAM,uBAAsBF,YAAW,CAQ9B,YAAsB,CAEzB,OAAA,KAAK,OAAS,MAAQ,KAAK,KAAK,QAAQ,WAAa,KAAK,QAAQ,QAEtE,CAEO,SAAS,MAAe,OAAsB,CAC7C,MAAA,SAAS,MAAO,MAAM,EAC5B,KAAK,uBAAuB,CAC9B,CAEO,SACL,MACA,OACA,KACA,MACM,CACN,MAAM,SAAS,MAAO,OAAQ,KAAM,KAAK,EACzC,KAAK,uBAAuB,CAC9B,CAEO,SAAS,MAAe,MAAe,IAAiB,CACvD,MAAA,SAAS,MAAO,MAAO,GAAG,EAChC,KAAK,uBAAuB,CAC9B,CAEO,SAAS,QAAuC,CACrD,MAAM,SAAS,OAAO,EAClB,KAAK,SAAS,OAAS,GAAK,KAAK,MAAQ,MAAQ,KAAK,eACnD,KAAA,KAAK,aAAa,IAAI,EAC3B,KAAK,KAAK,SAEd,CACF,EAxCE,eAAc,SAAW,YACzB,eAAc,MAAQ,MAAM,WAF9B,IAAM,cAAN,eA2CA,MAAA,gBAAe,cC5Cf,MAAM,kBAAkBE,UAAgC,CACtD,OAAc,QAAQ,SAAuB,QAAoB,CAEjE,CAEO,OAAO,KAAc,MAAkB,CAI5C,MAAM,SAAS,EAAG,KAAK,SAAU,KAAM,KAAK,CAC9C,CAEO,SACL,MACA,OACA,KACA,MACM,CACF,QAAU,GAAK,SAAW,KAAK,SAC5B,KAAA,OAAO,KAAM,KAAK,EAEvB,MAAM,SAAS,MAAO,OAAQ,KAAM,KAAK,CAE7C,CAEO,SAAoC,CACzC,OAAO,KAAK,QAAQ,QAAQ,KAAK,QAAS,KAAK,MAAM,CACvD,CACF,CAEA,MAAA,YAAe,UC1BT,gBAAkB,CACtB,WAAY,GACZ,cAAe,GACf,sBAAuB,GACvB,UAAW,GACX,QAAS,EACX,EAEM,wBAA0B,IAE1B,YAAN,MAAM,oBAAmBF,YAA2B,CASlD,YACS,SACP,KACA,CAEA,MAAM,KAAM,IAAI,EAJT,KAAA,SAAA,SAKP,KAAK,OAAS,KACd,KAAK,MAAM,EACX,KAAK,SAAW,IAAI,iBAAkB,WAAgC,CACpE,KAAK,OAAO,SAAS,CAAA,CACtB,EACD,KAAK,SAAS,QAAQ,KAAK,QAAS,eAAe,EACnD,KAAK,OAAO,CACd,CAEO,OAAO,MAA8B,MAAmB,CAC7D,OAAO,KAAK,SAAS,OAAO,KAAM,MAAO,KAAK,CAChD,CAEO,KAAK,KAAmB,OAAS,GAAoB,CAC1D,MAAM,KAAO,KAAK,SAAS,KAAK,KAAM,MAAM,EAC5C,OAAK,KAGD,KAAK,SAAW,KACX,KAEF,OAAS,KAAK,KAAK,KAAK,OAAO,QAAQ,WAAY,EAAI,EAAI,KALzD,IAMX,CAEO,MACL,MACA,MAAe,MAAM,IACM,CAC3B,OAAO,KAAK,SAAS,MAAM,MAAO,KAAK,CACzC,CAEO,YAAY,YAAmC,CACpD,OAAO,KAAK,SAAS,SAAS,GAAG,WAAW,CAC9C,CAEO,OAAc,CACf,KAAK,QAAU,MAGnB,MAAM,MAAM,CACd,CAEO,QAAe,CACpB,MAAM,OAAO,EACb,KAAK,SAAS,YAChB,CAEO,SAAS,MAAe,OAAsB,CACnD,KAAK,OAAO,EACR,QAAU,GAAK,SAAW,KAAK,SAC5B,KAAA,SAAS,QAAS,OAAU,CAC/B,MAAM,OAAO,CAAA,CACd,EAEK,MAAA,SAAS,MAAO,MAAM,CAEhC,CAEO,SACL,MACA,OACA,KACA,MACM,CACN,KAAK,OAAO,EACZ,MAAM,SAAS,MAAO,OAAQ,KAAM,KAAK,CAC3C,CAEO,SAAS,MAAe,MAAe,IAAiB,CAC7D,KAAK,OAAO,EACN,MAAA,SAAS,MAAO,MAAO,GAAG,CAClC,CAOO,SAAS,UAAiB,GAAI,QAAe,CAAA,EAAU,CAC5D,MAAM,SAAS,OAAO,EACtB,MAAM,aAAe,QAAQ,cAAgB,IAAI,QAEjD,IAAI,QAAU,MAAM,KAAK,KAAK,SAAS,aAAa,EAG7C,KAAA,QAAQ,OAAS,GACZ,UAAA,KAAK,QAAQ,IAAK,CAAA,EAE9B,MAAM,KAAO,CAAC,KAAmB,WAAa,KAAe,CACvD,MAAQ,MAAQ,OAAS,MAGzB,KAAK,QAAQ,YAAc,OAG1B,aAAa,IAAI,KAAK,OAAO,GAChC,aAAa,IAAI,KAAK,QAAS,CAAE,CAAA,EAE/B,YACF,KAAK,KAAK,MAAM,EAClB,EAEI,SAAY,MAAqB,CAEhC,aAAa,IAAI,KAAK,OAAO,IAG9B,gBAAgBA,cACb,KAAA,SAAS,QAAQ,QAAQ,EAEnB,aAAA,OAAO,KAAK,OAAO,EAChC,KAAK,SAAS,OAAO,EAAA,EAEvB,IAAI,UAAY,UAChB,QAAS,EAAI,EAAG,UAAU,OAAS,EAAG,GAAK,EAAG,CAC5C,GAAI,GAAK,wBACD,MAAA,IAAI,MAAM,iDAAiD,EA4B5D,IA1BG,UAAA,QAAS,UAA6B,CAC9C,MAAM,KAAO,KAAK,KAAK,SAAS,OAAQ,EAAI,EACxC,MAAQ,OAGR,KAAK,UAAY,SAAS,SACxB,SAAS,OAAS,aACpB,KAAK,KAAK,KAAK,SAAS,gBAAiB,EAAK,CAAC,EAC/C,MAAM,KAAK,SAAS,UAAU,EAAE,QAAS,MAAe,CACtD,MAAM,MAAQ,KAAK,KAAK,KAAM,EAAK,EACnC,KAAK,MAAO,EAAK,EACb,iBAAiBA,cACb,MAAA,SAAS,QAAS,YAAqB,CAC3C,KAAK,WAAY,EAAK,CAAA,CACvB,CACH,CACD,GACQ,SAAS,OAAS,cAC3B,KAAK,KAAK,IAAI,GAGlB,KAAK,IAAI,EAAA,CACV,EACI,KAAA,SAAS,QAAQ,QAAQ,EAC9B,UAAY,MAAM,KAAK,KAAK,SAAS,aAAa,EAClD,QAAU,UAAU,QACb,QAAQ,OAAS,GACZ,UAAA,KAAK,QAAQ,IAAK,CAAA,CAEhC,CACF,CAEO,OACL,UACA,QAAkC,GAC5B,CACM,UAAA,WAAa,KAAK,SAAS,YAAY,EAC7C,MAAA,iBAAmB,QAEtB,UAAA,IAAK,UAA6B,CACjC,MAAM,KAAO,KAAK,KAAK,SAAS,OAAQ,EAAI,EAC5C,OAAI,MAAQ,KACH,KAEL,aAAa,IAAI,KAAK,OAAO,GAC/B,aAAa,IAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EACrC,OAEP,aAAa,IAAI,KAAK,QAAS,CAAC,QAAQ,CAAC,EAClC,KACT,CACD,EACA,QAAS,MAAsB,CAC1B,MAAQ,MAAQ,OAAS,MAAQ,aAAa,IAAI,KAAK,OAAO,GAC3D,KAAA,OAAO,aAAa,IAAI,KAAK,OAAO,GAAK,GAAI,OAAO,CAC3D,CACD,EACH,QAAQ,aAAe,aACnB,aAAa,IAAI,KAAK,OAAO,GAC/B,MAAM,OAAO,aAAa,IAAI,KAAK,OAAO,EAAG,OAAO,EAEjD,KAAA,SAAS,UAAW,OAAO,CAClC,CACF,EAnME,YAAc,SAAW,SACzB,YAAc,aAAeI,YACf,YAAA,gBAAqC,CAACA,YAAWC,eAAa,EAC5E,YAAc,MAAQ,MAAM,WAC5B,YAAc,QAAU,MAL1B,IAAM,WAAN,YAsMA,MAAA,aAAe,WCnNT,UAAN,MAAM,kBAAiBH,UAAyB,CAI9C,OAAc,OAAO,MAAqB,CACjC,OAAA,SAAS,eAAe,KAAK,CACtC,CAEA,OAAc,MAAM,QAAuB,CACzC,OAAO,QAAQ,IACjB,CAKA,YAAY,OAAc,KAAY,CACpC,MAAM,OAAQ,IAAI,EAClB,KAAK,KAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,CAC7C,CAEO,SAAS,MAAe,OAAsB,CACnD,KAAK,QAAQ,KAAO,KAAK,KACvB,KAAK,KAAK,MAAM,EAAG,KAAK,EAAI,KAAK,KAAK,MAAM,MAAQ,MAAM,CAC9D,CAEO,MAAM,KAAY,OAAwB,CAC3C,OAAA,KAAK,UAAY,KACZ,OAEF,EACT,CAEO,SAAS,MAAe,MAAe,IAAiB,CACzD,KAAO,MACJ,KAAA,KAAO,KAAK,KAAK,MAAM,EAAG,KAAK,EAAI,MAAQ,KAAK,KAAK,MAAM,KAAK,EAChE,KAAA,QAAQ,KAAO,KAAK,MAEnB,MAAA,SAAS,MAAO,MAAO,GAAG,CAEpC,CAEO,QAAiB,CACtB,OAAO,KAAK,KAAK,MACnB,CAEO,SAAS,QAAuC,CACrD,MAAM,SAAS,OAAO,EACtB,KAAK,KAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,EACvC,KAAK,KAAK,SAAW,EACvB,KAAK,OAAO,EACH,KAAK,gBAAgB,WAAY,KAAK,KAAK,OAAS,OAC7D,KAAK,SAAS,KAAK,OAAA,EAAW,KAAK,KAAkB,OAAO,EAC5D,KAAK,KAAK,SAEd,CAEO,SAAS,MAAe,WAAa,GAAuB,CAC1D,MAAA,CAAC,KAAK,QAAS,KAAK,CAC7B,CAEO,MAAM,MAAe,MAAQ,GAAoB,CACtD,GAAI,CAAC,MAAO,CACV,GAAI,QAAU,EACL,OAAA,KAEL,GAAA,QAAU,KAAK,SACjB,OAAO,KAAK,IAEhB,CACM,MAAA,MAAQ,KAAK,OAAO,OAAO,KAAK,QAAQ,UAAU,KAAK,CAAC,EAC9D,YAAK,OAAO,aAAa,MAAO,KAAK,MAAQ,MAAS,EACtD,KAAK,KAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,EACpC,KACT,CAEO,OACL,UACA,SACM,CAEJ,UAAU,KAAM,UAEZ,SAAS,OAAS,iBAAmB,SAAS,SAAW,KAAK,OAEjE,IAED,KAAK,KAAO,KAAK,QAAQ,MAAM,KAAK,OAAO,EAE/C,CAEO,OAAgB,CACrB,OAAO,KAAK,IACd,CACF,EA5FE,UAAuB,SAAW,OAClC,UAAc,MAAQ,MAAM,YAF9B,IAAM,SAAN,UA+FA,MAAA,WAAe"}