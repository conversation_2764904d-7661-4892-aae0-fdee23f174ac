{"name": "@types/quill", "version": "1.3.10", "description": "TypeScript definitions for Quill", "license": "MIT", "contributors": [{"name": "Sumit", "url": "https://github.com/sumitkm", "githubUsername": "sumitkm"}, {"name": "<PERSON>", "url": "https://github.com/guillaume-ro-fr", "githubUsername": "guillaume-ro-fr"}, {"name": "<PERSON>", "url": "https://github.com/43081j", "githubUsername": "43081j"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/AnielloFalcone", "githubUsername": "AnielloFalcone"}, {"name": "<PERSON>", "url": "https://github.com/mhamri", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"parchment": "^1.1.2"}, "typesPublisherContentHash": "2483630ee29fdf4f03b09b41c35faff526b532bade15446ac2ce2a4a9ce5e9a3", "typeScriptVersion": "2.0"}