{"version": 3, "file": "quill.js", "names": ["<PERSON><PERSON><PERSON>", "AlignClass", "AlignStyle", "DirectionAttribute", "DirectionClass", "DirectionStyle", "Indent", "Blockquote", "Header", "List", "BackgroundClass", "BackgroundStyle", "ColorClass", "ColorStyle", "FontClass", "FontStyle", "SizeClass", "SizeStyle", "Bold", "Italic", "Link", "<PERSON><PERSON><PERSON>", "Strike", "Underline", "Formula", "Image", "Video", "CodeBlock", "Code", "InlineCode", "Syntax", "Table", "<PERSON><PERSON><PERSON>", "Icons", "Picker", "ColorPicker", "IconPicker", "<PERSON><PERSON><PERSON>", "BubbleTheme", "SnowTheme", "register", "AttributeMap", "Delta", "<PERSON><PERSON><PERSON>", "Op", "OpIterator", "Parchment", "Range"], "sources": ["../src/quill.ts"], "sourcesContent": ["import Quill from './core.js';\nimport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n} from './core.js';\n\nimport { AlignClass, AlignStyle } from './formats/align.js';\nimport {\n  DirectionAttribute,\n  DirectionClass,\n  DirectionStyle,\n} from './formats/direction.js';\nimport Indent from './formats/indent.js';\n\nimport Blockquote from './formats/blockquote.js';\nimport Header from './formats/header.js';\nimport List from './formats/list.js';\n\nimport { BackgroundClass, BackgroundStyle } from './formats/background.js';\nimport { ColorClass, ColorStyle } from './formats/color.js';\nimport { FontClass, FontStyle } from './formats/font.js';\nimport { SizeClass, SizeStyle } from './formats/size.js';\n\nimport Bold from './formats/bold.js';\nimport Italic from './formats/italic.js';\nimport Link from './formats/link.js';\nimport Script from './formats/script.js';\nimport Strike from './formats/strike.js';\nimport Underline from './formats/underline.js';\n\nimport Formula from './formats/formula.js';\nimport Image from './formats/image.js';\nimport Video from './formats/video.js';\n\nimport CodeBlock, { Code as InlineCode } from './formats/code.js';\n\nimport Syntax from './modules/syntax.js';\nimport Table from './modules/table.js';\nimport Toolbar from './modules/toolbar.js';\n\nimport Icons from './ui/icons.js';\nimport Picker from './ui/picker.js';\nimport ColorPicker from './ui/color-picker.js';\nimport IconPicker from './ui/icon-picker.js';\nimport Tooltip from './ui/tooltip.js';\n\nimport BubbleTheme from './themes/bubble.js';\nimport SnowTheme from './themes/snow.js';\n\nQuill.register(\n  {\n    'attributors/attribute/direction': DirectionAttribute,\n\n    'attributors/class/align': AlignClass,\n    'attributors/class/background': BackgroundClass,\n    'attributors/class/color': ColorClass,\n    'attributors/class/direction': DirectionClass,\n    'attributors/class/font': FontClass,\n    'attributors/class/size': SizeClass,\n\n    'attributors/style/align': AlignStyle,\n    'attributors/style/background': BackgroundStyle,\n    'attributors/style/color': ColorStyle,\n    'attributors/style/direction': DirectionStyle,\n    'attributors/style/font': FontStyle,\n    'attributors/style/size': SizeStyle,\n  },\n  true,\n);\n\nQuill.register(\n  {\n    'formats/align': AlignClass,\n    'formats/direction': DirectionClass,\n    'formats/indent': Indent,\n\n    'formats/background': BackgroundStyle,\n    'formats/color': ColorStyle,\n    'formats/font': FontClass,\n    'formats/size': SizeClass,\n\n    'formats/blockquote': Blockquote,\n    'formats/code-block': CodeBlock,\n    'formats/header': Header,\n    'formats/list': List,\n\n    'formats/bold': Bold,\n    'formats/code': InlineCode,\n    'formats/italic': Italic,\n    'formats/link': Link,\n    'formats/script': Script,\n    'formats/strike': Strike,\n    'formats/underline': Underline,\n\n    'formats/formula': Formula,\n    'formats/image': Image,\n    'formats/video': Video,\n\n    'modules/syntax': Syntax,\n    'modules/table': Table,\n    'modules/toolbar': Toolbar,\n\n    'themes/bubble': BubbleTheme,\n    'themes/snow': SnowTheme,\n\n    'ui/icons': Icons,\n    'ui/picker': Picker,\n    'ui/icon-picker': IconPicker,\n    'ui/color-picker': ColorPicker,\n    'ui/tooltip': Tooltip,\n  },\n  true,\n);\n\nexport {\n  AttributeMap,\n  Delta,\n  Module,\n  Op,\n  OpIterator,\n  Parchment,\n  Range,\n} from './core.js';\nexport type {\n  Bounds,\n  DebugLevel,\n  EmitterSource,\n  ExpandedQuillOptions,\n  QuillOptions,\n};\n\nexport default Quill;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,WAAW;AAS7B,SAASC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3D,SACEC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,QACT,wBAAwB;AAC/B,OAAOC,MAAM,MAAM,qBAAqB;AAExC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AAEpC,SAASC,eAAe,EAAEC,eAAe,QAAQ,yBAAyB;AAC1E,SAASC,UAAU,EAAEC,UAAU,QAAQ,oBAAoB;AAC3D,SAASC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AACxD,SAASC,SAAS,EAAEC,SAAS,QAAQ,mBAAmB;AAExD,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,wBAAwB;AAE9C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,KAAK,MAAM,oBAAoB;AAEtC,OAAOC,SAAS,IAAIC,IAAI,IAAIC,UAAU,QAAQ,mBAAmB;AAEjE,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,OAAO,MAAM,sBAAsB;AAE1C,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,OAAO,MAAM,iBAAiB;AAErC,OAAOC,WAAW,MAAM,oBAAoB;AAC5C,OAAOC,SAAS,MAAM,kBAAkB;AAExCvC,KAAK,CAACwC,QAAQ,CACZ;EACE,iCAAiC,EAAErC,kBAAkB;EAErD,yBAAyB,EAAEF,UAAU;EACrC,8BAA8B,EAAES,eAAe;EAC/C,yBAAyB,EAAEE,UAAU;EACrC,6BAA6B,EAAER,cAAc;EAC7C,wBAAwB,EAAEU,SAAS;EACnC,wBAAwB,EAAEE,SAAS;EAEnC,yBAAyB,EAAEd,UAAU;EACrC,8BAA8B,EAAES,eAAe;EAC/C,yBAAyB,EAAEE,UAAU;EACrC,6BAA6B,EAAER,cAAc;EAC7C,wBAAwB,EAAEU,SAAS;EACnC,wBAAwB,EAAEE;AAC5B,CAAC,EACD,IACF,CAAC;AAEDjB,KAAK,CAACwC,QAAQ,CACZ;EACE,eAAe,EAAEvC,UAAU;EAC3B,mBAAmB,EAAEG,cAAc;EACnC,gBAAgB,EAAEE,MAAM;EAExB,oBAAoB,EAAEK,eAAe;EACrC,eAAe,EAAEE,UAAU;EAC3B,cAAc,EAAEC,SAAS;EACzB,cAAc,EAAEE,SAAS;EAEzB,oBAAoB,EAAET,UAAU;EAChC,oBAAoB,EAAEoB,SAAS;EAC/B,gBAAgB,EAAEnB,MAAM;EACxB,cAAc,EAAEC,IAAI;EAEpB,cAAc,EAAES,IAAI;EACpB,cAAc,EAAEW,UAAU;EAC1B,gBAAgB,EAAEV,MAAM;EACxB,cAAc,EAAEC,IAAI;EACpB,gBAAgB,EAAEC,MAAM;EACxB,gBAAgB,EAAEC,MAAM;EACxB,mBAAmB,EAAEC,SAAS;EAE9B,iBAAiB,EAAEC,OAAO;EAC1B,eAAe,EAAEC,KAAK;EACtB,eAAe,EAAEC,KAAK;EAEtB,gBAAgB,EAAEI,MAAM;EACxB,eAAe,EAAEC,KAAK;EACtB,iBAAiB,EAAEC,OAAO;EAE1B,eAAe,EAAEM,WAAW;EAC5B,aAAa,EAAEC,SAAS;EAExB,UAAU,EAAEN,KAAK;EACjB,WAAW,EAAEC,MAAM;EACnB,gBAAgB,EAAEE,UAAU;EAC5B,iBAAiB,EAAED,WAAW;EAC9B,YAAY,EAAEE;AAChB,CAAC,EACD,IACF,CAAC;AAED,SACEI,YAAY,EACZC,KAAK,EACLC,MAAM,EACNC,EAAE,EACFC,UAAU,EACVC,SAAS,EACTC,KAAK,QACA,WAAW;AASlB,eAAe/C,KAAK", "ignoreList": []}