{"version": 3, "file": "input.js", "names": ["Delta", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "deleteRange", "INSERT_TYPES", "Input", "constructor", "quill", "options", "root", "addEventListener", "event", "handleBeforeInput", "test", "navigator", "userAgent", "on", "events", "COMPOSITION_BEFORE_START", "handleCompositionStart", "range", "replaceText", "text", "arguments", "length", "undefined", "formats", "getFormat", "index", "updateContents", "retain", "insert", "sources", "USER", "setSelection", "SILENT", "composition", "isComposing", "defaultPrevented", "includes", "inputType", "staticRange", "getTargetRanges", "collapsed", "getPlainTextFromInputEvent", "normalized", "selection", "normalizeNative", "normalizedToRange", "preventDefault", "getSelection", "data", "dataTransfer", "types", "getData"], "sources": ["../../src/modules/input.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type { Range } from '../core/selection.js';\nimport { deleteRange } from './keyboard.js';\n\nconst INSERT_TYPES = ['insertText', 'insertReplacementText'];\n\nclass Input extends Module {\n  constructor(quill: Quill, options: Record<string, never>) {\n    super(quill, options);\n\n    quill.root.addEventListener('beforeinput', (event) => {\n      this.handleBeforeInput(event);\n    });\n\n    // Gboard with English input on Android triggers `compositionstart` sometimes even\n    // users are not going to type anything.\n    if (!/Android/i.test(navigator.userAgent)) {\n      quill.on(Quill.events.COMPOSITION_BEFORE_START, () => {\n        this.handleCompositionStart();\n      });\n    }\n  }\n\n  private deleteRange(range: Range) {\n    deleteRange({ range, quill: this.quill });\n  }\n\n  private replaceText(range: Range, text = '') {\n    if (range.length === 0) return false;\n\n    if (text) {\n      // Follow the native behavior that inherits the formats of the first character\n      const formats = this.quill.getFormat(range.index, 1);\n      this.deleteRange(range);\n      this.quill.updateContents(\n        new Delta().retain(range.index).insert(text, formats),\n        Quill.sources.USER,\n      );\n    } else {\n      this.deleteRange(range);\n    }\n\n    this.quill.setSelection(range.index + text.length, 0, Quill.sources.SILENT);\n    return true;\n  }\n\n  private handleBeforeInput(event: InputEvent) {\n    if (\n      this.quill.composition.isComposing ||\n      event.defaultPrevented ||\n      !INSERT_TYPES.includes(event.inputType)\n    ) {\n      return;\n    }\n\n    const staticRange = event.getTargetRanges\n      ? event.getTargetRanges()[0]\n      : null;\n    if (!staticRange || staticRange.collapsed === true) {\n      return;\n    }\n\n    const text = getPlainTextFromInputEvent(event);\n    if (text == null) {\n      return;\n    }\n    const normalized = this.quill.selection.normalizeNative(staticRange);\n    const range = normalized\n      ? this.quill.selection.normalizedToRange(normalized)\n      : null;\n    if (range && this.replaceText(range, text)) {\n      event.preventDefault();\n    }\n  }\n\n  private handleCompositionStart() {\n    const range = this.quill.getSelection();\n    if (range) {\n      this.replaceText(range);\n    }\n  }\n}\n\nfunction getPlainTextFromInputEvent(event: InputEvent) {\n  // When `inputType` is \"insertText\":\n  // - `event.data` should be string (Safari uses `event.dataTransfer`).\n  // - `event.dataTransfer` should be null.\n  // When `inputType` is \"insertReplacementText\":\n  // - `event.data` should be null.\n  // - `event.dataTransfer` should contain \"text/plain\" data.\n\n  if (typeof event.data === 'string') {\n    return event.data;\n  }\n  if (event.dataTransfer?.types.includes('text/plain')) {\n    return event.dataTransfer.getData('text/plain');\n  }\n  return null;\n}\n\nexport default Input;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,SAASC,WAAW,QAAQ,eAAe;AAE3C,MAAMC,YAAY,GAAG,CAAC,YAAY,EAAE,uBAAuB,CAAC;AAE5D,MAAMC,KAAK,SAASJ,MAAM,CAAC;EACzBK,WAAWA,CAACC,KAAY,EAAEC,OAA8B,EAAE;IACxD,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IAErBD,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAGC,KAAK,IAAK;MACpD,IAAI,CAACC,iBAAiB,CAACD,KAAK,CAAC;IAC/B,CAAC,CAAC;;IAEF;IACA;IACA,IAAI,CAAC,UAAU,CAACE,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,EAAE;MACzCR,KAAK,CAACS,EAAE,CAACd,KAAK,CAACe,MAAM,CAACC,wBAAwB,EAAE,MAAM;QACpD,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC/B,CAAC,CAAC;IACJ;EACF;EAEQhB,WAAWA,CAACiB,KAAY,EAAE;IAChCjB,WAAW,CAAC;MAAEiB,KAAK;MAAEb,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EAC3C;EAEQc,WAAWA,CAACD,KAAY,EAAa;IAAA,IAAXE,IAAI,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,EAAE;IACzC,IAAIH,KAAK,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAEpC,IAAIF,IAAI,EAAE;MACR;MACA,MAAMI,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACoB,SAAS,CAACP,KAAK,CAACQ,KAAK,EAAE,CAAC,CAAC;MACpD,IAAI,CAACzB,WAAW,CAACiB,KAAK,CAAC;MACvB,IAAI,CAACb,KAAK,CAACsB,cAAc,CACvB,IAAI7B,KAAK,CAAC,CAAC,CAAC8B,MAAM,CAACV,KAAK,CAACQ,KAAK,CAAC,CAACG,MAAM,CAACT,IAAI,EAAEI,OAAO,CAAC,EACrDxB,KAAK,CAAC8B,OAAO,CAACC,IAChB,CAAC;IACH,CAAC,MAAM;MACL,IAAI,CAAC9B,WAAW,CAACiB,KAAK,CAAC;IACzB;IAEA,IAAI,CAACb,KAAK,CAAC2B,YAAY,CAACd,KAAK,CAACQ,KAAK,GAAGN,IAAI,CAACE,MAAM,EAAE,CAAC,EAAEtB,KAAK,CAAC8B,OAAO,CAACG,MAAM,CAAC;IAC3E,OAAO,IAAI;EACb;EAEQvB,iBAAiBA,CAACD,KAAiB,EAAE;IAC3C,IACE,IAAI,CAACJ,KAAK,CAAC6B,WAAW,CAACC,WAAW,IAClC1B,KAAK,CAAC2B,gBAAgB,IACtB,CAAClC,YAAY,CAACmC,QAAQ,CAAC5B,KAAK,CAAC6B,SAAS,CAAC,EACvC;MACA;IACF;IAEA,MAAMC,WAAW,GAAG9B,KAAK,CAAC+B,eAAe,GACrC/B,KAAK,CAAC+B,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1B,IAAI;IACR,IAAI,CAACD,WAAW,IAAIA,WAAW,CAACE,SAAS,KAAK,IAAI,EAAE;MAClD;IACF;IAEA,MAAMrB,IAAI,GAAGsB,0BAA0B,CAACjC,KAAK,CAAC;IAC9C,IAAIW,IAAI,IAAI,IAAI,EAAE;MAChB;IACF;IACA,MAAMuB,UAAU,GAAG,IAAI,CAACtC,KAAK,CAACuC,SAAS,CAACC,eAAe,CAACN,WAAW,CAAC;IACpE,MAAMrB,KAAK,GAAGyB,UAAU,GACpB,IAAI,CAACtC,KAAK,CAACuC,SAAS,CAACE,iBAAiB,CAACH,UAAU,CAAC,GAClD,IAAI;IACR,IAAIzB,KAAK,IAAI,IAAI,CAACC,WAAW,CAACD,KAAK,EAAEE,IAAI,CAAC,EAAE;MAC1CX,KAAK,CAACsC,cAAc,CAAC,CAAC;IACxB;EACF;EAEQ9B,sBAAsBA,CAAA,EAAG;IAC/B,MAAMC,KAAK,GAAG,IAAI,CAACb,KAAK,CAAC2C,YAAY,CAAC,CAAC;IACvC,IAAI9B,KAAK,EAAE;MACT,IAAI,CAACC,WAAW,CAACD,KAAK,CAAC;IACzB;EACF;AACF;AAEA,SAASwB,0BAA0BA,CAACjC,KAAiB,EAAE;EACrD;EACA;EACA;EACA;EACA;EACA;;EAEA,IAAI,OAAOA,KAAK,CAACwC,IAAI,KAAK,QAAQ,EAAE;IAClC,OAAOxC,KAAK,CAACwC,IAAI;EACnB;EACA,IAAIxC,KAAK,CAACyC,YAAY,EAAEC,KAAK,CAACd,QAAQ,CAAC,YAAY,CAAC,EAAE;IACpD,OAAO5B,KAAK,CAACyC,YAAY,CAACE,OAAO,CAAC,YAAY,CAAC;EACjD;EACA,OAAO,IAAI;AACb;AAEA,eAAejD,KAAK", "ignoreList": []}