{"version": 3, "file": "toolbar.js", "names": ["Delta", "EmbedBlot", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "logger", "<PERSON><PERSON><PERSON>", "debug", "<PERSON><PERSON><PERSON>", "constructor", "quill", "options", "Array", "isArray", "container", "document", "createElement", "setAttribute", "addControls", "parentNode", "insertBefore", "querySelector", "HTMLElement", "error", "classList", "add", "controls", "handlers", "Object", "keys", "for<PERSON>ach", "format", "handler", "add<PERSON><PERSON><PERSON>", "from", "querySelectorAll", "input", "attach", "on", "events", "EDITOR_CHANGE", "range", "selection", "getRange", "update", "find", "className", "indexOf", "slice", "length", "tagName", "scroll", "query", "warn", "eventName", "addEventListener", "e", "value", "selectedIndex", "selected", "hasAttribute", "contains", "preventDefault", "focus", "call", "prototype", "prompt", "updateContents", "retain", "index", "delete", "insert", "sources", "USER", "push", "formats", "getFormat", "pair", "option", "replace", "remove", "isActive", "getAttribute", "toString", "toggle", "DEFAULTS", "addButton", "append<PERSON><PERSON><PERSON>", "groups", "group", "control", "addSelect", "values", "String", "clean", "getSelection", "name", "INLINE", "removeFormat", "direction", "align", "indent", "parseInt", "modifier", "link", "list", "default"], "sources": ["../../src/modules/toolbar.ts"], "sourcesContent": ["import Delta from 'quill-delta';\nimport { EmbedBlot, Scope } from 'parchment';\nimport Quill from '../core/quill.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport type { Range } from '../core/selection.js';\n\nconst debug = logger('quill:toolbar');\n\ntype Handler = (this: Toolbar, value: any) => void;\n\nexport type ToolbarConfig = Array<\n  string[] | Array<string | Record<string, unknown>>\n>;\nexport interface ToolbarProps {\n  container?: HTMLElement | ToolbarConfig | null;\n  handlers?: Record<string, Handler>;\n  option?: number;\n  module?: boolean;\n  theme?: boolean;\n}\n\nclass Toolbar extends Module<ToolbarProps> {\n  static DEFAULTS: ToolbarProps;\n\n  container?: HTMLElement | null;\n  controls: [string, HTMLElement][];\n  handlers: Record<string, Handler>;\n\n  constructor(quill: Quill, options: Partial<ToolbarProps>) {\n    super(quill, options);\n    if (Array.isArray(this.options.container)) {\n      const container = document.createElement('div');\n      container.setAttribute('role', 'toolbar');\n      addControls(container, this.options.container);\n      quill.container?.parentNode?.insertBefore(container, quill.container);\n      this.container = container;\n    } else if (typeof this.options.container === 'string') {\n      this.container = document.querySelector(this.options.container);\n    } else {\n      this.container = this.options.container;\n    }\n    if (!(this.container instanceof HTMLElement)) {\n      debug.error('Container required for toolbar', this.options);\n      return;\n    }\n    this.container.classList.add('ql-toolbar');\n    this.controls = [];\n    this.handlers = {};\n    if (this.options.handlers) {\n      Object.keys(this.options.handlers).forEach((format) => {\n        const handler = this.options.handlers?.[format];\n        if (handler) {\n          this.addHandler(format, handler);\n        }\n      });\n    }\n    Array.from(this.container.querySelectorAll('button, select')).forEach(\n      (input) => {\n        // @ts-expect-error\n        this.attach(input);\n      },\n    );\n    this.quill.on(Quill.events.EDITOR_CHANGE, () => {\n      const [range] = this.quill.selection.getRange(); // quill.getSelection triggers update\n      this.update(range);\n    });\n  }\n\n  addHandler(format: string, handler: Handler) {\n    this.handlers[format] = handler;\n  }\n\n  attach(input: HTMLElement) {\n    let format = Array.from(input.classList).find((className) => {\n      return className.indexOf('ql-') === 0;\n    });\n    if (!format) return;\n    format = format.slice('ql-'.length);\n    if (input.tagName === 'BUTTON') {\n      input.setAttribute('type', 'button');\n    }\n    if (\n      this.handlers[format] == null &&\n      this.quill.scroll.query(format) == null\n    ) {\n      debug.warn('ignoring attaching to nonexistent format', format, input);\n      return;\n    }\n    const eventName = input.tagName === 'SELECT' ? 'change' : 'click';\n    input.addEventListener(eventName, (e) => {\n      let value;\n      if (input.tagName === 'SELECT') {\n        // @ts-expect-error\n        if (input.selectedIndex < 0) return;\n        // @ts-expect-error\n        const selected = input.options[input.selectedIndex];\n        if (selected.hasAttribute('selected')) {\n          value = false;\n        } else {\n          value = selected.value || false;\n        }\n      } else {\n        if (input.classList.contains('ql-active')) {\n          value = false;\n        } else {\n          // @ts-expect-error\n          value = input.value || !input.hasAttribute('value');\n        }\n        e.preventDefault();\n      }\n      this.quill.focus();\n      const [range] = this.quill.selection.getRange();\n      if (this.handlers[format] != null) {\n        this.handlers[format].call(this, value);\n      } else if (\n        // @ts-expect-error\n        this.quill.scroll.query(format).prototype instanceof EmbedBlot\n      ) {\n        value = prompt(`Enter ${format}`); // eslint-disable-line no-alert\n        if (!value) return;\n        this.quill.updateContents(\n          new Delta()\n            // @ts-expect-error Fix me later\n            .retain(range.index)\n            // @ts-expect-error Fix me later\n            .delete(range.length)\n            .insert({ [format]: value }),\n          Quill.sources.USER,\n        );\n      } else {\n        this.quill.format(format, value, Quill.sources.USER);\n      }\n      this.update(range);\n    });\n    this.controls.push([format, input]);\n  }\n\n  update(range: Range | null) {\n    const formats = range == null ? {} : this.quill.getFormat(range);\n    this.controls.forEach((pair) => {\n      const [format, input] = pair;\n      if (input.tagName === 'SELECT') {\n        let option: HTMLOptionElement | null = null;\n        if (range == null) {\n          option = null;\n        } else if (formats[format] == null) {\n          option = input.querySelector('option[selected]');\n        } else if (!Array.isArray(formats[format])) {\n          let value = formats[format];\n          if (typeof value === 'string') {\n            value = value.replace(/\"/g, '\\\\\"');\n          }\n          option = input.querySelector(`option[value=\"${value}\"]`);\n        }\n        if (option == null) {\n          // @ts-expect-error TODO fix me later\n          input.value = ''; // TODO make configurable?\n          // @ts-expect-error TODO fix me later\n          input.selectedIndex = -1;\n        } else {\n          option.selected = true;\n        }\n      } else if (range == null) {\n        input.classList.remove('ql-active');\n        input.setAttribute('aria-pressed', 'false');\n      } else if (input.hasAttribute('value')) {\n        // both being null should match (default values)\n        // '1' should match with 1 (headers)\n        const value = formats[format] as boolean | number | string | object;\n        const isActive =\n          value === input.getAttribute('value') ||\n          (value != null && value.toString() === input.getAttribute('value')) ||\n          (value == null && !input.getAttribute('value'));\n        input.classList.toggle('ql-active', isActive);\n        input.setAttribute('aria-pressed', isActive.toString());\n      } else {\n        const isActive = formats[format] != null;\n        input.classList.toggle('ql-active', isActive);\n        input.setAttribute('aria-pressed', isActive.toString());\n      }\n    });\n  }\n}\nToolbar.DEFAULTS = {};\n\nfunction addButton(container: HTMLElement, format: string, value?: string) {\n  const input = document.createElement('button');\n  input.setAttribute('type', 'button');\n  input.classList.add(`ql-${format}`);\n  input.setAttribute('aria-pressed', 'false');\n  if (value != null) {\n    input.value = value;\n    input.setAttribute('aria-label', `${format}: ${value}`);\n  } else {\n    input.setAttribute('aria-label', format);\n  }\n  container.appendChild(input);\n}\n\nfunction addControls(\n  container: HTMLElement,\n  groups:\n    | (string | Record<string, unknown>)[][]\n    | (string | Record<string, unknown>)[],\n) {\n  if (!Array.isArray(groups[0])) {\n    // @ts-expect-error\n    groups = [groups];\n  }\n  groups.forEach((controls: any) => {\n    const group = document.createElement('span');\n    group.classList.add('ql-formats');\n    controls.forEach((control: any) => {\n      if (typeof control === 'string') {\n        addButton(group, control);\n      } else {\n        const format = Object.keys(control)[0];\n        const value = control[format];\n        if (Array.isArray(value)) {\n          addSelect(group, format, value);\n        } else {\n          addButton(group, format, value);\n        }\n      }\n    });\n    container.appendChild(group);\n  });\n}\n\nfunction addSelect(\n  container: HTMLElement,\n  format: string,\n  values: Array<string | boolean>,\n) {\n  const input = document.createElement('select');\n  input.classList.add(`ql-${format}`);\n  values.forEach((value) => {\n    const option = document.createElement('option');\n    if (value !== false) {\n      option.setAttribute('value', String(value));\n    } else {\n      option.setAttribute('selected', 'selected');\n    }\n    input.appendChild(option);\n  });\n  container.appendChild(input);\n}\n\nToolbar.DEFAULTS = {\n  container: null,\n  handlers: {\n    clean() {\n      const range = this.quill.getSelection();\n      if (range == null) return;\n      if (range.length === 0) {\n        const formats = this.quill.getFormat();\n        Object.keys(formats).forEach((name) => {\n          // Clean functionality in existing apps only clean inline formats\n          if (this.quill.scroll.query(name, Scope.INLINE) != null) {\n            this.quill.format(name, false, Quill.sources.USER);\n          }\n        });\n      } else {\n        this.quill.removeFormat(range.index, range.length, Quill.sources.USER);\n      }\n    },\n    direction(value) {\n      const { align } = this.quill.getFormat();\n      if (value === 'rtl' && align == null) {\n        this.quill.format('align', 'right', Quill.sources.USER);\n      } else if (!value && align === 'right') {\n        this.quill.format('align', false, Quill.sources.USER);\n      }\n      this.quill.format('direction', value, Quill.sources.USER);\n    },\n    indent(value) {\n      const range = this.quill.getSelection();\n      // @ts-expect-error\n      const formats = this.quill.getFormat(range);\n      // @ts-expect-error\n      const indent = parseInt(formats.indent || 0, 10);\n      if (value === '+1' || value === '-1') {\n        let modifier = value === '+1' ? 1 : -1;\n        if (formats.direction === 'rtl') modifier *= -1;\n        this.quill.format('indent', indent + modifier, Quill.sources.USER);\n      }\n    },\n    link(value) {\n      if (value === true) {\n        value = prompt('Enter link URL:'); // eslint-disable-line no-alert\n      }\n      this.quill.format('link', value, Quill.sources.USER);\n    },\n    list(value) {\n      const range = this.quill.getSelection();\n      // @ts-expect-error\n      const formats = this.quill.getFormat(range);\n      if (value === 'check') {\n        if (formats.list === 'checked' || formats.list === 'unchecked') {\n          this.quill.format('list', false, Quill.sources.USER);\n        } else {\n          this.quill.format('list', 'unchecked', Quill.sources.USER);\n        }\n      } else {\n        this.quill.format('list', value, Quill.sources.USER);\n      }\n    },\n  },\n};\n\nexport { Toolbar as default, addControls };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,SAASC,SAAS,EAAEC,KAAK,QAAQ,WAAW;AAC5C,OAAOC,KAAK,MAAM,kBAAkB;AACpC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AAGtC,MAAMC,KAAK,GAAGF,MAAM,CAAC,eAAe,CAAC;AAerC,MAAMG,OAAO,SAASF,MAAM,CAAe;EAOzCG,WAAWA,CAACC,KAAY,EAAEC,OAA8B,EAAE;IACxD,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAIC,KAAK,CAACC,OAAO,CAAC,IAAI,CAACF,OAAO,CAACG,SAAS,CAAC,EAAE;MACzC,MAAMA,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC/CF,SAAS,CAACG,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC;MACzCC,WAAW,CAACJ,SAAS,EAAE,IAAI,CAACH,OAAO,CAACG,SAAS,CAAC;MAC9CJ,KAAK,CAACI,SAAS,EAAEK,UAAU,EAAEC,YAAY,CAACN,SAAS,EAAEJ,KAAK,CAACI,SAAS,CAAC;MACrE,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC5B,CAAC,MAAM,IAAI,OAAO,IAAI,CAACH,OAAO,CAACG,SAAS,KAAK,QAAQ,EAAE;MACrD,IAAI,CAACA,SAAS,GAAGC,QAAQ,CAACM,aAAa,CAAC,IAAI,CAACV,OAAO,CAACG,SAAS,CAAC;IACjE,CAAC,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,IAAI,CAACH,OAAO,CAACG,SAAS;IACzC;IACA,IAAI,EAAE,IAAI,CAACA,SAAS,YAAYQ,WAAW,CAAC,EAAE;MAC5Cf,KAAK,CAACgB,KAAK,CAAC,gCAAgC,EAAE,IAAI,CAACZ,OAAO,CAAC;MAC3D;IACF;IACA,IAAI,CAACG,SAAS,CAACU,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAC1C,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAC;IAClB,IAAI,IAAI,CAAChB,OAAO,CAACgB,QAAQ,EAAE;MACzBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAClB,OAAO,CAACgB,QAAQ,CAAC,CAACG,OAAO,CAAEC,MAAM,IAAK;QACrD,MAAMC,OAAO,GAAG,IAAI,CAACrB,OAAO,CAACgB,QAAQ,GAAGI,MAAM,CAAC;QAC/C,IAAIC,OAAO,EAAE;UACX,IAAI,CAACC,UAAU,CAACF,MAAM,EAAEC,OAAO,CAAC;QAClC;MACF,CAAC,CAAC;IACJ;IACApB,KAAK,CAACsB,IAAI,CAAC,IAAI,CAACpB,SAAS,CAACqB,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,CAACL,OAAO,CAClEM,KAAK,IAAK;MACT;MACA,IAAI,CAACC,MAAM,CAACD,KAAK,CAAC;IACpB,CACF,CAAC;IACD,IAAI,CAAC1B,KAAK,CAAC4B,EAAE,CAAClC,KAAK,CAACmC,MAAM,CAACC,aAAa,EAAE,MAAM;MAC9C,MAAM,CAACC,KAAK,CAAC,GAAG,IAAI,CAAC/B,KAAK,CAACgC,SAAS,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;MACjD,IAAI,CAACC,MAAM,CAACH,KAAK,CAAC;IACpB,CAAC,CAAC;EACJ;EAEAR,UAAUA,CAACF,MAAc,EAAEC,OAAgB,EAAE;IAC3C,IAAI,CAACL,QAAQ,CAACI,MAAM,CAAC,GAAGC,OAAO;EACjC;EAEAK,MAAMA,CAACD,KAAkB,EAAE;IACzB,IAAIL,MAAM,GAAGnB,KAAK,CAACsB,IAAI,CAACE,KAAK,CAACZ,SAAS,CAAC,CAACqB,IAAI,CAAEC,SAAS,IAAK;MAC3D,OAAOA,SAAS,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAAChB,MAAM,EAAE;IACbA,MAAM,GAAGA,MAAM,CAACiB,KAAK,CAAC,KAAK,CAACC,MAAM,CAAC;IACnC,IAAIb,KAAK,CAACc,OAAO,KAAK,QAAQ,EAAE;MAC9Bd,KAAK,CAACnB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IACtC;IACA,IACE,IAAI,CAACU,QAAQ,CAACI,MAAM,CAAC,IAAI,IAAI,IAC7B,IAAI,CAACrB,KAAK,CAACyC,MAAM,CAACC,KAAK,CAACrB,MAAM,CAAC,IAAI,IAAI,EACvC;MACAxB,KAAK,CAAC8C,IAAI,CAAC,0CAA0C,EAAEtB,MAAM,EAAEK,KAAK,CAAC;MACrE;IACF;IACA,MAAMkB,SAAS,GAAGlB,KAAK,CAACc,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO;IACjEd,KAAK,CAACmB,gBAAgB,CAACD,SAAS,EAAGE,CAAC,IAAK;MACvC,IAAIC,KAAK;MACT,IAAIrB,KAAK,CAACc,OAAO,KAAK,QAAQ,EAAE;QAC9B;QACA,IAAId,KAAK,CAACsB,aAAa,GAAG,CAAC,EAAE;QAC7B;QACA,MAAMC,QAAQ,GAAGvB,KAAK,CAACzB,OAAO,CAACyB,KAAK,CAACsB,aAAa,CAAC;QACnD,IAAIC,QAAQ,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;UACrCH,KAAK,GAAG,KAAK;QACf,CAAC,MAAM;UACLA,KAAK,GAAGE,QAAQ,CAACF,KAAK,IAAI,KAAK;QACjC;MACF,CAAC,MAAM;QACL,IAAIrB,KAAK,CAACZ,SAAS,CAACqC,QAAQ,CAAC,WAAW,CAAC,EAAE;UACzCJ,KAAK,GAAG,KAAK;QACf,CAAC,MAAM;UACL;UACAA,KAAK,GAAGrB,KAAK,CAACqB,KAAK,IAAI,CAACrB,KAAK,CAACwB,YAAY,CAAC,OAAO,CAAC;QACrD;QACAJ,CAAC,CAACM,cAAc,CAAC,CAAC;MACpB;MACA,IAAI,CAACpD,KAAK,CAACqD,KAAK,CAAC,CAAC;MAClB,MAAM,CAACtB,KAAK,CAAC,GAAG,IAAI,CAAC/B,KAAK,CAACgC,SAAS,CAACC,QAAQ,CAAC,CAAC;MAC/C,IAAI,IAAI,CAAChB,QAAQ,CAACI,MAAM,CAAC,IAAI,IAAI,EAAE;QACjC,IAAI,CAACJ,QAAQ,CAACI,MAAM,CAAC,CAACiC,IAAI,CAAC,IAAI,EAAEP,KAAK,CAAC;MACzC,CAAC,MAAM;MACL;MACA,IAAI,CAAC/C,KAAK,CAACyC,MAAM,CAACC,KAAK,CAACrB,MAAM,CAAC,CAACkC,SAAS,YAAY/D,SAAS,EAC9D;QACAuD,KAAK,GAAGS,MAAM,CAAE,SAAQnC,MAAO,EAAC,CAAC,CAAC,CAAC;QACnC,IAAI,CAAC0B,KAAK,EAAE;QACZ,IAAI,CAAC/C,KAAK,CAACyD,cAAc,CACvB,IAAIlE,KAAK,CAAC;QACR;QAAA,CACCmE,MAAM,CAAC3B,KAAK,CAAC4B,KAAK;QACnB;QAAA,CACCC,MAAM,CAAC7B,KAAK,CAACQ,MAAM,CAAC,CACpBsB,MAAM,CAAC;UAAE,CAACxC,MAAM,GAAG0B;QAAM,CAAC,CAAC,EAC9BrD,KAAK,CAACoE,OAAO,CAACC,IAChB,CAAC;MACH,CAAC,MAAM;QACL,IAAI,CAAC/D,KAAK,CAACqB,MAAM,CAACA,MAAM,EAAE0B,KAAK,EAAErD,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;MACtD;MACA,IAAI,CAAC7B,MAAM,CAACH,KAAK,CAAC;IACpB,CAAC,CAAC;IACF,IAAI,CAACf,QAAQ,CAACgD,IAAI,CAAC,CAAC3C,MAAM,EAAEK,KAAK,CAAC,CAAC;EACrC;EAEAQ,MAAMA,CAACH,KAAmB,EAAE;IAC1B,MAAMkC,OAAO,GAAGlC,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC/B,KAAK,CAACkE,SAAS,CAACnC,KAAK,CAAC;IAChE,IAAI,CAACf,QAAQ,CAACI,OAAO,CAAE+C,IAAI,IAAK;MAC9B,MAAM,CAAC9C,MAAM,EAAEK,KAAK,CAAC,GAAGyC,IAAI;MAC5B,IAAIzC,KAAK,CAACc,OAAO,KAAK,QAAQ,EAAE;QAC9B,IAAI4B,MAAgC,GAAG,IAAI;QAC3C,IAAIrC,KAAK,IAAI,IAAI,EAAE;UACjBqC,MAAM,GAAG,IAAI;QACf,CAAC,MAAM,IAAIH,OAAO,CAAC5C,MAAM,CAAC,IAAI,IAAI,EAAE;UAClC+C,MAAM,GAAG1C,KAAK,CAACf,aAAa,CAAC,kBAAkB,CAAC;QAClD,CAAC,MAAM,IAAI,CAACT,KAAK,CAACC,OAAO,CAAC8D,OAAO,CAAC5C,MAAM,CAAC,CAAC,EAAE;UAC1C,IAAI0B,KAAK,GAAGkB,OAAO,CAAC5C,MAAM,CAAC;UAC3B,IAAI,OAAO0B,KAAK,KAAK,QAAQ,EAAE;YAC7BA,KAAK,GAAGA,KAAK,CAACsB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;UACpC;UACAD,MAAM,GAAG1C,KAAK,CAACf,aAAa,CAAE,iBAAgBoC,KAAM,IAAG,CAAC;QAC1D;QACA,IAAIqB,MAAM,IAAI,IAAI,EAAE;UAClB;UACA1C,KAAK,CAACqB,KAAK,GAAG,EAAE,CAAC,CAAC;UAClB;UACArB,KAAK,CAACsB,aAAa,GAAG,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLoB,MAAM,CAACnB,QAAQ,GAAG,IAAI;QACxB;MACF,CAAC,MAAM,IAAIlB,KAAK,IAAI,IAAI,EAAE;QACxBL,KAAK,CAACZ,SAAS,CAACwD,MAAM,CAAC,WAAW,CAAC;QACnC5C,KAAK,CAACnB,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;MAC7C,CAAC,MAAM,IAAImB,KAAK,CAACwB,YAAY,CAAC,OAAO,CAAC,EAAE;QACtC;QACA;QACA,MAAMH,KAAK,GAAGkB,OAAO,CAAC5C,MAAM,CAAuC;QACnE,MAAMkD,QAAQ,GACZxB,KAAK,KAAKrB,KAAK,CAAC8C,YAAY,CAAC,OAAO,CAAC,IACpCzB,KAAK,IAAI,IAAI,IAAIA,KAAK,CAAC0B,QAAQ,CAAC,CAAC,KAAK/C,KAAK,CAAC8C,YAAY,CAAC,OAAO,CAAE,IAClEzB,KAAK,IAAI,IAAI,IAAI,CAACrB,KAAK,CAAC8C,YAAY,CAAC,OAAO,CAAE;QACjD9C,KAAK,CAACZ,SAAS,CAAC4D,MAAM,CAAC,WAAW,EAAEH,QAAQ,CAAC;QAC7C7C,KAAK,CAACnB,YAAY,CAAC,cAAc,EAAEgE,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;MACzD,CAAC,MAAM;QACL,MAAMF,QAAQ,GAAGN,OAAO,CAAC5C,MAAM,CAAC,IAAI,IAAI;QACxCK,KAAK,CAACZ,SAAS,CAAC4D,MAAM,CAAC,WAAW,EAAEH,QAAQ,CAAC;QAC7C7C,KAAK,CAACnB,YAAY,CAAC,cAAc,EAAEgE,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;AACF;AACA3E,OAAO,CAAC6E,QAAQ,GAAG,CAAC,CAAC;AAErB,SAASC,SAASA,CAACxE,SAAsB,EAAEiB,MAAc,EAAE0B,KAAc,EAAE;EACzE,MAAMrB,KAAK,GAAGrB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9CoB,KAAK,CAACnB,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;EACpCmB,KAAK,CAACZ,SAAS,CAACC,GAAG,CAAE,MAAKM,MAAO,EAAC,CAAC;EACnCK,KAAK,CAACnB,YAAY,CAAC,cAAc,EAAE,OAAO,CAAC;EAC3C,IAAIwC,KAAK,IAAI,IAAI,EAAE;IACjBrB,KAAK,CAACqB,KAAK,GAAGA,KAAK;IACnBrB,KAAK,CAACnB,YAAY,CAAC,YAAY,EAAG,GAAEc,MAAO,KAAI0B,KAAM,EAAC,CAAC;EACzD,CAAC,MAAM;IACLrB,KAAK,CAACnB,YAAY,CAAC,YAAY,EAAEc,MAAM,CAAC;EAC1C;EACAjB,SAAS,CAACyE,WAAW,CAACnD,KAAK,CAAC;AAC9B;AAEA,SAASlB,WAAWA,CAClBJ,SAAsB,EACtB0E,MAEwC,EACxC;EACA,IAAI,CAAC5E,KAAK,CAACC,OAAO,CAAC2E,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;IAC7B;IACAA,MAAM,GAAG,CAACA,MAAM,CAAC;EACnB;EACAA,MAAM,CAAC1D,OAAO,CAAEJ,QAAa,IAAK;IAChC,MAAM+D,KAAK,GAAG1E,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC5CyE,KAAK,CAACjE,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IACjCC,QAAQ,CAACI,OAAO,CAAE4D,OAAY,IAAK;MACjC,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/BJ,SAAS,CAACG,KAAK,EAAEC,OAAO,CAAC;MAC3B,CAAC,MAAM;QACL,MAAM3D,MAAM,GAAGH,MAAM,CAACC,IAAI,CAAC6D,OAAO,CAAC,CAAC,CAAC,CAAC;QACtC,MAAMjC,KAAK,GAAGiC,OAAO,CAAC3D,MAAM,CAAC;QAC7B,IAAInB,KAAK,CAACC,OAAO,CAAC4C,KAAK,CAAC,EAAE;UACxBkC,SAAS,CAACF,KAAK,EAAE1D,MAAM,EAAE0B,KAAK,CAAC;QACjC,CAAC,MAAM;UACL6B,SAAS,CAACG,KAAK,EAAE1D,MAAM,EAAE0B,KAAK,CAAC;QACjC;MACF;IACF,CAAC,CAAC;IACF3C,SAAS,CAACyE,WAAW,CAACE,KAAK,CAAC;EAC9B,CAAC,CAAC;AACJ;AAEA,SAASE,SAASA,CAChB7E,SAAsB,EACtBiB,MAAc,EACd6D,MAA+B,EAC/B;EACA,MAAMxD,KAAK,GAAGrB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC9CoB,KAAK,CAACZ,SAAS,CAACC,GAAG,CAAE,MAAKM,MAAO,EAAC,CAAC;EACnC6D,MAAM,CAAC9D,OAAO,CAAE2B,KAAK,IAAK;IACxB,MAAMqB,MAAM,GAAG/D,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,IAAIyC,KAAK,KAAK,KAAK,EAAE;MACnBqB,MAAM,CAAC7D,YAAY,CAAC,OAAO,EAAE4E,MAAM,CAACpC,KAAK,CAAC,CAAC;IAC7C,CAAC,MAAM;MACLqB,MAAM,CAAC7D,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC;IAC7C;IACAmB,KAAK,CAACmD,WAAW,CAACT,MAAM,CAAC;EAC3B,CAAC,CAAC;EACFhE,SAAS,CAACyE,WAAW,CAACnD,KAAK,CAAC;AAC9B;AAEA5B,OAAO,CAAC6E,QAAQ,GAAG;EACjBvE,SAAS,EAAE,IAAI;EACfa,QAAQ,EAAE;IACRmE,KAAKA,CAAA,EAAG;MACN,MAAMrD,KAAK,GAAG,IAAI,CAAC/B,KAAK,CAACqF,YAAY,CAAC,CAAC;MACvC,IAAItD,KAAK,IAAI,IAAI,EAAE;MACnB,IAAIA,KAAK,CAACQ,MAAM,KAAK,CAAC,EAAE;QACtB,MAAM0B,OAAO,GAAG,IAAI,CAACjE,KAAK,CAACkE,SAAS,CAAC,CAAC;QACtChD,MAAM,CAACC,IAAI,CAAC8C,OAAO,CAAC,CAAC7C,OAAO,CAAEkE,IAAI,IAAK;UACrC;UACA,IAAI,IAAI,CAACtF,KAAK,CAACyC,MAAM,CAACC,KAAK,CAAC4C,IAAI,EAAE7F,KAAK,CAAC8F,MAAM,CAAC,IAAI,IAAI,EAAE;YACvD,IAAI,CAACvF,KAAK,CAACqB,MAAM,CAACiE,IAAI,EAAE,KAAK,EAAE5F,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;UACpD;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAI,CAAC/D,KAAK,CAACwF,YAAY,CAACzD,KAAK,CAAC4B,KAAK,EAAE5B,KAAK,CAACQ,MAAM,EAAE7C,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;MACxE;IACF,CAAC;IACD0B,SAASA,CAAC1C,KAAK,EAAE;MACf,MAAM;QAAE2C;MAAM,CAAC,GAAG,IAAI,CAAC1F,KAAK,CAACkE,SAAS,CAAC,CAAC;MACxC,IAAInB,KAAK,KAAK,KAAK,IAAI2C,KAAK,IAAI,IAAI,EAAE;QACpC,IAAI,CAAC1F,KAAK,CAACqB,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE3B,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;MACzD,CAAC,MAAM,IAAI,CAAChB,KAAK,IAAI2C,KAAK,KAAK,OAAO,EAAE;QACtC,IAAI,CAAC1F,KAAK,CAACqB,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE3B,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;MACvD;MACA,IAAI,CAAC/D,KAAK,CAACqB,MAAM,CAAC,WAAW,EAAE0B,KAAK,EAAErD,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;IAC3D,CAAC;IACD4B,MAAMA,CAAC5C,KAAK,EAAE;MACZ,MAAMhB,KAAK,GAAG,IAAI,CAAC/B,KAAK,CAACqF,YAAY,CAAC,CAAC;MACvC;MACA,MAAMpB,OAAO,GAAG,IAAI,CAACjE,KAAK,CAACkE,SAAS,CAACnC,KAAK,CAAC;MAC3C;MACA,MAAM4D,MAAM,GAAGC,QAAQ,CAAC3B,OAAO,CAAC0B,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC;MAChD,IAAI5C,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;QACpC,IAAI8C,QAAQ,GAAG9C,KAAK,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACtC,IAAIkB,OAAO,CAACwB,SAAS,KAAK,KAAK,EAAEI,QAAQ,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC7F,KAAK,CAACqB,MAAM,CAAC,QAAQ,EAAEsE,MAAM,GAAGE,QAAQ,EAAEnG,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;MACpE;IACF,CAAC;IACD+B,IAAIA,CAAC/C,KAAK,EAAE;MACV,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,GAAGS,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC;MACrC;MACA,IAAI,CAACxD,KAAK,CAACqB,MAAM,CAAC,MAAM,EAAE0B,KAAK,EAAErD,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;IACtD,CAAC;IACDgC,IAAIA,CAAChD,KAAK,EAAE;MACV,MAAMhB,KAAK,GAAG,IAAI,CAAC/B,KAAK,CAACqF,YAAY,CAAC,CAAC;MACvC;MACA,MAAMpB,OAAO,GAAG,IAAI,CAACjE,KAAK,CAACkE,SAAS,CAACnC,KAAK,CAAC;MAC3C,IAAIgB,KAAK,KAAK,OAAO,EAAE;QACrB,IAAIkB,OAAO,CAAC8B,IAAI,KAAK,SAAS,IAAI9B,OAAO,CAAC8B,IAAI,KAAK,WAAW,EAAE;UAC9D,IAAI,CAAC/F,KAAK,CAACqB,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE3B,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;QACtD,CAAC,MAAM;UACL,IAAI,CAAC/D,KAAK,CAACqB,MAAM,CAAC,MAAM,EAAE,WAAW,EAAE3B,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;QAC5D;MACF,CAAC,MAAM;QACL,IAAI,CAAC/D,KAAK,CAACqB,MAAM,CAAC,MAAM,EAAE0B,KAAK,EAAErD,KAAK,CAACoE,OAAO,CAACC,IAAI,CAAC;MACtD;IACF;EACF;AACF,CAAC;AAED,SAASjE,OAAO,IAAIkG,OAAO,EAAExF,WAAW", "ignoreList": []}