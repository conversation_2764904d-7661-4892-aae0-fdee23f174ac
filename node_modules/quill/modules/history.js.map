{"version": 3, "file": "history.js", "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "History", "DEFAULTS", "delay", "maxStack", "userOnly", "lastRecorded", "ignoreChange", "stack", "undo", "redo", "currentRange", "constructor", "quill", "options", "on", "events", "EDITOR_CHANGE", "eventName", "value", "oldValue", "source", "SELECTION_CHANGE", "sources", "SILENT", "TEXT_CHANGE", "USER", "record", "transform", "transformRange", "keyboard", "addBinding", "key", "<PERSON><PERSON><PERSON>", "bind", "shift<PERSON>ey", "test", "navigator", "platform", "root", "addEventListener", "event", "inputType", "preventDefault", "change", "dest", "length", "item", "pop", "base", "getContents", "inverse<PERSON><PERSON><PERSON>", "delta", "invert", "push", "range", "updateContents", "restoreSelection", "clear", "cutoff", "change<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ops", "undoDelta", "undoRange", "timestamp", "Date", "now", "compose", "shift", "transformStack", "stackItem", "setSelection", "index", "getLastChangeIndex", "scroll", "remoteDelta", "i", "oldItem", "splice", "endsWithNewlineChange", "lastOp", "insert", "endsWith", "attributes", "Object", "keys", "some", "attr", "query", "BLOCK", "deleteLength", "reduce", "op", "delete", "changeIndex", "start", "transformPosition", "end", "default"], "sources": ["../../src/modules/history.ts"], "sourcesContent": ["import { Scope } from 'parchment';\nimport type Delta from 'quill-delta';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type Scroll from '../blots/scroll.js';\nimport type { Range } from '../core/selection.js';\n\nexport interface HistoryOptions {\n  userOnly: boolean;\n  delay: number;\n  maxStack: number;\n}\n\nexport interface StackItem {\n  delta: Delta;\n  range: Range | null;\n}\n\ninterface Stack {\n  undo: StackItem[];\n  redo: StackItem[];\n}\n\nclass History extends Module<HistoryOptions> {\n  static DEFAULTS: HistoryOptions = {\n    delay: 1000,\n    maxStack: 100,\n    userOnly: false,\n  };\n\n  lastRecorded = 0;\n  ignoreChange = false;\n  stack: Stack = { undo: [], redo: [] };\n  currentRange: Range | null = null;\n\n  constructor(quill: Quill, options: Partial<HistoryOptions>) {\n    super(quill, options);\n    this.quill.on(\n      Quill.events.EDITOR_CHANGE,\n      (eventName, value, oldValue, source) => {\n        if (eventName === Quill.events.SELECTION_CHANGE) {\n          if (value && source !== Quill.sources.SILENT) {\n            this.currentRange = value;\n          }\n        } else if (eventName === Quill.events.TEXT_CHANGE) {\n          if (!this.ignoreChange) {\n            if (!this.options.userOnly || source === Quill.sources.USER) {\n              this.record(value, oldValue);\n            } else {\n              this.transform(value);\n            }\n          }\n\n          this.currentRange = transformRange(this.currentRange, value);\n        }\n      },\n    );\n\n    this.quill.keyboard.addBinding(\n      { key: 'z', shortKey: true },\n      this.undo.bind(this),\n    );\n    this.quill.keyboard.addBinding(\n      { key: ['z', 'Z'], shortKey: true, shiftKey: true },\n      this.redo.bind(this),\n    );\n    if (/Win/i.test(navigator.platform)) {\n      this.quill.keyboard.addBinding(\n        { key: 'y', shortKey: true },\n        this.redo.bind(this),\n      );\n    }\n\n    this.quill.root.addEventListener('beforeinput', (event) => {\n      if (event.inputType === 'historyUndo') {\n        this.undo();\n        event.preventDefault();\n      } else if (event.inputType === 'historyRedo') {\n        this.redo();\n        event.preventDefault();\n      }\n    });\n  }\n\n  change(source: 'undo' | 'redo', dest: 'redo' | 'undo') {\n    if (this.stack[source].length === 0) return;\n    const item = this.stack[source].pop();\n    if (!item) return;\n    const base = this.quill.getContents();\n    const inverseDelta = item.delta.invert(base);\n    this.stack[dest].push({\n      delta: inverseDelta,\n      range: transformRange(item.range, inverseDelta),\n    });\n    this.lastRecorded = 0;\n    this.ignoreChange = true;\n    this.quill.updateContents(item.delta, Quill.sources.USER);\n    this.ignoreChange = false;\n\n    this.restoreSelection(item);\n  }\n\n  clear() {\n    this.stack = { undo: [], redo: [] };\n  }\n\n  cutoff() {\n    this.lastRecorded = 0;\n  }\n\n  record(changeDelta: Delta, oldDelta: Delta) {\n    if (changeDelta.ops.length === 0) return;\n    this.stack.redo = [];\n    let undoDelta = changeDelta.invert(oldDelta);\n    let undoRange = this.currentRange;\n    const timestamp = Date.now();\n    if (\n      // @ts-expect-error Fix me later\n      this.lastRecorded + this.options.delay > timestamp &&\n      this.stack.undo.length > 0\n    ) {\n      const item = this.stack.undo.pop();\n      if (item) {\n        undoDelta = undoDelta.compose(item.delta);\n        undoRange = item.range;\n      }\n    } else {\n      this.lastRecorded = timestamp;\n    }\n    if (undoDelta.length() === 0) return;\n    this.stack.undo.push({ delta: undoDelta, range: undoRange });\n    // @ts-expect-error Fix me later\n    if (this.stack.undo.length > this.options.maxStack) {\n      this.stack.undo.shift();\n    }\n  }\n\n  redo() {\n    this.change('redo', 'undo');\n  }\n\n  transform(delta: Delta) {\n    transformStack(this.stack.undo, delta);\n    transformStack(this.stack.redo, delta);\n  }\n\n  undo() {\n    this.change('undo', 'redo');\n  }\n\n  protected restoreSelection(stackItem: StackItem) {\n    if (stackItem.range) {\n      this.quill.setSelection(stackItem.range, Quill.sources.USER);\n    } else {\n      const index = getLastChangeIndex(this.quill.scroll, stackItem.delta);\n      this.quill.setSelection(index, Quill.sources.USER);\n    }\n  }\n}\n\nfunction transformStack(stack: StackItem[], delta: Delta) {\n  let remoteDelta = delta;\n  for (let i = stack.length - 1; i >= 0; i -= 1) {\n    const oldItem = stack[i];\n    stack[i] = {\n      delta: remoteDelta.transform(oldItem.delta, true),\n      range: oldItem.range && transformRange(oldItem.range, remoteDelta),\n    };\n    remoteDelta = oldItem.delta.transform(remoteDelta);\n    if (stack[i].delta.length() === 0) {\n      stack.splice(i, 1);\n    }\n  }\n}\n\nfunction endsWithNewlineChange(scroll: Scroll, delta: Delta) {\n  const lastOp = delta.ops[delta.ops.length - 1];\n  if (lastOp == null) return false;\n  if (lastOp.insert != null) {\n    return typeof lastOp.insert === 'string' && lastOp.insert.endsWith('\\n');\n  }\n  if (lastOp.attributes != null) {\n    return Object.keys(lastOp.attributes).some((attr) => {\n      return scroll.query(attr, Scope.BLOCK) != null;\n    });\n  }\n  return false;\n}\n\nfunction getLastChangeIndex(scroll: Scroll, delta: Delta) {\n  const deleteLength = delta.reduce((length, op) => {\n    return length + (op.delete || 0);\n  }, 0);\n  let changeIndex = delta.length() - deleteLength;\n  if (endsWithNewlineChange(scroll, delta)) {\n    changeIndex -= 1;\n  }\n  return changeIndex;\n}\n\nfunction transformRange(range: Range | null, delta: Delta) {\n  if (!range) return range;\n  const start = delta.transformPosition(range.index);\n  const end = delta.transformPosition(range.index + range.length);\n  return { index: start, length: end - start };\n}\n\nexport { History as default, getLastChangeIndex };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AAEjC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AAoBpC,MAAMC,OAAO,SAASF,MAAM,CAAiB;EAC3C,OAAOG,QAAQ,GAAmB;IAChCC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,GAAG;IACbC,QAAQ,EAAE;EACZ,CAAC;EAEDC,YAAY,GAAG,CAAC;EAChBC,YAAY,GAAG,KAAK;EACpBC,KAAK,GAAU;IAAEC,IAAI,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC;EACrCC,YAAY,GAAiB,IAAI;EAEjCC,WAAWA,CAACC,KAAY,EAAEC,OAAgC,EAAE;IAC1D,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAI,CAACD,KAAK,CAACE,EAAE,CACXf,KAAK,CAACgB,MAAM,CAACC,aAAa,EAC1B,CAACC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,KAAK;MACtC,IAAIH,SAAS,KAAKlB,KAAK,CAACgB,MAAM,CAACM,gBAAgB,EAAE;QAC/C,IAAIH,KAAK,IAAIE,MAAM,KAAKrB,KAAK,CAACuB,OAAO,CAACC,MAAM,EAAE;UAC5C,IAAI,CAACb,YAAY,GAAGQ,KAAK;QAC3B;MACF,CAAC,MAAM,IAAID,SAAS,KAAKlB,KAAK,CAACgB,MAAM,CAACS,WAAW,EAAE;QACjD,IAAI,CAAC,IAAI,CAAClB,YAAY,EAAE;UACtB,IAAI,CAAC,IAAI,CAACO,OAAO,CAACT,QAAQ,IAAIgB,MAAM,KAAKrB,KAAK,CAACuB,OAAO,CAACG,IAAI,EAAE;YAC3D,IAAI,CAACC,MAAM,CAACR,KAAK,EAAEC,QAAQ,CAAC;UAC9B,CAAC,MAAM;YACL,IAAI,CAACQ,SAAS,CAACT,KAAK,CAAC;UACvB;QACF;QAEA,IAAI,CAACR,YAAY,GAAGkB,cAAc,CAAC,IAAI,CAAClB,YAAY,EAAEQ,KAAK,CAAC;MAC9D;IACF,CACF,CAAC;IAED,IAAI,CAACN,KAAK,CAACiB,QAAQ,CAACC,UAAU,CAC5B;MAAEC,GAAG,EAAE,GAAG;MAAEC,QAAQ,EAAE;IAAK,CAAC,EAC5B,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAC,IAAI,CACrB,CAAC;IACD,IAAI,CAACrB,KAAK,CAACiB,QAAQ,CAACC,UAAU,CAC5B;MAAEC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;MAAEC,QAAQ,EAAE,IAAI;MAAEE,QAAQ,EAAE;IAAK,CAAC,EACnD,IAAI,CAACzB,IAAI,CAACwB,IAAI,CAAC,IAAI,CACrB,CAAC;IACD,IAAI,MAAM,CAACE,IAAI,CAACC,SAAS,CAACC,QAAQ,CAAC,EAAE;MACnC,IAAI,CAACzB,KAAK,CAACiB,QAAQ,CAACC,UAAU,CAC5B;QAAEC,GAAG,EAAE,GAAG;QAAEC,QAAQ,EAAE;MAAK,CAAC,EAC5B,IAAI,CAACvB,IAAI,CAACwB,IAAI,CAAC,IAAI,CACrB,CAAC;IACH;IAEA,IAAI,CAACrB,KAAK,CAAC0B,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAGC,KAAK,IAAK;MACzD,IAAIA,KAAK,CAACC,SAAS,KAAK,aAAa,EAAE;QACrC,IAAI,CAACjC,IAAI,CAAC,CAAC;QACXgC,KAAK,CAACE,cAAc,CAAC,CAAC;MACxB,CAAC,MAAM,IAAIF,KAAK,CAACC,SAAS,KAAK,aAAa,EAAE;QAC5C,IAAI,CAAChC,IAAI,CAAC,CAAC;QACX+B,KAAK,CAACE,cAAc,CAAC,CAAC;MACxB;IACF,CAAC,CAAC;EACJ;EAEAC,MAAMA,CAACvB,MAAuB,EAAEwB,IAAqB,EAAE;IACrD,IAAI,IAAI,CAACrC,KAAK,CAACa,MAAM,CAAC,CAACyB,MAAM,KAAK,CAAC,EAAE;IACrC,MAAMC,IAAI,GAAG,IAAI,CAACvC,KAAK,CAACa,MAAM,CAAC,CAAC2B,GAAG,CAAC,CAAC;IACrC,IAAI,CAACD,IAAI,EAAE;IACX,MAAME,IAAI,GAAG,IAAI,CAACpC,KAAK,CAACqC,WAAW,CAAC,CAAC;IACrC,MAAMC,YAAY,GAAGJ,IAAI,CAACK,KAAK,CAACC,MAAM,CAACJ,IAAI,CAAC;IAC5C,IAAI,CAACzC,KAAK,CAACqC,IAAI,CAAC,CAACS,IAAI,CAAC;MACpBF,KAAK,EAAED,YAAY;MACnBI,KAAK,EAAE1B,cAAc,CAACkB,IAAI,CAACQ,KAAK,EAAEJ,YAAY;IAChD,CAAC,CAAC;IACF,IAAI,CAAC7C,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACM,KAAK,CAAC2C,cAAc,CAACT,IAAI,CAACK,KAAK,EAAEpD,KAAK,CAACuB,OAAO,CAACG,IAAI,CAAC;IACzD,IAAI,CAACnB,YAAY,GAAG,KAAK;IAEzB,IAAI,CAACkD,gBAAgB,CAACV,IAAI,CAAC;EAC7B;EAEAW,KAAKA,CAAA,EAAG;IACN,IAAI,CAAClD,KAAK,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAG,CAAC;EACrC;EAEAiD,MAAMA,CAAA,EAAG;IACP,IAAI,CAACrD,YAAY,GAAG,CAAC;EACvB;EAEAqB,MAAMA,CAACiC,WAAkB,EAAEC,QAAe,EAAE;IAC1C,IAAID,WAAW,CAACE,GAAG,CAAChB,MAAM,KAAK,CAAC,EAAE;IAClC,IAAI,CAACtC,KAAK,CAACE,IAAI,GAAG,EAAE;IACpB,IAAIqD,SAAS,GAAGH,WAAW,CAACP,MAAM,CAACQ,QAAQ,CAAC;IAC5C,IAAIG,SAAS,GAAG,IAAI,CAACrD,YAAY;IACjC,MAAMsD,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B;IACE;IACA,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAACQ,OAAO,CAACX,KAAK,GAAG8D,SAAS,IAClD,IAAI,CAACzD,KAAK,CAACC,IAAI,CAACqC,MAAM,GAAG,CAAC,EAC1B;MACA,MAAMC,IAAI,GAAG,IAAI,CAACvC,KAAK,CAACC,IAAI,CAACuC,GAAG,CAAC,CAAC;MAClC,IAAID,IAAI,EAAE;QACRgB,SAAS,GAAGA,SAAS,CAACK,OAAO,CAACrB,IAAI,CAACK,KAAK,CAAC;QACzCY,SAAS,GAAGjB,IAAI,CAACQ,KAAK;MACxB;IACF,CAAC,MAAM;MACL,IAAI,CAACjD,YAAY,GAAG2D,SAAS;IAC/B;IACA,IAAIF,SAAS,CAACjB,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;IAC9B,IAAI,CAACtC,KAAK,CAACC,IAAI,CAAC6C,IAAI,CAAC;MAAEF,KAAK,EAAEW,SAAS;MAAER,KAAK,EAAES;IAAU,CAAC,CAAC;IAC5D;IACA,IAAI,IAAI,CAACxD,KAAK,CAACC,IAAI,CAACqC,MAAM,GAAG,IAAI,CAAChC,OAAO,CAACV,QAAQ,EAAE;MAClD,IAAI,CAACI,KAAK,CAACC,IAAI,CAAC4D,KAAK,CAAC,CAAC;IACzB;EACF;EAEA3D,IAAIA,CAAA,EAAG;IACL,IAAI,CAACkC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7B;EAEAhB,SAASA,CAACwB,KAAY,EAAE;IACtBkB,cAAc,CAAC,IAAI,CAAC9D,KAAK,CAACC,IAAI,EAAE2C,KAAK,CAAC;IACtCkB,cAAc,CAAC,IAAI,CAAC9D,KAAK,CAACE,IAAI,EAAE0C,KAAK,CAAC;EACxC;EAEA3C,IAAIA,CAAA,EAAG;IACL,IAAI,CAACmC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;EAC7B;EAEUa,gBAAgBA,CAACc,SAAoB,EAAE;IAC/C,IAAIA,SAAS,CAAChB,KAAK,EAAE;MACnB,IAAI,CAAC1C,KAAK,CAAC2D,YAAY,CAACD,SAAS,CAAChB,KAAK,EAAEvD,KAAK,CAACuB,OAAO,CAACG,IAAI,CAAC;IAC9D,CAAC,MAAM;MACL,MAAM+C,KAAK,GAAGC,kBAAkB,CAAC,IAAI,CAAC7D,KAAK,CAAC8D,MAAM,EAAEJ,SAAS,CAACnB,KAAK,CAAC;MACpE,IAAI,CAACvC,KAAK,CAAC2D,YAAY,CAACC,KAAK,EAAEzE,KAAK,CAACuB,OAAO,CAACG,IAAI,CAAC;IACpD;EACF;AACF;AAEA,SAAS4C,cAAcA,CAAC9D,KAAkB,EAAE4C,KAAY,EAAE;EACxD,IAAIwB,WAAW,GAAGxB,KAAK;EACvB,KAAK,IAAIyB,CAAC,GAAGrE,KAAK,CAACsC,MAAM,GAAG,CAAC,EAAE+B,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IAC7C,MAAMC,OAAO,GAAGtE,KAAK,CAACqE,CAAC,CAAC;IACxBrE,KAAK,CAACqE,CAAC,CAAC,GAAG;MACTzB,KAAK,EAAEwB,WAAW,CAAChD,SAAS,CAACkD,OAAO,CAAC1B,KAAK,EAAE,IAAI,CAAC;MACjDG,KAAK,EAAEuB,OAAO,CAACvB,KAAK,IAAI1B,cAAc,CAACiD,OAAO,CAACvB,KAAK,EAAEqB,WAAW;IACnE,CAAC;IACDA,WAAW,GAAGE,OAAO,CAAC1B,KAAK,CAACxB,SAAS,CAACgD,WAAW,CAAC;IAClD,IAAIpE,KAAK,CAACqE,CAAC,CAAC,CAACzB,KAAK,CAACN,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE;MACjCtC,KAAK,CAACuE,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;IACpB;EACF;AACF;AAEA,SAASG,qBAAqBA,CAACL,MAAc,EAAEvB,KAAY,EAAE;EAC3D,MAAM6B,MAAM,GAAG7B,KAAK,CAACU,GAAG,CAACV,KAAK,CAACU,GAAG,CAAChB,MAAM,GAAG,CAAC,CAAC;EAC9C,IAAImC,MAAM,IAAI,IAAI,EAAE,OAAO,KAAK;EAChC,IAAIA,MAAM,CAACC,MAAM,IAAI,IAAI,EAAE;IACzB,OAAO,OAAOD,MAAM,CAACC,MAAM,KAAK,QAAQ,IAAID,MAAM,CAACC,MAAM,CAACC,QAAQ,CAAC,IAAI,CAAC;EAC1E;EACA,IAAIF,MAAM,CAACG,UAAU,IAAI,IAAI,EAAE;IAC7B,OAAOC,MAAM,CAACC,IAAI,CAACL,MAAM,CAACG,UAAU,CAAC,CAACG,IAAI,CAAEC,IAAI,IAAK;MACnD,OAAOb,MAAM,CAACc,KAAK,CAACD,IAAI,EAAE1F,KAAK,CAAC4F,KAAK,CAAC,IAAI,IAAI;IAChD,CAAC,CAAC;EACJ;EACA,OAAO,KAAK;AACd;AAEA,SAAShB,kBAAkBA,CAACC,MAAc,EAAEvB,KAAY,EAAE;EACxD,MAAMuC,YAAY,GAAGvC,KAAK,CAACwC,MAAM,CAAC,CAAC9C,MAAM,EAAE+C,EAAE,KAAK;IAChD,OAAO/C,MAAM,IAAI+C,EAAE,CAACC,MAAM,IAAI,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC,CAAC;EACL,IAAIC,WAAW,GAAG3C,KAAK,CAACN,MAAM,CAAC,CAAC,GAAG6C,YAAY;EAC/C,IAAIX,qBAAqB,CAACL,MAAM,EAAEvB,KAAK,CAAC,EAAE;IACxC2C,WAAW,IAAI,CAAC;EAClB;EACA,OAAOA,WAAW;AACpB;AAEA,SAASlE,cAAcA,CAAC0B,KAAmB,EAAEH,KAAY,EAAE;EACzD,IAAI,CAACG,KAAK,EAAE,OAAOA,KAAK;EACxB,MAAMyC,KAAK,GAAG5C,KAAK,CAAC6C,iBAAiB,CAAC1C,KAAK,CAACkB,KAAK,CAAC;EAClD,MAAMyB,GAAG,GAAG9C,KAAK,CAAC6C,iBAAiB,CAAC1C,KAAK,CAACkB,KAAK,GAAGlB,KAAK,CAACT,MAAM,CAAC;EAC/D,OAAO;IAAE2B,KAAK,EAAEuB,KAAK;IAAElD,MAAM,EAAEoD,GAAG,GAAGF;EAAM,CAAC;AAC9C;AAEA,SAAS/F,OAAO,IAAIkG,OAAO,EAAEzB,kBAAkB", "ignoreList": []}