{"version": 3, "file": "clipboard.js", "names": ["Attributor", "BlockBlot", "ClassAttributor", "EmbedBlot", "<PERSON><PERSON>", "StyleAttributor", "Delta", "BlockEmbed", "logger", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "AlignAttribute", "AlignStyle", "BackgroundStyle", "CodeBlock", "ColorStyle", "DirectionAttribute", "DirectionStyle", "FontStyle", "SizeStyle", "deleteRange", "normalizeExternalHTML", "debug", "CLIPBOARD_CONFIG", "Node", "TEXT_NODE", "matchText", "matchNewline", "matchBreak", "ELEMENT_NODE", "matchBlot", "matchAttributor", "matchStyles", "matchIndent", "matchList", "matchCodeBlock", "matchTable", "createMatchAlias", "matchIgnore", "ATTRIBUTE_ATTRIBUTORS", "reduce", "memo", "attr", "keyName", "STYLE_ATTRIBUTORS", "Clipboard", "DEFAULTS", "matchers", "constructor", "quill", "options", "root", "addEventListener", "e", "onCaptureCopy", "onCapturePaste", "bind", "concat", "for<PERSON>ach", "_ref", "selector", "matcher", "addMatcher", "push", "convert", "_ref2", "html", "text", "formats", "arguments", "length", "undefined", "blotName", "insert", "delta", "convertHTML", "deltaEndsWith", "ops", "attributes", "table", "compose", "retain", "delete", "normalizeHTML", "doc", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "container", "body", "nodeMatches", "WeakMap", "elementMatchers", "textMatchers", "prepareMatching", "traverse", "scroll", "dangerouslyPasteHTML", "index", "source", "sources", "API", "setContents", "setSelection", "SILENT", "paste", "updateContents", "isCut", "defaultPrevented", "preventDefault", "range", "selection", "getRange", "onCopy", "clipboardData", "setData", "normalizeURIList", "urlList", "split", "filter", "url", "join", "isEnabled", "getSelection", "getData", "files", "Array", "from", "uploader", "upload", "childElementCount", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "tagName", "onPaste", "getText", "getSemanticHTML", "_ref3", "getFormat", "pastedDelta", "log", "USER", "scrollSelectionIntoView", "pair", "querySelectorAll", "node", "has", "matches", "get", "set", "applyFormat", "format", "value", "query", "newDelta", "op", "endText", "i", "slice", "isLine", "Element", "match", "prototype", "includes", "toLowerCase", "isBetweenInlineElements", "previousElementSibling", "nextElement<PERSON><PERSON>ling", "preNodes", "isPre", "parentNode", "nodeType", "childNodes", "childNode", "<PERSON><PERSON><PERSON><PERSON>", "reducedDelta", "_node", "keys", "classes", "styles", "name", "ATTRIBUTE", "attrName", "Object", "entries", "_ref4", "embed", "language", "indent", "parent", "composed", "element", "list", "checkedAttr", "getAttribute", "HTMLParagraphElement", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "style", "fontStyle", "italic", "textDecoration", "underline", "strike", "fontWeight", "startsWith", "parseInt", "bold", "_ref5", "parseFloat", "textIndent", "parentElement", "rows", "row", "indexOf", "data", "trim", "replace", "previousSibling", "replaceAll", "default"], "sources": ["../../src/modules/clipboard.ts"], "sourcesContent": ["import type { ScrollBlot } from 'parchment';\nimport {\n  Attributor,\n  BlockBlot,\n  ClassAttributor,\n  EmbedB<PERSON>,\n  Scope,\n  StyleAttributor,\n} from 'parchment';\nimport Delta from 'quill-delta';\nimport { BlockEmbed } from '../blots/block.js';\nimport type { EmitterSource } from '../core/emitter.js';\nimport logger from '../core/logger.js';\nimport Module from '../core/module.js';\nimport Quill from '../core/quill.js';\nimport type { Range } from '../core/selection.js';\nimport { AlignAttribute, AlignStyle } from '../formats/align.js';\nimport { BackgroundStyle } from '../formats/background.js';\nimport CodeBlock from '../formats/code.js';\nimport { ColorStyle } from '../formats/color.js';\nimport { DirectionAttribute, DirectionStyle } from '../formats/direction.js';\nimport { FontStyle } from '../formats/font.js';\nimport { SizeStyle } from '../formats/size.js';\nimport { deleteRange } from './keyboard.js';\nimport normalizeExternalHTML from './normalizeExternalHTML/index.js';\n\nconst debug = logger('quill:clipboard');\n\ntype Selector = string | Node['TEXT_NODE'] | Node['ELEMENT_NODE'];\ntype Matcher = (node: Node, delta: Delta, scroll: ScrollBlot) => Delta;\n\nconst CLIPBOARD_CONFIG: [Selector, Matcher][] = [\n  [Node.TEXT_NODE, matchText],\n  [Node.TEXT_NODE, matchNewline],\n  ['br', matchBreak],\n  [Node.ELEMENT_NODE, matchNewline],\n  [Node.ELEMENT_NODE, matchBlot],\n  [Node.ELEMENT_NODE, matchAttributor],\n  [Node.ELEMENT_NODE, matchStyles],\n  ['li', matchIndent],\n  ['ol, ul', matchList],\n  ['pre', matchCodeBlock],\n  ['tr', matchTable],\n  ['b', createMatchAlias('bold')],\n  ['i', createMatchAlias('italic')],\n  ['strike', createMatchAlias('strike')],\n  ['style', matchIgnore],\n];\n\nconst ATTRIBUTE_ATTRIBUTORS = [AlignAttribute, DirectionAttribute].reduce(\n  (memo: Record<string, Attributor>, attr) => {\n    memo[attr.keyName] = attr;\n    return memo;\n  },\n  {},\n);\n\nconst STYLE_ATTRIBUTORS = [\n  AlignStyle,\n  BackgroundStyle,\n  ColorStyle,\n  DirectionStyle,\n  FontStyle,\n  SizeStyle,\n].reduce((memo: Record<string, Attributor>, attr) => {\n  memo[attr.keyName] = attr;\n  return memo;\n}, {});\n\ninterface ClipboardOptions {\n  matchers: [Selector, Matcher][];\n}\n\nclass Clipboard extends Module<ClipboardOptions> {\n  static DEFAULTS: ClipboardOptions = {\n    matchers: [],\n  };\n\n  matchers: [Selector, Matcher][];\n\n  constructor(quill: Quill, options: Partial<ClipboardOptions>) {\n    super(quill, options);\n    this.quill.root.addEventListener('copy', (e) =>\n      this.onCaptureCopy(e, false),\n    );\n    this.quill.root.addEventListener('cut', (e) => this.onCaptureCopy(e, true));\n    this.quill.root.addEventListener('paste', this.onCapturePaste.bind(this));\n    this.matchers = [];\n    CLIPBOARD_CONFIG.concat(this.options.matchers ?? []).forEach(\n      ([selector, matcher]) => {\n        this.addMatcher(selector, matcher);\n      },\n    );\n  }\n\n  addMatcher(selector: Selector, matcher: Matcher) {\n    this.matchers.push([selector, matcher]);\n  }\n\n  convert(\n    { html, text }: { html?: string; text?: string },\n    formats: Record<string, unknown> = {},\n  ) {\n    if (formats[CodeBlock.blotName]) {\n      return new Delta().insert(text || '', {\n        [CodeBlock.blotName]: formats[CodeBlock.blotName],\n      });\n    }\n    if (!html) {\n      return new Delta().insert(text || '', formats);\n    }\n    const delta = this.convertHTML(html);\n    // Remove trailing newline\n    if (\n      deltaEndsWith(delta, '\\n') &&\n      (delta.ops[delta.ops.length - 1].attributes == null || formats.table)\n    ) {\n      return delta.compose(new Delta().retain(delta.length() - 1).delete(1));\n    }\n    return delta;\n  }\n\n  protected normalizeHTML(doc: Document) {\n    normalizeExternalHTML(doc);\n  }\n\n  protected convertHTML(html: string) {\n    const doc = new DOMParser().parseFromString(html, 'text/html');\n    this.normalizeHTML(doc);\n    const container = doc.body;\n    const nodeMatches = new WeakMap();\n    const [elementMatchers, textMatchers] = this.prepareMatching(\n      container,\n      nodeMatches,\n    );\n    return traverse(\n      this.quill.scroll,\n      container,\n      elementMatchers,\n      textMatchers,\n      nodeMatches,\n    );\n  }\n\n  dangerouslyPasteHTML(html: string, source?: EmitterSource): void;\n  dangerouslyPasteHTML(\n    index: number,\n    html: string,\n    source?: EmitterSource,\n  ): void;\n  dangerouslyPasteHTML(\n    index: number | string,\n    html?: string,\n    source: EmitterSource = Quill.sources.API,\n  ) {\n    if (typeof index === 'string') {\n      const delta = this.convert({ html: index, text: '' });\n      // @ts-expect-error\n      this.quill.setContents(delta, html);\n      this.quill.setSelection(0, Quill.sources.SILENT);\n    } else {\n      const paste = this.convert({ html, text: '' });\n      this.quill.updateContents(\n        new Delta().retain(index).concat(paste),\n        source,\n      );\n      this.quill.setSelection(index + paste.length(), Quill.sources.SILENT);\n    }\n  }\n\n  onCaptureCopy(e: ClipboardEvent, isCut = false) {\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    const [range] = this.quill.selection.getRange();\n    if (range == null) return;\n    const { html, text } = this.onCopy(range, isCut);\n    e.clipboardData?.setData('text/plain', text);\n    e.clipboardData?.setData('text/html', html);\n    if (isCut) {\n      deleteRange({ range, quill: this.quill });\n    }\n  }\n\n  /*\n   * https://www.iana.org/assignments/media-types/text/uri-list\n   */\n  private normalizeURIList(urlList: string) {\n    return (\n      urlList\n        .split(/\\r?\\n/)\n        // Ignore all comments\n        .filter((url) => url[0] !== '#')\n        .join('\\n')\n    );\n  }\n\n  onCapturePaste(e: ClipboardEvent) {\n    if (e.defaultPrevented || !this.quill.isEnabled()) return;\n    e.preventDefault();\n    const range = this.quill.getSelection(true);\n    if (range == null) return;\n    const html = e.clipboardData?.getData('text/html');\n    let text = e.clipboardData?.getData('text/plain');\n    if (!html && !text) {\n      const urlList = e.clipboardData?.getData('text/uri-list');\n      if (urlList) {\n        text = this.normalizeURIList(urlList);\n      }\n    }\n    const files = Array.from(e.clipboardData?.files || []);\n    if (!html && files.length > 0) {\n      this.quill.uploader.upload(range, files);\n      return;\n    }\n    if (html && files.length > 0) {\n      const doc = new DOMParser().parseFromString(html, 'text/html');\n      if (\n        doc.body.childElementCount === 1 &&\n        doc.body.firstElementChild?.tagName === 'IMG'\n      ) {\n        this.quill.uploader.upload(range, files);\n        return;\n      }\n    }\n    this.onPaste(range, { html, text });\n  }\n\n  onCopy(range: Range, isCut: boolean): { html: string; text: string };\n  onCopy(range: Range) {\n    const text = this.quill.getText(range);\n    const html = this.quill.getSemanticHTML(range);\n    return { html, text };\n  }\n\n  onPaste(range: Range, { text, html }: { text?: string; html?: string }) {\n    const formats = this.quill.getFormat(range.index);\n    const pastedDelta = this.convert({ text, html }, formats);\n    debug.log('onPaste', pastedDelta, { text, html });\n    const delta = new Delta()\n      .retain(range.index)\n      .delete(range.length)\n      .concat(pastedDelta);\n    this.quill.updateContents(delta, Quill.sources.USER);\n    // range.length contributes to delta.length()\n    this.quill.setSelection(\n      delta.length() - range.length,\n      Quill.sources.SILENT,\n    );\n    this.quill.scrollSelectionIntoView();\n  }\n\n  prepareMatching(container: Element, nodeMatches: WeakMap<Node, Matcher[]>) {\n    const elementMatchers: Matcher[] = [];\n    const textMatchers: Matcher[] = [];\n    this.matchers.forEach((pair) => {\n      const [selector, matcher] = pair;\n      switch (selector) {\n        case Node.TEXT_NODE:\n          textMatchers.push(matcher);\n          break;\n        case Node.ELEMENT_NODE:\n          elementMatchers.push(matcher);\n          break;\n        default:\n          Array.from(container.querySelectorAll(selector)).forEach((node) => {\n            if (nodeMatches.has(node)) {\n              const matches = nodeMatches.get(node);\n              matches?.push(matcher);\n            } else {\n              nodeMatches.set(node, [matcher]);\n            }\n          });\n          break;\n      }\n    });\n    return [elementMatchers, textMatchers];\n  }\n}\n\nfunction applyFormat(\n  delta: Delta,\n  format: string,\n  value: unknown,\n  scroll: ScrollBlot,\n): Delta {\n  if (!scroll.query(format)) {\n    return delta;\n  }\n\n  return delta.reduce((newDelta, op) => {\n    if (!op.insert) return newDelta;\n    if (op.attributes && op.attributes[format]) {\n      return newDelta.push(op);\n    }\n    const formats = value ? { [format]: value } : {};\n    return newDelta.insert(op.insert, { ...formats, ...op.attributes });\n  }, new Delta());\n}\n\nfunction deltaEndsWith(delta: Delta, text: string) {\n  let endText = '';\n  for (\n    let i = delta.ops.length - 1;\n    i >= 0 && endText.length < text.length;\n    --i // eslint-disable-line no-plusplus\n  ) {\n    const op = delta.ops[i];\n    if (typeof op.insert !== 'string') break;\n    endText = op.insert + endText;\n  }\n  return endText.slice(-1 * text.length) === text;\n}\n\nfunction isLine(node: Node, scroll: ScrollBlot) {\n  if (!(node instanceof Element)) return false;\n  const match = scroll.query(node);\n  // @ts-expect-error\n  if (match && match.prototype instanceof EmbedBlot) return false;\n\n  return [\n    'address',\n    'article',\n    'blockquote',\n    'canvas',\n    'dd',\n    'div',\n    'dl',\n    'dt',\n    'fieldset',\n    'figcaption',\n    'figure',\n    'footer',\n    'form',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'header',\n    'iframe',\n    'li',\n    'main',\n    'nav',\n    'ol',\n    'output',\n    'p',\n    'pre',\n    'section',\n    'table',\n    'td',\n    'tr',\n    'ul',\n    'video',\n  ].includes(node.tagName.toLowerCase());\n}\n\nfunction isBetweenInlineElements(node: HTMLElement, scroll: ScrollBlot) {\n  return (\n    node.previousElementSibling &&\n    node.nextElementSibling &&\n    !isLine(node.previousElementSibling, scroll) &&\n    !isLine(node.nextElementSibling, scroll)\n  );\n}\n\nconst preNodes = new WeakMap();\nfunction isPre(node: Node | null) {\n  if (node == null) return false;\n  if (!preNodes.has(node)) {\n    // @ts-expect-error\n    if (node.tagName === 'PRE') {\n      preNodes.set(node, true);\n    } else {\n      preNodes.set(node, isPre(node.parentNode));\n    }\n  }\n  return preNodes.get(node);\n}\n\nfunction traverse(\n  scroll: ScrollBlot,\n  node: ChildNode,\n  elementMatchers: Matcher[],\n  textMatchers: Matcher[],\n  nodeMatches: WeakMap<Node, Matcher[]>,\n): Delta {\n  // Post-order\n  if (node.nodeType === node.TEXT_NODE) {\n    return textMatchers.reduce((delta: Delta, matcher) => {\n      return matcher(node, delta, scroll);\n    }, new Delta());\n  }\n  if (node.nodeType === node.ELEMENT_NODE) {\n    return Array.from(node.childNodes || []).reduce((delta, childNode) => {\n      let childrenDelta = traverse(\n        scroll,\n        childNode,\n        elementMatchers,\n        textMatchers,\n        nodeMatches,\n      );\n      if (childNode.nodeType === node.ELEMENT_NODE) {\n        childrenDelta = elementMatchers.reduce((reducedDelta, matcher) => {\n          return matcher(childNode as HTMLElement, reducedDelta, scroll);\n        }, childrenDelta);\n        childrenDelta = (nodeMatches.get(childNode) || []).reduce(\n          (reducedDelta, matcher) => {\n            return matcher(childNode, reducedDelta, scroll);\n          },\n          childrenDelta,\n        );\n      }\n      return delta.concat(childrenDelta);\n    }, new Delta());\n  }\n  return new Delta();\n}\n\nfunction createMatchAlias(format: string) {\n  return (_node: Element, delta: Delta, scroll: ScrollBlot) => {\n    return applyFormat(delta, format, true, scroll);\n  };\n}\n\nfunction matchAttributor(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  const attributes = Attributor.keys(node);\n  const classes = ClassAttributor.keys(node);\n  const styles = StyleAttributor.keys(node);\n  const formats: Record<string, string | undefined> = {};\n  attributes\n    .concat(classes)\n    .concat(styles)\n    .forEach((name) => {\n      let attr = scroll.query(name, Scope.ATTRIBUTE) as Attributor;\n      if (attr != null) {\n        formats[attr.attrName] = attr.value(node);\n        if (formats[attr.attrName]) return;\n      }\n      attr = ATTRIBUTE_ATTRIBUTORS[name];\n      if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n        formats[attr.attrName] = attr.value(node) || undefined;\n      }\n      attr = STYLE_ATTRIBUTORS[name];\n      if (attr != null && (attr.attrName === name || attr.keyName === name)) {\n        attr = STYLE_ATTRIBUTORS[name];\n        formats[attr.attrName] = attr.value(node) || undefined;\n      }\n    });\n\n  return Object.entries(formats).reduce(\n    (newDelta, [name, value]) => applyFormat(newDelta, name, value, scroll),\n    delta,\n  );\n}\n\nfunction matchBlot(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query(node);\n  if (match == null) return delta;\n  // @ts-expect-error\n  if (match.prototype instanceof EmbedBlot) {\n    const embed = {};\n    // @ts-expect-error\n    const value = match.value(node);\n    if (value != null) {\n      // @ts-expect-error\n      embed[match.blotName] = value;\n      // @ts-expect-error\n      return new Delta().insert(embed, match.formats(node, scroll));\n    }\n  } else {\n    // @ts-expect-error\n    if (match.prototype instanceof BlockBlot && !deltaEndsWith(delta, '\\n')) {\n      delta.insert('\\n');\n    }\n    if (\n      'blotName' in match &&\n      'formats' in match &&\n      typeof match.formats === 'function'\n    ) {\n      return applyFormat(\n        delta,\n        match.blotName,\n        match.formats(node, scroll),\n        scroll,\n      );\n    }\n  }\n  return delta;\n}\n\nfunction matchBreak(node: Node, delta: Delta) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    delta.insert('\\n');\n  }\n  return delta;\n}\n\nfunction matchCodeBlock(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query('code-block');\n  const language =\n    match && 'formats' in match && typeof match.formats === 'function'\n      ? match.formats(node, scroll)\n      : true;\n  return applyFormat(delta, 'code-block', language, scroll);\n}\n\nfunction matchIgnore() {\n  return new Delta();\n}\n\nfunction matchIndent(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const match = scroll.query(node);\n  if (\n    match == null ||\n    // @ts-expect-error\n    match.blotName !== 'list' ||\n    !deltaEndsWith(delta, '\\n')\n  ) {\n    return delta;\n  }\n  let indent = -1;\n  let parent = node.parentNode;\n  while (parent != null) {\n    // @ts-expect-error\n    if (['OL', 'UL'].includes(parent.tagName)) {\n      indent += 1;\n    }\n    parent = parent.parentNode;\n  }\n  if (indent <= 0) return delta;\n  return delta.reduce((composed, op) => {\n    if (!op.insert) return composed;\n    if (op.attributes && typeof op.attributes.indent === 'number') {\n      return composed.push(op);\n    }\n    return composed.insert(op.insert, { indent, ...(op.attributes || {}) });\n  }, new Delta());\n}\n\nfunction matchList(node: Node, delta: Delta, scroll: ScrollBlot) {\n  const element = node as Element;\n  let list = element.tagName === 'OL' ? 'ordered' : 'bullet';\n\n  const checkedAttr = element.getAttribute('data-checked');\n  if (checkedAttr) {\n    list = checkedAttr === 'true' ? 'checked' : 'unchecked';\n  }\n\n  return applyFormat(delta, 'list', list, scroll);\n}\n\nfunction matchNewline(node: Node, delta: Delta, scroll: ScrollBlot) {\n  if (!deltaEndsWith(delta, '\\n')) {\n    if (\n      isLine(node, scroll) &&\n      (node.childNodes.length > 0 || node instanceof HTMLParagraphElement)\n    ) {\n      return delta.insert('\\n');\n    }\n    if (delta.length() > 0 && node.nextSibling) {\n      let nextSibling: Node | null = node.nextSibling;\n      while (nextSibling != null) {\n        if (isLine(nextSibling, scroll)) {\n          return delta.insert('\\n');\n        }\n        const match = scroll.query(nextSibling);\n        // @ts-expect-error\n        if (match && match.prototype instanceof BlockEmbed) {\n          return delta.insert('\\n');\n        }\n        nextSibling = nextSibling.firstChild;\n      }\n    }\n  }\n  return delta;\n}\n\nfunction matchStyles(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  const formats: Record<string, unknown> = {};\n  const style: Partial<CSSStyleDeclaration> = node.style || {};\n  if (style.fontStyle === 'italic') {\n    formats.italic = true;\n  }\n  if (style.textDecoration === 'underline') {\n    formats.underline = true;\n  }\n  if (style.textDecoration === 'line-through') {\n    formats.strike = true;\n  }\n  if (\n    style.fontWeight?.startsWith('bold') ||\n    // @ts-expect-error Fix me later\n    parseInt(style.fontWeight, 10) >= 700\n  ) {\n    formats.bold = true;\n  }\n  delta = Object.entries(formats).reduce(\n    (newDelta, [name, value]) => applyFormat(newDelta, name, value, scroll),\n    delta,\n  );\n  // @ts-expect-error\n  if (parseFloat(style.textIndent || 0) > 0) {\n    // Could be 0.5in\n    return new Delta().insert('\\t').concat(delta);\n  }\n  return delta;\n}\n\nfunction matchTable(\n  node: HTMLTableRowElement,\n  delta: Delta,\n  scroll: ScrollBlot,\n) {\n  const table =\n    node.parentElement?.tagName === 'TABLE'\n      ? node.parentElement\n      : node.parentElement?.parentElement;\n  if (table != null) {\n    const rows = Array.from(table.querySelectorAll('tr'));\n    const row = rows.indexOf(node) + 1;\n    return applyFormat(delta, 'table', row, scroll);\n  }\n  return delta;\n}\n\nfunction matchText(node: HTMLElement, delta: Delta, scroll: ScrollBlot) {\n  // @ts-expect-error\n  let text = node.data as string;\n  // Word represents empty line with <o:p>&nbsp;</o:p>\n  if (node.parentElement?.tagName === 'O:P') {\n    return delta.insert(text.trim());\n  }\n  if (!isPre(node)) {\n    if (\n      text.trim().length === 0 &&\n      text.includes('\\n') &&\n      !isBetweenInlineElements(node, scroll)\n    ) {\n      return delta;\n    }\n    // convert all non-nbsp whitespace into regular space\n    text = text.replace(/[^\\S\\u00a0]/g, ' ');\n    // collapse consecutive spaces into one\n    text = text.replace(/ {2,}/g, ' ');\n    if (\n      (node.previousSibling == null &&\n        node.parentElement != null &&\n        isLine(node.parentElement, scroll)) ||\n      (node.previousSibling instanceof Element &&\n        isLine(node.previousSibling, scroll))\n    ) {\n      // block structure means we don't need leading space\n      text = text.replace(/^ /, '');\n    }\n    if (\n      (node.nextSibling == null &&\n        node.parentElement != null &&\n        isLine(node.parentElement, scroll)) ||\n      (node.nextSibling instanceof Element && isLine(node.nextSibling, scroll))\n    ) {\n      // block structure means we don't need trailing space\n      text = text.replace(/ $/, '');\n    }\n    // done removing whitespace and can normalize all to regular space\n    text = text.replaceAll('\\u00a0', ' ');\n  }\n  return delta.insert(text);\n}\n\nexport {\n  Clipboard as default,\n  matchAttributor,\n  matchBlot,\n  matchNewline,\n  matchText,\n  traverse,\n};\n"], "mappings": "AACA,SACEA,UAAU,EACVC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,KAAK,EACLC,eAAe,QACV,WAAW;AAClB,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,UAAU,QAAQ,mBAAmB;AAE9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,SAASC,cAAc,EAAEC,UAAU,QAAQ,qBAAqB;AAChE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,yBAAyB;AAC5E,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,WAAW,QAAQ,eAAe;AAC3C,OAAOC,qBAAqB,MAAM,kCAAkC;AAEpE,MAAMC,KAAK,GAAGd,MAAM,CAAC,iBAAiB,CAAC;AAKvC,MAAMe,gBAAuC,GAAG,CAC9C,CAACC,IAAI,CAACC,SAAS,EAAEC,SAAS,CAAC,EAC3B,CAACF,IAAI,CAACC,SAAS,EAAEE,YAAY,CAAC,EAC9B,CAAC,IAAI,EAAEC,UAAU,CAAC,EAClB,CAACJ,IAAI,CAACK,YAAY,EAAEF,YAAY,CAAC,EACjC,CAACH,IAAI,CAACK,YAAY,EAAEC,SAAS,CAAC,EAC9B,CAACN,IAAI,CAACK,YAAY,EAAEE,eAAe,CAAC,EACpC,CAACP,IAAI,CAACK,YAAY,EAAEG,WAAW,CAAC,EAChC,CAAC,IAAI,EAAEC,WAAW,CAAC,EACnB,CAAC,QAAQ,EAAEC,SAAS,CAAC,EACrB,CAAC,KAAK,EAAEC,cAAc,CAAC,EACvB,CAAC,IAAI,EAAEC,UAAU,CAAC,EAClB,CAAC,GAAG,EAAEC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAC/B,CAAC,GAAG,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACjC,CAAC,QAAQ,EAAEA,gBAAgB,CAAC,QAAQ,CAAC,CAAC,EACtC,CAAC,OAAO,EAAEC,WAAW,CAAC,CACvB;AAED,MAAMC,qBAAqB,GAAG,CAAC5B,cAAc,EAAEK,kBAAkB,CAAC,CAACwB,MAAM,CACvE,CAACC,IAAgC,EAAEC,IAAI,KAAK;EAC1CD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EACD,CAAC,CACH,CAAC;AAED,MAAMG,iBAAiB,GAAG,CACxBhC,UAAU,EACVC,eAAe,EACfE,UAAU,EACVE,cAAc,EACdC,SAAS,EACTC,SAAS,CACV,CAACqB,MAAM,CAAC,CAACC,IAAgC,EAAEC,IAAI,KAAK;EACnDD,IAAI,CAACC,IAAI,CAACC,OAAO,CAAC,GAAGD,IAAI;EACzB,OAAOD,IAAI;AACb,CAAC,EAAE,CAAC,CAAC,CAAC;AAMN,MAAMI,SAAS,SAASpC,MAAM,CAAmB;EAC/C,OAAOqC,QAAQ,GAAqB;IAClCC,QAAQ,EAAE;EACZ,CAAC;EAIDC,WAAWA,CAACC,KAAY,EAAEC,OAAkC,EAAE;IAC5D,KAAK,CAACD,KAAK,EAAEC,OAAO,CAAC;IACrB,IAAI,CAACD,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAGC,CAAC,IACzC,IAAI,CAACC,aAAa,CAACD,CAAC,EAAE,KAAK,CAC7B,CAAC;IACD,IAAI,CAACJ,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,KAAK,EAAGC,CAAC,IAAK,IAAI,CAACC,aAAa,CAACD,CAAC,EAAE,IAAI,CAAC,CAAC;IAC3E,IAAI,CAACJ,KAAK,CAACE,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACG,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACzE,IAAI,CAACT,QAAQ,GAAG,EAAE;IAClBxB,gBAAgB,CAACkC,MAAM,CAAC,IAAI,CAACP,OAAO,CAACH,QAAQ,IAAI,EAAE,CAAC,CAACW,OAAO,CAC1DC,IAAA,IAAyB;MAAA,IAAxB,CAACC,QAAQ,EAAEC,OAAO,CAAC,GAAAF,IAAA;MAClB,IAAI,CAACG,UAAU,CAACF,QAAQ,EAAEC,OAAO,CAAC;IACpC,CACF,CAAC;EACH;EAEAC,UAAUA,CAACF,QAAkB,EAAEC,OAAgB,EAAE;IAC/C,IAAI,CAACd,QAAQ,CAACgB,IAAI,CAAC,CAACH,QAAQ,EAAEC,OAAO,CAAC,CAAC;EACzC;EAEAG,OAAOA,CAAAC,KAAA,EAGL;IAAA,IAFA;MAAEC,IAAI;MAAEC;IAAuC,CAAC,GAAAF,KAAA;IAAA,IAChDG,OAAgC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAErC,IAAID,OAAO,CAACtD,SAAS,CAAC0D,QAAQ,CAAC,EAAE;MAC/B,OAAO,IAAIlE,KAAK,CAAC,CAAC,CAACmE,MAAM,CAACN,IAAI,IAAI,EAAE,EAAE;QACpC,CAACrD,SAAS,CAAC0D,QAAQ,GAAGJ,OAAO,CAACtD,SAAS,CAAC0D,QAAQ;MAClD,CAAC,CAAC;IACJ;IACA,IAAI,CAACN,IAAI,EAAE;MACT,OAAO,IAAI5D,KAAK,CAAC,CAAC,CAACmE,MAAM,CAACN,IAAI,IAAI,EAAE,EAAEC,OAAO,CAAC;IAChD;IACA,MAAMM,KAAK,GAAG,IAAI,CAACC,WAAW,CAACT,IAAI,CAAC;IACpC;IACA,IACEU,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,KACzBA,KAAK,CAACG,GAAG,CAACH,KAAK,CAACG,GAAG,CAACP,MAAM,GAAG,CAAC,CAAC,CAACQ,UAAU,IAAI,IAAI,IAAIV,OAAO,CAACW,KAAK,CAAC,EACrE;MACA,OAAOL,KAAK,CAACM,OAAO,CAAC,IAAI1E,KAAK,CAAC,CAAC,CAAC2E,MAAM,CAACP,KAAK,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAACY,MAAM,CAAC,CAAC,CAAC,CAAC;IACxE;IACA,OAAOR,KAAK;EACd;EAEUS,aAAaA,CAACC,GAAa,EAAE;IACrC/D,qBAAqB,CAAC+D,GAAG,CAAC;EAC5B;EAEUT,WAAWA,CAACT,IAAY,EAAE;IAClC,MAAMkB,GAAG,GAAG,IAAIC,SAAS,CAAC,CAAC,CAACC,eAAe,CAACpB,IAAI,EAAE,WAAW,CAAC;IAC9D,IAAI,CAACiB,aAAa,CAACC,GAAG,CAAC;IACvB,MAAMG,SAAS,GAAGH,GAAG,CAACI,IAAI;IAC1B,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;IACjC,MAAM,CAACC,eAAe,EAAEC,YAAY,CAAC,GAAG,IAAI,CAACC,eAAe,CAC1DN,SAAS,EACTE,WACF,CAAC;IACD,OAAOK,QAAQ,CACb,IAAI,CAAC7C,KAAK,CAAC8C,MAAM,EACjBR,SAAS,EACTI,eAAe,EACfC,YAAY,EACZH,WACF,CAAC;EACH;EAQAO,oBAAoBA,CAClBC,KAAsB,EACtB/B,IAAa,EAEb;IAAA,IADAgC,MAAqB,GAAA7B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG3D,KAAK,CAACyF,OAAO,CAACC,GAAG;IAEzC,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;MAC7B,MAAMvB,KAAK,GAAG,IAAI,CAACV,OAAO,CAAC;QAAEE,IAAI,EAAE+B,KAAK;QAAE9B,IAAI,EAAE;MAAG,CAAC,CAAC;MACrD;MACA,IAAI,CAAClB,KAAK,CAACoD,WAAW,CAAC3B,KAAK,EAAER,IAAI,CAAC;MACnC,IAAI,CAACjB,KAAK,CAACqD,YAAY,CAAC,CAAC,EAAE5F,KAAK,CAACyF,OAAO,CAACI,MAAM,CAAC;IAClD,CAAC,MAAM;MACL,MAAMC,KAAK,GAAG,IAAI,CAACxC,OAAO,CAAC;QAAEE,IAAI;QAAEC,IAAI,EAAE;MAAG,CAAC,CAAC;MAC9C,IAAI,CAAClB,KAAK,CAACwD,cAAc,CACvB,IAAInG,KAAK,CAAC,CAAC,CAAC2E,MAAM,CAACgB,KAAK,CAAC,CAACxC,MAAM,CAAC+C,KAAK,CAAC,EACvCN,MACF,CAAC;MACD,IAAI,CAACjD,KAAK,CAACqD,YAAY,CAACL,KAAK,GAAGO,KAAK,CAAClC,MAAM,CAAC,CAAC,EAAE5D,KAAK,CAACyF,OAAO,CAACI,MAAM,CAAC;IACvE;EACF;EAEAjD,aAAaA,CAACD,CAAiB,EAAiB;IAAA,IAAfqD,KAAK,GAAArC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAC5C,IAAIhB,CAAC,CAACsD,gBAAgB,EAAE;IACxBtD,CAAC,CAACuD,cAAc,CAAC,CAAC;IAClB,MAAM,CAACC,KAAK,CAAC,GAAG,IAAI,CAAC5D,KAAK,CAAC6D,SAAS,CAACC,QAAQ,CAAC,CAAC;IAC/C,IAAIF,KAAK,IAAI,IAAI,EAAE;IACnB,MAAM;MAAE3C,IAAI;MAAEC;IAAK,CAAC,GAAG,IAAI,CAAC6C,MAAM,CAACH,KAAK,EAAEH,KAAK,CAAC;IAChDrD,CAAC,CAAC4D,aAAa,EAAEC,OAAO,CAAC,YAAY,EAAE/C,IAAI,CAAC;IAC5Cd,CAAC,CAAC4D,aAAa,EAAEC,OAAO,CAAC,WAAW,EAAEhD,IAAI,CAAC;IAC3C,IAAIwC,KAAK,EAAE;MACTtF,WAAW,CAAC;QAAEyF,KAAK;QAAE5D,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;IAC3C;EACF;;EAEA;AACF;AACA;EACUkE,gBAAgBA,CAACC,OAAe,EAAE;IACxC,OACEA,OAAO,CACJC,KAAK,CAAC,OAAO;IACd;IAAA,CACCC,MAAM,CAAEC,GAAG,IAAKA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAC/BC,IAAI,CAAC,IAAI,CAAC;EAEjB;EAEAjE,cAAcA,CAACF,CAAiB,EAAE;IAChC,IAAIA,CAAC,CAACsD,gBAAgB,IAAI,CAAC,IAAI,CAAC1D,KAAK,CAACwE,SAAS,CAAC,CAAC,EAAE;IACnDpE,CAAC,CAACuD,cAAc,CAAC,CAAC;IAClB,MAAMC,KAAK,GAAG,IAAI,CAAC5D,KAAK,CAACyE,YAAY,CAAC,IAAI,CAAC;IAC3C,IAAIb,KAAK,IAAI,IAAI,EAAE;IACnB,MAAM3C,IAAI,GAAGb,CAAC,CAAC4D,aAAa,EAAEU,OAAO,CAAC,WAAW,CAAC;IAClD,IAAIxD,IAAI,GAAGd,CAAC,CAAC4D,aAAa,EAAEU,OAAO,CAAC,YAAY,CAAC;IACjD,IAAI,CAACzD,IAAI,IAAI,CAACC,IAAI,EAAE;MAClB,MAAMiD,OAAO,GAAG/D,CAAC,CAAC4D,aAAa,EAAEU,OAAO,CAAC,eAAe,CAAC;MACzD,IAAIP,OAAO,EAAE;QACXjD,IAAI,GAAG,IAAI,CAACgD,gBAAgB,CAACC,OAAO,CAAC;MACvC;IACF;IACA,MAAMQ,KAAK,GAAGC,KAAK,CAACC,IAAI,CAACzE,CAAC,CAAC4D,aAAa,EAAEW,KAAK,IAAI,EAAE,CAAC;IACtD,IAAI,CAAC1D,IAAI,IAAI0D,KAAK,CAACtD,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACrB,KAAK,CAAC8E,QAAQ,CAACC,MAAM,CAACnB,KAAK,EAAEe,KAAK,CAAC;MACxC;IACF;IACA,IAAI1D,IAAI,IAAI0D,KAAK,CAACtD,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMc,GAAG,GAAG,IAAIC,SAAS,CAAC,CAAC,CAACC,eAAe,CAACpB,IAAI,EAAE,WAAW,CAAC;MAC9D,IACEkB,GAAG,CAACI,IAAI,CAACyC,iBAAiB,KAAK,CAAC,IAChC7C,GAAG,CAACI,IAAI,CAAC0C,iBAAiB,EAAEC,OAAO,KAAK,KAAK,EAC7C;QACA,IAAI,CAAClF,KAAK,CAAC8E,QAAQ,CAACC,MAAM,CAACnB,KAAK,EAAEe,KAAK,CAAC;QACxC;MACF;IACF;IACA,IAAI,CAACQ,OAAO,CAACvB,KAAK,EAAE;MAAE3C,IAAI;MAAEC;IAAK,CAAC,CAAC;EACrC;EAGA6C,MAAMA,CAACH,KAAY,EAAE;IACnB,MAAM1C,IAAI,GAAG,IAAI,CAAClB,KAAK,CAACoF,OAAO,CAACxB,KAAK,CAAC;IACtC,MAAM3C,IAAI,GAAG,IAAI,CAACjB,KAAK,CAACqF,eAAe,CAACzB,KAAK,CAAC;IAC9C,OAAO;MAAE3C,IAAI;MAAEC;IAAK,CAAC;EACvB;EAEAiE,OAAOA,CAACvB,KAAY,EAAA0B,KAAA,EAAoD;IAAA,IAAlD;MAAEpE,IAAI;MAAED;IAAuC,CAAC,GAAAqE,KAAA;IACpE,MAAMnE,OAAO,GAAG,IAAI,CAACnB,KAAK,CAACuF,SAAS,CAAC3B,KAAK,CAACZ,KAAK,CAAC;IACjD,MAAMwC,WAAW,GAAG,IAAI,CAACzE,OAAO,CAAC;MAAEG,IAAI;MAAED;IAAK,CAAC,EAAEE,OAAO,CAAC;IACzD9C,KAAK,CAACoH,GAAG,CAAC,SAAS,EAAED,WAAW,EAAE;MAAEtE,IAAI;MAAED;IAAK,CAAC,CAAC;IACjD,MAAMQ,KAAK,GAAG,IAAIpE,KAAK,CAAC,CAAC,CACtB2E,MAAM,CAAC4B,KAAK,CAACZ,KAAK,CAAC,CACnBf,MAAM,CAAC2B,KAAK,CAACvC,MAAM,CAAC,CACpBb,MAAM,CAACgF,WAAW,CAAC;IACtB,IAAI,CAACxF,KAAK,CAACwD,cAAc,CAAC/B,KAAK,EAAEhE,KAAK,CAACyF,OAAO,CAACwC,IAAI,CAAC;IACpD;IACA,IAAI,CAAC1F,KAAK,CAACqD,YAAY,CACrB5B,KAAK,CAACJ,MAAM,CAAC,CAAC,GAAGuC,KAAK,CAACvC,MAAM,EAC7B5D,KAAK,CAACyF,OAAO,CAACI,MAChB,CAAC;IACD,IAAI,CAACtD,KAAK,CAAC2F,uBAAuB,CAAC,CAAC;EACtC;EAEA/C,eAAeA,CAACN,SAAkB,EAAEE,WAAqC,EAAE;IACzE,MAAME,eAA0B,GAAG,EAAE;IACrC,MAAMC,YAAuB,GAAG,EAAE;IAClC,IAAI,CAAC7C,QAAQ,CAACW,OAAO,CAAEmF,IAAI,IAAK;MAC9B,MAAM,CAACjF,QAAQ,EAAEC,OAAO,CAAC,GAAGgF,IAAI;MAChC,QAAQjF,QAAQ;QACd,KAAKpC,IAAI,CAACC,SAAS;UACjBmE,YAAY,CAAC7B,IAAI,CAACF,OAAO,CAAC;UAC1B;QACF,KAAKrC,IAAI,CAACK,YAAY;UACpB8D,eAAe,CAAC5B,IAAI,CAACF,OAAO,CAAC;UAC7B;QACF;UACEgE,KAAK,CAACC,IAAI,CAACvC,SAAS,CAACuD,gBAAgB,CAAClF,QAAQ,CAAC,CAAC,CAACF,OAAO,CAAEqF,IAAI,IAAK;YACjE,IAAItD,WAAW,CAACuD,GAAG,CAACD,IAAI,CAAC,EAAE;cACzB,MAAME,OAAO,GAAGxD,WAAW,CAACyD,GAAG,CAACH,IAAI,CAAC;cACrCE,OAAO,EAAElF,IAAI,CAACF,OAAO,CAAC;YACxB,CAAC,MAAM;cACL4B,WAAW,CAAC0D,GAAG,CAACJ,IAAI,EAAE,CAAClF,OAAO,CAAC,CAAC;YAClC;UACF,CAAC,CAAC;UACF;MACJ;IACF,CAAC,CAAC;IACF,OAAO,CAAC8B,eAAe,EAAEC,YAAY,CAAC;EACxC;AACF;AAEA,SAASwD,WAAWA,CAClB1E,KAAY,EACZ2E,MAAc,EACdC,KAAc,EACdvD,MAAkB,EACX;EACP,IAAI,CAACA,MAAM,CAACwD,KAAK,CAACF,MAAM,CAAC,EAAE;IACzB,OAAO3E,KAAK;EACd;EAEA,OAAOA,KAAK,CAAClC,MAAM,CAAC,CAACgH,QAAQ,EAAEC,EAAE,KAAK;IACpC,IAAI,CAACA,EAAE,CAAChF,MAAM,EAAE,OAAO+E,QAAQ;IAC/B,IAAIC,EAAE,CAAC3E,UAAU,IAAI2E,EAAE,CAAC3E,UAAU,CAACuE,MAAM,CAAC,EAAE;MAC1C,OAAOG,QAAQ,CAACzF,IAAI,CAAC0F,EAAE,CAAC;IAC1B;IACA,MAAMrF,OAAO,GAAGkF,KAAK,GAAG;MAAE,CAACD,MAAM,GAAGC;IAAM,CAAC,GAAG,CAAC,CAAC;IAChD,OAAOE,QAAQ,CAAC/E,MAAM,CAACgF,EAAE,CAAChF,MAAM,EAAE;MAAE,GAAGL,OAAO;MAAE,GAAGqF,EAAE,CAAC3E;IAAW,CAAC,CAAC;EACrE,CAAC,EAAE,IAAIxE,KAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAASsE,aAAaA,CAACF,KAAY,EAAEP,IAAY,EAAE;EACjD,IAAIuF,OAAO,GAAG,EAAE;EAChB,KACE,IAAIC,CAAC,GAAGjF,KAAK,CAACG,GAAG,CAACP,MAAM,GAAG,CAAC,EAC5BqF,CAAC,IAAI,CAAC,IAAID,OAAO,CAACpF,MAAM,GAAGH,IAAI,CAACG,MAAM,EACtC,EAAEqF,CAAC,CAAC;EAAA,EACJ;IACA,MAAMF,EAAE,GAAG/E,KAAK,CAACG,GAAG,CAAC8E,CAAC,CAAC;IACvB,IAAI,OAAOF,EAAE,CAAChF,MAAM,KAAK,QAAQ,EAAE;IACnCiF,OAAO,GAAGD,EAAE,CAAChF,MAAM,GAAGiF,OAAO;EAC/B;EACA,OAAOA,OAAO,CAACE,KAAK,CAAC,CAAC,CAAC,GAAGzF,IAAI,CAACG,MAAM,CAAC,KAAKH,IAAI;AACjD;AAEA,SAAS0F,MAAMA,CAACd,IAAU,EAAEhD,MAAkB,EAAE;EAC9C,IAAI,EAAEgD,IAAI,YAAYe,OAAO,CAAC,EAAE,OAAO,KAAK;EAC5C,MAAMC,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAACR,IAAI,CAAC;EAChC;EACA,IAAIgB,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAY7J,SAAS,EAAE,OAAO,KAAK;EAE/D,OAAO,CACL,SAAS,EACT,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,MAAM,EACN,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,GAAG,EACH,KAAK,EACL,SAAS,EACT,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,OAAO,CACR,CAAC8J,QAAQ,CAAClB,IAAI,CAACZ,OAAO,CAAC+B,WAAW,CAAC,CAAC,CAAC;AACxC;AAEA,SAASC,uBAAuBA,CAACpB,IAAiB,EAAEhD,MAAkB,EAAE;EACtE,OACEgD,IAAI,CAACqB,sBAAsB,IAC3BrB,IAAI,CAACsB,kBAAkB,IACvB,CAACR,MAAM,CAACd,IAAI,CAACqB,sBAAsB,EAAErE,MAAM,CAAC,IAC5C,CAAC8D,MAAM,CAACd,IAAI,CAACsB,kBAAkB,EAAEtE,MAAM,CAAC;AAE5C;AAEA,MAAMuE,QAAQ,GAAG,IAAI5E,OAAO,CAAC,CAAC;AAC9B,SAAS6E,KAAKA,CAACxB,IAAiB,EAAE;EAChC,IAAIA,IAAI,IAAI,IAAI,EAAE,OAAO,KAAK;EAC9B,IAAI,CAACuB,QAAQ,CAACtB,GAAG,CAACD,IAAI,CAAC,EAAE;IACvB;IACA,IAAIA,IAAI,CAACZ,OAAO,KAAK,KAAK,EAAE;MAC1BmC,QAAQ,CAACnB,GAAG,CAACJ,IAAI,EAAE,IAAI,CAAC;IAC1B,CAAC,MAAM;MACLuB,QAAQ,CAACnB,GAAG,CAACJ,IAAI,EAAEwB,KAAK,CAACxB,IAAI,CAACyB,UAAU,CAAC,CAAC;IAC5C;EACF;EACA,OAAOF,QAAQ,CAACpB,GAAG,CAACH,IAAI,CAAC;AAC3B;AAEA,SAASjD,QAAQA,CACfC,MAAkB,EAClBgD,IAAe,EACfpD,eAA0B,EAC1BC,YAAuB,EACvBH,WAAqC,EAC9B;EACP;EACA,IAAIsD,IAAI,CAAC0B,QAAQ,KAAK1B,IAAI,CAACtH,SAAS,EAAE;IACpC,OAAOmE,YAAY,CAACpD,MAAM,CAAC,CAACkC,KAAY,EAAEb,OAAO,KAAK;MACpD,OAAOA,OAAO,CAACkF,IAAI,EAAErE,KAAK,EAAEqB,MAAM,CAAC;IACrC,CAAC,EAAE,IAAIzF,KAAK,CAAC,CAAC,CAAC;EACjB;EACA,IAAIyI,IAAI,CAAC0B,QAAQ,KAAK1B,IAAI,CAAClH,YAAY,EAAE;IACvC,OAAOgG,KAAK,CAACC,IAAI,CAACiB,IAAI,CAAC2B,UAAU,IAAI,EAAE,CAAC,CAAClI,MAAM,CAAC,CAACkC,KAAK,EAAEiG,SAAS,KAAK;MACpE,IAAIC,aAAa,GAAG9E,QAAQ,CAC1BC,MAAM,EACN4E,SAAS,EACThF,eAAe,EACfC,YAAY,EACZH,WACF,CAAC;MACD,IAAIkF,SAAS,CAACF,QAAQ,KAAK1B,IAAI,CAAClH,YAAY,EAAE;QAC5C+I,aAAa,GAAGjF,eAAe,CAACnD,MAAM,CAAC,CAACqI,YAAY,EAAEhH,OAAO,KAAK;UAChE,OAAOA,OAAO,CAAC8G,SAAS,EAAiBE,YAAY,EAAE9E,MAAM,CAAC;QAChE,CAAC,EAAE6E,aAAa,CAAC;QACjBA,aAAa,GAAG,CAACnF,WAAW,CAACyD,GAAG,CAACyB,SAAS,CAAC,IAAI,EAAE,EAAEnI,MAAM,CACvD,CAACqI,YAAY,EAAEhH,OAAO,KAAK;UACzB,OAAOA,OAAO,CAAC8G,SAAS,EAAEE,YAAY,EAAE9E,MAAM,CAAC;QACjD,CAAC,EACD6E,aACF,CAAC;MACH;MACA,OAAOlG,KAAK,CAACjB,MAAM,CAACmH,aAAa,CAAC;IACpC,CAAC,EAAE,IAAItK,KAAK,CAAC,CAAC,CAAC;EACjB;EACA,OAAO,IAAIA,KAAK,CAAC,CAAC;AACpB;AAEA,SAAS+B,gBAAgBA,CAACgH,MAAc,EAAE;EACxC,OAAO,CAACyB,KAAc,EAAEpG,KAAY,EAAEqB,MAAkB,KAAK;IAC3D,OAAOqD,WAAW,CAAC1E,KAAK,EAAE2E,MAAM,EAAE,IAAI,EAAEtD,MAAM,CAAC;EACjD,CAAC;AACH;AAEA,SAAShE,eAAeA,CAACgH,IAAiB,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EAC5E,MAAMjB,UAAU,GAAG9E,UAAU,CAAC+K,IAAI,CAAChC,IAAI,CAAC;EACxC,MAAMiC,OAAO,GAAG9K,eAAe,CAAC6K,IAAI,CAAChC,IAAI,CAAC;EAC1C,MAAMkC,MAAM,GAAG5K,eAAe,CAAC0K,IAAI,CAAChC,IAAI,CAAC;EACzC,MAAM3E,OAA2C,GAAG,CAAC,CAAC;EACtDU,UAAU,CACPrB,MAAM,CAACuH,OAAO,CAAC,CACfvH,MAAM,CAACwH,MAAM,CAAC,CACdvH,OAAO,CAAEwH,IAAI,IAAK;IACjB,IAAIxI,IAAI,GAAGqD,MAAM,CAACwD,KAAK,CAAC2B,IAAI,EAAE9K,KAAK,CAAC+K,SAAS,CAAe;IAC5D,IAAIzI,IAAI,IAAI,IAAI,EAAE;MAChB0B,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,GAAG1I,IAAI,CAAC4G,KAAK,CAACP,IAAI,CAAC;MACzC,IAAI3E,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,EAAE;IAC9B;IACA1I,IAAI,GAAGH,qBAAqB,CAAC2I,IAAI,CAAC;IAClC,IAAIxI,IAAI,IAAI,IAAI,KAAKA,IAAI,CAAC0I,QAAQ,KAAKF,IAAI,IAAIxI,IAAI,CAACC,OAAO,KAAKuI,IAAI,CAAC,EAAE;MACrE9G,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,GAAG1I,IAAI,CAAC4G,KAAK,CAACP,IAAI,CAAC,IAAIxE,SAAS;IACxD;IACA7B,IAAI,GAAGE,iBAAiB,CAACsI,IAAI,CAAC;IAC9B,IAAIxI,IAAI,IAAI,IAAI,KAAKA,IAAI,CAAC0I,QAAQ,KAAKF,IAAI,IAAIxI,IAAI,CAACC,OAAO,KAAKuI,IAAI,CAAC,EAAE;MACrExI,IAAI,GAAGE,iBAAiB,CAACsI,IAAI,CAAC;MAC9B9G,OAAO,CAAC1B,IAAI,CAAC0I,QAAQ,CAAC,GAAG1I,IAAI,CAAC4G,KAAK,CAACP,IAAI,CAAC,IAAIxE,SAAS;IACxD;EACF,CAAC,CAAC;EAEJ,OAAO8G,MAAM,CAACC,OAAO,CAAClH,OAAO,CAAC,CAAC5B,MAAM,CACnC,CAACgH,QAAQ,EAAA+B,KAAA;IAAA,IAAE,CAACL,IAAI,EAAE5B,KAAK,CAAC,GAAAiC,KAAA;IAAA,OAAKnC,WAAW,CAACI,QAAQ,EAAE0B,IAAI,EAAE5B,KAAK,EAAEvD,MAAM,CAAC;EAAA,GACvErB,KACF,CAAC;AACH;AAEA,SAAS5C,SAASA,CAACiH,IAAU,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EAC/D,MAAMgE,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAACR,IAAI,CAAC;EAChC,IAAIgB,KAAK,IAAI,IAAI,EAAE,OAAOrF,KAAK;EAC/B;EACA,IAAIqF,KAAK,CAACC,SAAS,YAAY7J,SAAS,EAAE;IACxC,MAAMqL,KAAK,GAAG,CAAC,CAAC;IAChB;IACA,MAAMlC,KAAK,GAAGS,KAAK,CAACT,KAAK,CAACP,IAAI,CAAC;IAC/B,IAAIO,KAAK,IAAI,IAAI,EAAE;MACjB;MACAkC,KAAK,CAACzB,KAAK,CAACvF,QAAQ,CAAC,GAAG8E,KAAK;MAC7B;MACA,OAAO,IAAIhJ,KAAK,CAAC,CAAC,CAACmE,MAAM,CAAC+G,KAAK,EAAEzB,KAAK,CAAC3F,OAAO,CAAC2E,IAAI,EAAEhD,MAAM,CAAC,CAAC;IAC/D;EACF,CAAC,MAAM;IACL;IACA,IAAIgE,KAAK,CAACC,SAAS,YAAY/J,SAAS,IAAI,CAAC2E,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;MACvEA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;IACpB;IACA,IACE,UAAU,IAAIsF,KAAK,IACnB,SAAS,IAAIA,KAAK,IAClB,OAAOA,KAAK,CAAC3F,OAAO,KAAK,UAAU,EACnC;MACA,OAAOgF,WAAW,CAChB1E,KAAK,EACLqF,KAAK,CAACvF,QAAQ,EACduF,KAAK,CAAC3F,OAAO,CAAC2E,IAAI,EAAEhD,MAAM,CAAC,EAC3BA,MACF,CAAC;IACH;EACF;EACA,OAAOrB,KAAK;AACd;AAEA,SAAS9C,UAAUA,CAACmH,IAAU,EAAErE,KAAY,EAAE;EAC5C,IAAI,CAACE,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/BA,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;EACpB;EACA,OAAOC,KAAK;AACd;AAEA,SAASvC,cAAcA,CAAC4G,IAAU,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EACpE,MAAMgE,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAAC,YAAY,CAAC;EACxC,MAAMkC,QAAQ,GACZ1B,KAAK,IAAI,SAAS,IAAIA,KAAK,IAAI,OAAOA,KAAK,CAAC3F,OAAO,KAAK,UAAU,GAC9D2F,KAAK,CAAC3F,OAAO,CAAC2E,IAAI,EAAEhD,MAAM,CAAC,GAC3B,IAAI;EACV,OAAOqD,WAAW,CAAC1E,KAAK,EAAE,YAAY,EAAE+G,QAAQ,EAAE1F,MAAM,CAAC;AAC3D;AAEA,SAASzD,WAAWA,CAAA,EAAG;EACrB,OAAO,IAAIhC,KAAK,CAAC,CAAC;AACpB;AAEA,SAAS2B,WAAWA,CAAC8G,IAAU,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EACjE,MAAMgE,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAACR,IAAI,CAAC;EAChC,IACEgB,KAAK,IAAI,IAAI;EACb;EACAA,KAAK,CAACvF,QAAQ,KAAK,MAAM,IACzB,CAACI,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAC3B;IACA,OAAOA,KAAK;EACd;EACA,IAAIgH,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,MAAM,GAAG5C,IAAI,CAACyB,UAAU;EAC5B,OAAOmB,MAAM,IAAI,IAAI,EAAE;IACrB;IACA,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC1B,QAAQ,CAAC0B,MAAM,CAACxD,OAAO,CAAC,EAAE;MACzCuD,MAAM,IAAI,CAAC;IACb;IACAC,MAAM,GAAGA,MAAM,CAACnB,UAAU;EAC5B;EACA,IAAIkB,MAAM,IAAI,CAAC,EAAE,OAAOhH,KAAK;EAC7B,OAAOA,KAAK,CAAClC,MAAM,CAAC,CAACoJ,QAAQ,EAAEnC,EAAE,KAAK;IACpC,IAAI,CAACA,EAAE,CAAChF,MAAM,EAAE,OAAOmH,QAAQ;IAC/B,IAAInC,EAAE,CAAC3E,UAAU,IAAI,OAAO2E,EAAE,CAAC3E,UAAU,CAAC4G,MAAM,KAAK,QAAQ,EAAE;MAC7D,OAAOE,QAAQ,CAAC7H,IAAI,CAAC0F,EAAE,CAAC;IAC1B;IACA,OAAOmC,QAAQ,CAACnH,MAAM,CAACgF,EAAE,CAAChF,MAAM,EAAE;MAAEiH,MAAM;MAAE,IAAIjC,EAAE,CAAC3E,UAAU,IAAI,CAAC,CAAC;IAAE,CAAC,CAAC;EACzE,CAAC,EAAE,IAAIxE,KAAK,CAAC,CAAC,CAAC;AACjB;AAEA,SAAS4B,SAASA,CAAC6G,IAAU,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EAC/D,MAAM8F,OAAO,GAAG9C,IAAe;EAC/B,IAAI+C,IAAI,GAAGD,OAAO,CAAC1D,OAAO,KAAK,IAAI,GAAG,SAAS,GAAG,QAAQ;EAE1D,MAAM4D,WAAW,GAAGF,OAAO,CAACG,YAAY,CAAC,cAAc,CAAC;EACxD,IAAID,WAAW,EAAE;IACfD,IAAI,GAAGC,WAAW,KAAK,MAAM,GAAG,SAAS,GAAG,WAAW;EACzD;EAEA,OAAO3C,WAAW,CAAC1E,KAAK,EAAE,MAAM,EAAEoH,IAAI,EAAE/F,MAAM,CAAC;AACjD;AAEA,SAASpE,YAAYA,CAACoH,IAAU,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EAClE,IAAI,CAACnB,aAAa,CAACF,KAAK,EAAE,IAAI,CAAC,EAAE;IAC/B,IACEmF,MAAM,CAACd,IAAI,EAAEhD,MAAM,CAAC,KACnBgD,IAAI,CAAC2B,UAAU,CAACpG,MAAM,GAAG,CAAC,IAAIyE,IAAI,YAAYkD,oBAAoB,CAAC,EACpE;MACA,OAAOvH,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;IAC3B;IACA,IAAIC,KAAK,CAACJ,MAAM,CAAC,CAAC,GAAG,CAAC,IAAIyE,IAAI,CAACmD,WAAW,EAAE;MAC1C,IAAIA,WAAwB,GAAGnD,IAAI,CAACmD,WAAW;MAC/C,OAAOA,WAAW,IAAI,IAAI,EAAE;QAC1B,IAAIrC,MAAM,CAACqC,WAAW,EAAEnG,MAAM,CAAC,EAAE;UAC/B,OAAOrB,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;QAC3B;QACA,MAAMsF,KAAK,GAAGhE,MAAM,CAACwD,KAAK,CAAC2C,WAAW,CAAC;QACvC;QACA,IAAInC,KAAK,IAAIA,KAAK,CAACC,SAAS,YAAYzJ,UAAU,EAAE;UAClD,OAAOmE,KAAK,CAACD,MAAM,CAAC,IAAI,CAAC;QAC3B;QACAyH,WAAW,GAAGA,WAAW,CAACC,UAAU;MACtC;IACF;EACF;EACA,OAAOzH,KAAK;AACd;AAEA,SAAS1C,WAAWA,CAAC+G,IAAiB,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EACxE,MAAM3B,OAAgC,GAAG,CAAC,CAAC;EAC3C,MAAMgI,KAAmC,GAAGrD,IAAI,CAACqD,KAAK,IAAI,CAAC,CAAC;EAC5D,IAAIA,KAAK,CAACC,SAAS,KAAK,QAAQ,EAAE;IAChCjI,OAAO,CAACkI,MAAM,GAAG,IAAI;EACvB;EACA,IAAIF,KAAK,CAACG,cAAc,KAAK,WAAW,EAAE;IACxCnI,OAAO,CAACoI,SAAS,GAAG,IAAI;EAC1B;EACA,IAAIJ,KAAK,CAACG,cAAc,KAAK,cAAc,EAAE;IAC3CnI,OAAO,CAACqI,MAAM,GAAG,IAAI;EACvB;EACA,IACEL,KAAK,CAACM,UAAU,EAAEC,UAAU,CAAC,MAAM,CAAC;EACpC;EACAC,QAAQ,CAACR,KAAK,CAACM,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,EACrC;IACAtI,OAAO,CAACyI,IAAI,GAAG,IAAI;EACrB;EACAnI,KAAK,GAAG2G,MAAM,CAACC,OAAO,CAAClH,OAAO,CAAC,CAAC5B,MAAM,CACpC,CAACgH,QAAQ,EAAAsD,KAAA;IAAA,IAAE,CAAC5B,IAAI,EAAE5B,KAAK,CAAC,GAAAwD,KAAA;IAAA,OAAK1D,WAAW,CAACI,QAAQ,EAAE0B,IAAI,EAAE5B,KAAK,EAAEvD,MAAM,CAAC;EAAA,GACvErB,KACF,CAAC;EACD;EACA,IAAIqI,UAAU,CAACX,KAAK,CAACY,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;IACzC;IACA,OAAO,IAAI1M,KAAK,CAAC,CAAC,CAACmE,MAAM,CAAC,IAAI,CAAC,CAAChB,MAAM,CAACiB,KAAK,CAAC;EAC/C;EACA,OAAOA,KAAK;AACd;AAEA,SAAStC,UAAUA,CACjB2G,IAAyB,EACzBrE,KAAY,EACZqB,MAAkB,EAClB;EACA,MAAMhB,KAAK,GACTgE,IAAI,CAACkE,aAAa,EAAE9E,OAAO,KAAK,OAAO,GACnCY,IAAI,CAACkE,aAAa,GAClBlE,IAAI,CAACkE,aAAa,EAAEA,aAAa;EACvC,IAAIlI,KAAK,IAAI,IAAI,EAAE;IACjB,MAAMmI,IAAI,GAAGrF,KAAK,CAACC,IAAI,CAAC/C,KAAK,CAAC+D,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACrD,MAAMqE,GAAG,GAAGD,IAAI,CAACE,OAAO,CAACrE,IAAI,CAAC,GAAG,CAAC;IAClC,OAAOK,WAAW,CAAC1E,KAAK,EAAE,OAAO,EAAEyI,GAAG,EAAEpH,MAAM,CAAC;EACjD;EACA,OAAOrB,KAAK;AACd;AAEA,SAAShD,SAASA,CAACqH,IAAiB,EAAErE,KAAY,EAAEqB,MAAkB,EAAE;EACtE;EACA,IAAI5B,IAAI,GAAG4E,IAAI,CAACsE,IAAc;EAC9B;EACA,IAAItE,IAAI,CAACkE,aAAa,EAAE9E,OAAO,KAAK,KAAK,EAAE;IACzC,OAAOzD,KAAK,CAACD,MAAM,CAACN,IAAI,CAACmJ,IAAI,CAAC,CAAC,CAAC;EAClC;EACA,IAAI,CAAC/C,KAAK,CAACxB,IAAI,CAAC,EAAE;IAChB,IACE5E,IAAI,CAACmJ,IAAI,CAAC,CAAC,CAAChJ,MAAM,KAAK,CAAC,IACxBH,IAAI,CAAC8F,QAAQ,CAAC,IAAI,CAAC,IACnB,CAACE,uBAAuB,CAACpB,IAAI,EAAEhD,MAAM,CAAC,EACtC;MACA,OAAOrB,KAAK;IACd;IACA;IACAP,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC;IACxC;IACApJ,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;IAClC,IACGxE,IAAI,CAACyE,eAAe,IAAI,IAAI,IAC3BzE,IAAI,CAACkE,aAAa,IAAI,IAAI,IAC1BpD,MAAM,CAACd,IAAI,CAACkE,aAAa,EAAElH,MAAM,CAAC,IACnCgD,IAAI,CAACyE,eAAe,YAAY1D,OAAO,IACtCD,MAAM,CAACd,IAAI,CAACyE,eAAe,EAAEzH,MAAM,CAAE,EACvC;MACA;MACA5B,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC/B;IACA,IACGxE,IAAI,CAACmD,WAAW,IAAI,IAAI,IACvBnD,IAAI,CAACkE,aAAa,IAAI,IAAI,IAC1BpD,MAAM,CAACd,IAAI,CAACkE,aAAa,EAAElH,MAAM,CAAC,IACnCgD,IAAI,CAACmD,WAAW,YAAYpC,OAAO,IAAID,MAAM,CAACd,IAAI,CAACmD,WAAW,EAAEnG,MAAM,CAAE,EACzE;MACA;MACA5B,IAAI,GAAGA,IAAI,CAACoJ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;IAC/B;IACA;IACApJ,IAAI,GAAGA,IAAI,CAACsJ,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC;EACvC;EACA,OAAO/I,KAAK,CAACD,MAAM,CAACN,IAAI,CAAC;AAC3B;AAEA,SACEtB,SAAS,IAAI6K,OAAO,EACpB3L,eAAe,EACfD,SAAS,EACTH,YAAY,EACZD,SAAS,EACToE,QAAQ", "ignoreList": []}