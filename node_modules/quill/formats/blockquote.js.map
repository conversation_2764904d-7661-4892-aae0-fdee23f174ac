{"version": 3, "file": "blockquote.js", "names": ["Block", "Blockquote", "blotName", "tagName"], "sources": ["../../src/formats/blockquote.ts"], "sourcesContent": ["import Block from '../blots/block.js';\n\nclass Blockquote extends Block {\n  static blotName = 'blockquote';\n  static tagName = 'blockquote';\n}\n\nexport default Blockquote;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AAErC,MAAMC,UAAU,SAASD,KAAK,CAAC;EAC7B,OAAOE,QAAQ,GAAG,YAAY;EAC9B,OAAOC,OAAO,GAAG,YAAY;AAC/B;AAEA,eAAeF,UAAU", "ignoreList": []}