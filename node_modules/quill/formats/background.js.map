{"version": 3, "file": "background.js", "names": ["ClassAttributor", "<PERSON><PERSON>", "ColorAttributor", "BackgroundClass", "scope", "INLINE", "BackgroundStyle"], "sources": ["../../src/formats/background.ts"], "sourcesContent": ["import { ClassAttributor, Scope } from 'parchment';\nimport { ColorAttributor } from './color.js';\n\nconst BackgroundClass = new ClassAttributor('background', 'ql-bg', {\n  scope: Scope.INLINE,\n});\nconst BackgroundStyle = new ColorAttributor('background', 'background-color', {\n  scope: Scope.INLINE,\n});\n\nexport { BackgroundClass, BackgroundStyle };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,QAAQ,WAAW;AAClD,SAASC,eAAe,QAAQ,YAAY;AAE5C,MAAMC,eAAe,GAAG,IAAIH,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE;EACjEI,KAAK,EAAEH,KAAK,CAACI;AACf,CAAC,CAAC;AACF,MAAMC,eAAe,GAAG,IAAIJ,eAAe,CAAC,YAAY,EAAE,kBAAkB,EAAE;EAC5EE,KAAK,EAAEH,KAAK,CAACI;AACf,CAAC,CAAC;AAEF,SAASF,eAAe,EAAEG,eAAe", "ignoreList": []}