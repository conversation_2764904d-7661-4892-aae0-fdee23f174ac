{"version": 3, "file": "bold.js", "names": ["Inline", "Bold", "blotName", "tagName", "create", "formats", "optimize", "context", "domNode", "statics", "replaceWith"], "sources": ["../../src/formats/bold.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Bold extends Inline {\n  static blotName = 'bold';\n  static tagName = ['STRONG', 'B'];\n\n  static create() {\n    return super.create();\n  }\n\n  static formats() {\n    return true;\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    if (this.domNode.tagName !== this.statics.tagName[0]) {\n      this.replaceWith(this.statics.blotName);\n    }\n  }\n}\n\nexport default Bold;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AAEvC,MAAMC,IAAI,SAASD,MAAM,CAAC;EACxB,OAAOE,QAAQ,GAAG,MAAM;EACxB,OAAOC,OAAO,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC;EAEhC,OAAOC,MAAMA,CAAA,EAAG;IACd,OAAO,KAAK,CAACA,MAAM,CAAC,CAAC;EACvB;EAEA,OAAOC,OAAOA,CAAA,EAAG;IACf,OAAO,IAAI;EACb;EAEAC,QAAQA,CAACC,OAA+B,EAAE;IACxC,KAAK,CAACD,QAAQ,CAACC,OAAO,CAAC;IACvB,IAAI,IAAI,CAACC,OAAO,CAACL,OAAO,KAAK,IAAI,CAACM,OAAO,CAACN,OAAO,CAAC,CAAC,CAAC,EAAE;MACpD,IAAI,CAACO,WAAW,CAAC,IAAI,CAACD,OAAO,CAACP,QAAQ,CAAC;IACzC;EACF;AACF;AAEA,eAAeD,IAAI", "ignoreList": []}