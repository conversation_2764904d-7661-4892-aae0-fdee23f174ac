{"version": 3, "file": "code.js", "names": ["Block", "Break", "<PERSON><PERSON><PERSON>", "Inline", "TextBlot", "escapeText", "Container", "<PERSON><PERSON><PERSON>", "CodeBlockContainer", "create", "value", "domNode", "setAttribute", "code", "index", "length", "children", "map", "child", "innerText", "join", "slice", "html", "CodeBlock", "TAB", "register", "Code", "blotName", "tagName", "className", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "default"], "sources": ["../../src/formats/code.ts"], "sourcesContent": ["import Block from '../blots/block.js';\nimport Break from '../blots/break.js';\nimport Cursor from '../blots/cursor.js';\nimport Inline from '../blots/inline.js';\nimport TextBlot, { escapeText } from '../blots/text.js';\nimport Container from '../blots/container.js';\nimport Quill from '../core/quill.js';\n\nclass CodeBlockContainer extends Container {\n  static create(value: string) {\n    const domNode = super.create(value) as Element;\n    domNode.setAttribute('spellcheck', 'false');\n    return domNode;\n  }\n\n  code(index: number, length: number) {\n    return (\n      this.children\n        // @ts-expect-error\n        .map((child) => (child.length() <= 1 ? '' : child.domNode.innerText))\n        .join('\\n')\n        .slice(index, index + length)\n    );\n  }\n\n  html(index: number, length: number) {\n    // `\\n`s are needed in order to support empty lines at the beginning and the end.\n    // https://html.spec.whatwg.org/multipage/syntax.html#element-restrictions\n    return `<pre>\\n${escapeText(this.code(index, length))}\\n</pre>`;\n  }\n}\n\nclass CodeBlock extends Block {\n  static TAB = '  ';\n\n  static register() {\n    Quill.register(CodeBlockContainer);\n  }\n}\n\nclass Code extends Inline {}\nCode.blotName = 'code';\nCode.tagName = 'CODE';\n\nCodeBlock.blotName = 'code-block';\nCodeBlock.className = 'ql-code-block';\nCodeBlock.tagName = 'DIV';\nCodeBlockContainer.blotName = 'code-block-container';\nCodeBlockContainer.className = 'ql-code-block-container';\nCodeBlockContainer.tagName = 'DIV';\n\nCodeBlockContainer.allowedChildren = [CodeBlock];\n\nCodeBlock.allowedChildren = [TextBlot, Break, Cursor];\nCodeBlock.requiredContainer = CodeBlockContainer;\n\nexport { Code, CodeBlockContainer, CodeBlock as default };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,QAAQ,IAAIC,UAAU,QAAQ,kBAAkB;AACvD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,MAAMC,kBAAkB,SAASF,SAAS,CAAC;EACzC,OAAOG,MAAMA,CAACC,KAAa,EAAE;IAC3B,MAAMC,OAAO,GAAG,KAAK,CAACF,MAAM,CAACC,KAAK,CAAY;IAC9CC,OAAO,CAACC,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC;IAC3C,OAAOD,OAAO;EAChB;EAEAE,IAAIA,CAACC,KAAa,EAAEC,MAAc,EAAE;IAClC,OACE,IAAI,CAACC;IACH;IAAA,CACCC,GAAG,CAAEC,KAAK,IAAMA,KAAK,CAACH,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAGG,KAAK,CAACP,OAAO,CAACQ,SAAU,CAAC,CACpEC,IAAI,CAAC,IAAI,CAAC,CACVC,KAAK,CAACP,KAAK,EAAEA,KAAK,GAAGC,MAAM,CAAC;EAEnC;EAEAO,IAAIA,CAACR,KAAa,EAAEC,MAAc,EAAE;IAClC;IACA;IACA,OAAQ,UAASV,UAAU,CAAC,IAAI,CAACQ,IAAI,CAACC,KAAK,EAAEC,MAAM,CAAC,CAAE,UAAS;EACjE;AACF;AAEA,MAAMQ,SAAS,SAASvB,KAAK,CAAC;EAC5B,OAAOwB,GAAG,GAAG,IAAI;EAEjB,OAAOC,QAAQA,CAAA,EAAG;IAChBlB,KAAK,CAACkB,QAAQ,CAACjB,kBAAkB,CAAC;EACpC;AACF;AAEA,MAAMkB,IAAI,SAASvB,MAAM,CAAC;AAC1BuB,IAAI,CAACC,QAAQ,GAAG,MAAM;AACtBD,IAAI,CAACE,OAAO,GAAG,MAAM;AAErBL,SAAS,CAACI,QAAQ,GAAG,YAAY;AACjCJ,SAAS,CAACM,SAAS,GAAG,eAAe;AACrCN,SAAS,CAACK,OAAO,GAAG,KAAK;AACzBpB,kBAAkB,CAACmB,QAAQ,GAAG,sBAAsB;AACpDnB,kBAAkB,CAACqB,SAAS,GAAG,yBAAyB;AACxDrB,kBAAkB,CAACoB,OAAO,GAAG,KAAK;AAElCpB,kBAAkB,CAACsB,eAAe,GAAG,CAACP,SAAS,CAAC;AAEhDA,SAAS,CAACO,eAAe,GAAG,CAAC1B,QAAQ,EAAEH,KAAK,EAAEC,MAAM,CAAC;AACrDqB,SAAS,CAACQ,iBAAiB,GAAGvB,kBAAkB;AAEhD,SAASkB,IAAI,EAAElB,kBAAkB,EAAEe,SAAS,IAAIS,OAAO", "ignoreList": []}