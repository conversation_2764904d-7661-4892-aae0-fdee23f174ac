{"version": 3, "file": "header.js", "names": ["Block", "Header", "blotName", "tagName", "formats", "domNode", "indexOf"], "sources": ["../../src/formats/header.ts"], "sourcesContent": ["import Block from '../blots/block.js';\n\nclass Header extends Block {\n  static blotName = 'header';\n  static tagName = ['H1', 'H2', 'H3', 'H4', 'H5', 'H6'];\n\n  static formats(domNode: Element) {\n    return this.tagName.indexOf(domNode.tagName) + 1;\n  }\n}\n\nexport default Header;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AAErC,MAAMC,MAAM,SAASD,KAAK,CAAC;EACzB,OAAOE,QAAQ,GAAG,QAAQ;EAC1B,OAAOC,OAAO,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAErD,OAAOC,OAAOA,CAACC,OAAgB,EAAE;IAC/B,OAAO,IAAI,CAACF,OAAO,CAACG,OAAO,CAACD,OAAO,CAACF,OAAO,CAAC,GAAG,CAAC;EAClD;AACF;AAEA,eAAeF,MAAM", "ignoreList": []}