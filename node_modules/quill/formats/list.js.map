{"version": 3, "file": "list.js", "names": ["Block", "Container", "<PERSON><PERSON><PERSON>", "ListContainer", "blotName", "tagName", "ListItem", "create", "value", "node", "setAttribute", "formats", "domNode", "getAttribute", "undefined", "register", "constructor", "scroll", "ui", "ownerDocument", "createElement", "listEventHandler", "e", "isEnabled", "format", "statics", "preventDefault", "addEventListener", "attachUI", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "default"], "sources": ["../../src/formats/list.ts"], "sourcesContent": ["import Block from '../blots/block.js';\nimport Container from '../blots/container.js';\nimport type Scroll from '../blots/scroll.js';\nimport Quill from '../core/quill.js';\n\nclass ListContainer extends Container {}\nListContainer.blotName = 'list-container';\nListContainer.tagName = 'OL';\n\nclass ListItem extends Block {\n  static create(value: string) {\n    const node = super.create() as HTMLElement;\n    node.setAttribute('data-list', value);\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    return domNode.getAttribute('data-list') || undefined;\n  }\n\n  static register() {\n    Quill.register(ListContainer);\n  }\n\n  constructor(scroll: Scroll, domNode: HTMLElement) {\n    super(scroll, domNode);\n    const ui = domNode.ownerDocument.createElement('span');\n    const listEventHandler = (e: Event) => {\n      if (!scroll.isEnabled()) return;\n      const format = this.statics.formats(domNode, scroll);\n      if (format === 'checked') {\n        this.format('list', 'unchecked');\n        e.preventDefault();\n      } else if (format === 'unchecked') {\n        this.format('list', 'checked');\n        e.preventDefault();\n      }\n    };\n    ui.addEventListener('mousedown', listEventHandler);\n    ui.addEventListener('touchstart', listEventHandler);\n    this.attachUI(ui);\n  }\n\n  format(name: string, value: string) {\n    if (name === this.statics.blotName && value) {\n      this.domNode.setAttribute('data-list', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n}\nListItem.blotName = 'list';\nListItem.tagName = 'LI';\n\nListContainer.allowedChildren = [ListItem];\nListItem.requiredContainer = ListContainer;\n\nexport { ListContainer, ListItem as default };\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,SAAS,MAAM,uBAAuB;AAE7C,OAAOC,KAAK,MAAM,kBAAkB;AAEpC,MAAMC,aAAa,SAASF,SAAS,CAAC;AACtCE,aAAa,CAACC,QAAQ,GAAG,gBAAgB;AACzCD,aAAa,CAACE,OAAO,GAAG,IAAI;AAE5B,MAAMC,QAAQ,SAASN,KAAK,CAAC;EAC3B,OAAOO,MAAMA,CAACC,KAAa,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAAC,CAAgB;IAC1CE,IAAI,CAACC,YAAY,CAAC,WAAW,EAAEF,KAAK,CAAC;IACrC,OAAOC,IAAI;EACb;EAEA,OAAOE,OAAOA,CAACC,OAAoB,EAAE;IACnC,OAAOA,OAAO,CAACC,YAAY,CAAC,WAAW,CAAC,IAAIC,SAAS;EACvD;EAEA,OAAOC,QAAQA,CAAA,EAAG;IAChBb,KAAK,CAACa,QAAQ,CAACZ,aAAa,CAAC;EAC/B;EAEAa,WAAWA,CAACC,MAAc,EAAEL,OAAoB,EAAE;IAChD,KAAK,CAACK,MAAM,EAAEL,OAAO,CAAC;IACtB,MAAMM,EAAE,GAAGN,OAAO,CAACO,aAAa,CAACC,aAAa,CAAC,MAAM,CAAC;IACtD,MAAMC,gBAAgB,GAAIC,CAAQ,IAAK;MACrC,IAAI,CAACL,MAAM,CAACM,SAAS,CAAC,CAAC,EAAE;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,OAAO,CAACd,OAAO,CAACC,OAAO,EAAEK,MAAM,CAAC;MACpD,IAAIO,MAAM,KAAK,SAAS,EAAE;QACxB,IAAI,CAACA,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC;QAChCF,CAAC,CAACI,cAAc,CAAC,CAAC;MACpB,CAAC,MAAM,IAAIF,MAAM,KAAK,WAAW,EAAE;QACjC,IAAI,CAACA,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC;QAC9BF,CAAC,CAACI,cAAc,CAAC,CAAC;MACpB;IACF,CAAC;IACDR,EAAE,CAACS,gBAAgB,CAAC,WAAW,EAAEN,gBAAgB,CAAC;IAClDH,EAAE,CAACS,gBAAgB,CAAC,YAAY,EAAEN,gBAAgB,CAAC;IACnD,IAAI,CAACO,QAAQ,CAACV,EAAE,CAAC;EACnB;EAEAM,MAAMA,CAACK,IAAY,EAAErB,KAAa,EAAE;IAClC,IAAIqB,IAAI,KAAK,IAAI,CAACJ,OAAO,CAACrB,QAAQ,IAAII,KAAK,EAAE;MAC3C,IAAI,CAACI,OAAO,CAACF,YAAY,CAAC,WAAW,EAAEF,KAAK,CAAC;IAC/C,CAAC,MAAM;MACL,KAAK,CAACgB,MAAM,CAACK,IAAI,EAAErB,KAAK,CAAC;IAC3B;EACF;AACF;AACAF,QAAQ,CAACF,QAAQ,GAAG,MAAM;AAC1BE,QAAQ,CAACD,OAAO,GAAG,IAAI;AAEvBF,aAAa,CAAC2B,eAAe,GAAG,CAACxB,QAAQ,CAAC;AAC1CA,QAAQ,CAACyB,iBAAiB,GAAG5B,aAAa;AAE1C,SAASA,aAAa,EAAEG,QAAQ,IAAI0B,OAAO", "ignoreList": []}