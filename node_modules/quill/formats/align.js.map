{"version": 3, "file": "align.js", "names": ["Attributor", "ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "config", "scope", "BLOCK", "whitelist", "AlignAttribute", "AlignClass", "AlignStyle"], "sources": ["../../src/formats/align.ts"], "sourcesContent": ["import { Attributor, ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst config = {\n  scope: Scope.BLOCK,\n  whitelist: ['right', 'center', 'justify'],\n};\n\nconst AlignAttribute = new Attributor('align', 'align', config);\nconst AlignClass = new ClassAttributor('align', 'ql-align', config);\nconst AlignStyle = new StyleAttributor('align', 'text-align', config);\n\nexport { AlignAttribute, AlignClass, AlignStyle };\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAE/E,MAAMC,MAAM,GAAG;EACbC,KAAK,EAAEH,KAAK,CAACI,KAAK;EAClBC,SAAS,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS;AAC1C,CAAC;AAED,MAAMC,cAAc,GAAG,IAAIR,UAAU,CAAC,OAAO,EAAE,OAAO,EAAEI,MAAM,CAAC;AAC/D,MAAMK,UAAU,GAAG,IAAIR,eAAe,CAAC,OAAO,EAAE,UAAU,EAAEG,MAAM,CAAC;AACnE,MAAMM,UAAU,GAAG,IAAIP,eAAe,CAAC,OAAO,EAAE,YAAY,EAAEC,MAAM,CAAC;AAErE,SAASI,cAAc,EAAEC,UAAU,EAAEC,UAAU", "ignoreList": []}