{"version": 3, "file": "size.js", "names": ["ClassAttributor", "<PERSON><PERSON>", "StyleAttributor", "SizeClass", "scope", "INLINE", "whitelist", "SizeStyle"], "sources": ["../../src/formats/size.ts"], "sourcesContent": ["import { ClassAttributor, Scope, StyleAttributor } from 'parchment';\n\nconst SizeClass = new ClassAttributor('size', 'ql-size', {\n  scope: Scope.INLINE,\n  whitelist: ['small', 'large', 'huge'],\n});\nconst SizeStyle = new StyleAttributor('size', 'font-size', {\n  scope: Scope.INLINE,\n  whitelist: ['10px', '18px', '32px'],\n});\n\nexport { SizeClass, SizeStyle };\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,EAAEC,eAAe,QAAQ,WAAW;AAEnE,MAAMC,SAAS,GAAG,IAAIH,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE;EACvDI,KAAK,EAAEH,KAAK,CAACI,MAAM;EACnBC,SAAS,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM;AACtC,CAAC,CAAC;AACF,MAAMC,SAAS,GAAG,IAAIL,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE;EACzDE,KAAK,EAAEH,KAAK,CAACI,MAAM;EACnBC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM;AACpC,CAAC,CAAC;AAEF,SAASH,SAAS,EAAEI,SAAS", "ignoreList": []}