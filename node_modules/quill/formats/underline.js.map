{"version": 3, "file": "underline.js", "names": ["Inline", "Underline", "blotName", "tagName"], "sources": ["../../src/formats/underline.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Underline extends Inline {\n  static blotName = 'underline';\n  static tagName = 'U';\n}\n\nexport default Underline;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AAEvC,MAAMC,SAAS,SAASD,MAAM,CAAC;EAC7B,OAAOE,QAAQ,GAAG,WAAW;EAC7B,OAAOC,OAAO,GAAG,GAAG;AACtB;AAEA,eAAeF,SAAS", "ignoreList": []}