{"version": 3, "file": "link.js", "names": ["Inline", "Link", "blotName", "tagName", "SANITIZED_URL", "PROTOCOL_WHITELIST", "create", "value", "node", "setAttribute", "sanitize", "formats", "domNode", "getAttribute", "url", "format", "name", "statics", "constructor", "protocols", "anchor", "document", "createElement", "href", "protocol", "slice", "indexOf", "default"], "sources": ["../../src/formats/link.ts"], "sourcesContent": ["import Inline from '../blots/inline.js';\n\nclass Link extends Inline {\n  static blotName = 'link';\n  static tagName = 'A';\n  static SANITIZED_URL = 'about:blank';\n  static PROTOCOL_WHITELIST = ['http', 'https', 'mailto', 'tel', 'sms'];\n\n  static create(value: string) {\n    const node = super.create(value) as HTMLElement;\n    node.setAttribute('href', this.sanitize(value));\n    node.setAttribute('rel', 'noopener noreferrer');\n    node.setAttribute('target', '_blank');\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    return domNode.getAttribute('href');\n  }\n\n  static sanitize(url: string) {\n    return sanitize(url, this.PROTOCOL_WHITELIST) ? url : this.SANITIZED_URL;\n  }\n\n  format(name: string, value: unknown) {\n    if (name !== this.statics.blotName || !value) {\n      super.format(name, value);\n    } else {\n      // @ts-expect-error\n      this.domNode.setAttribute('href', this.constructor.sanitize(value));\n    }\n  }\n}\n\nfunction sanitize(url: string, protocols: string[]) {\n  const anchor = document.createElement('a');\n  anchor.href = url;\n  const protocol = anchor.href.slice(0, anchor.href.indexOf(':'));\n  return protocols.indexOf(protocol) > -1;\n}\n\nexport { Link as default, sanitize };\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AAEvC,MAAMC,IAAI,SAASD,MAAM,CAAC;EACxB,OAAOE,QAAQ,GAAG,MAAM;EACxB,OAAOC,OAAO,GAAG,GAAG;EACpB,OAAOC,aAAa,GAAG,aAAa;EACpC,OAAOC,kBAAkB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC;EAErE,OAAOC,MAAMA,CAACC,KAAa,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,KAAK,CAAgB;IAC/CC,IAAI,CAACC,YAAY,CAAC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;IAC/CC,IAAI,CAACC,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC;IAC/CD,IAAI,CAACC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACrC,OAAOD,IAAI;EACb;EAEA,OAAOG,OAAOA,CAACC,OAAoB,EAAE;IACnC,OAAOA,OAAO,CAACC,YAAY,CAAC,MAAM,CAAC;EACrC;EAEA,OAAOH,QAAQA,CAACI,GAAW,EAAE;IAC3B,OAAOJ,QAAQ,CAACI,GAAG,EAAE,IAAI,CAACT,kBAAkB,CAAC,GAAGS,GAAG,GAAG,IAAI,CAACV,aAAa;EAC1E;EAEAW,MAAMA,CAACC,IAAY,EAAET,KAAc,EAAE;IACnC,IAAIS,IAAI,KAAK,IAAI,CAACC,OAAO,CAACf,QAAQ,IAAI,CAACK,KAAK,EAAE;MAC5C,KAAK,CAACQ,MAAM,CAACC,IAAI,EAAET,KAAK,CAAC;IAC3B,CAAC,MAAM;MACL;MACA,IAAI,CAACK,OAAO,CAACH,YAAY,CAAC,MAAM,EAAE,IAAI,CAACS,WAAW,CAACR,QAAQ,CAACH,KAAK,CAAC,CAAC;IACrE;EACF;AACF;AAEA,SAASG,QAAQA,CAACI,GAAW,EAAEK,SAAmB,EAAE;EAClD,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;EAC1CF,MAAM,CAACG,IAAI,GAAGT,GAAG;EACjB,MAAMU,QAAQ,GAAGJ,MAAM,CAACG,IAAI,CAACE,KAAK,CAAC,CAAC,EAAEL,MAAM,CAACG,IAAI,CAACG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC/D,OAAOP,SAAS,CAACO,OAAO,CAACF,QAAQ,CAAC,GAAG,CAAC,CAAC;AACzC;AAEA,SAASvB,IAAI,IAAI0B,OAAO,EAAEjB,QAAQ", "ignoreList": []}