{"version": 3, "file": "image.js", "names": ["EmbedBlot", "sanitize", "ATTRIBUTES", "Image", "blotName", "tagName", "create", "value", "node", "setAttribute", "formats", "domNode", "reduce", "attribute", "hasAttribute", "getAttribute", "match", "url", "test", "format", "name", "indexOf", "removeAttribute"], "sources": ["../../src/formats/image.ts"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\nimport { sanitize } from './link.js';\n\nconst ATTRIBUTES = ['alt', 'height', 'width'];\n\nclass Image extends EmbedBlot {\n  static blotName = 'image';\n  static tagName = 'IMG';\n\n  static create(value: string) {\n    const node = super.create(value) as Element;\n    if (typeof value === 'string') {\n      node.setAttribute('src', this.sanitize(value));\n    }\n    return node;\n  }\n\n  static formats(domNode: Element) {\n    return ATTRIBUTES.reduce(\n      (formats: Record<string, string | null>, attribute) => {\n        if (domNode.hasAttribute(attribute)) {\n          formats[attribute] = domNode.getAttribute(attribute);\n        }\n        return formats;\n      },\n      {},\n    );\n  }\n\n  static match(url: string) {\n    return /\\.(jpe?g|gif|png)$/.test(url) || /^data:image\\/.+;base64/.test(url);\n  }\n\n  static sanitize(url: string) {\n    return sanitize(url, ['http', 'https', 'data']) ? url : '//:0';\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('src');\n  }\n\n  domNode: HTMLImageElement;\n\n  format(name: string, value: string) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n}\n\nexport default Image;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,WAAW;AACrC,SAASC,QAAQ,QAAQ,WAAW;AAEpC,MAAMC,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;AAE7C,MAAMC,KAAK,SAASH,SAAS,CAAC;EAC5B,OAAOI,QAAQ,GAAG,OAAO;EACzB,OAAOC,OAAO,GAAG,KAAK;EAEtB,OAAOC,MAAMA,CAACC,KAAa,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,KAAK,CAAY;IAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BC,IAAI,CAACC,YAAY,CAAC,KAAK,EAAE,IAAI,CAACR,QAAQ,CAACM,KAAK,CAAC,CAAC;IAChD;IACA,OAAOC,IAAI;EACb;EAEA,OAAOE,OAAOA,CAACC,OAAgB,EAAE;IAC/B,OAAOT,UAAU,CAACU,MAAM,CACtB,CAACF,OAAsC,EAAEG,SAAS,KAAK;MACrD,IAAIF,OAAO,CAACG,YAAY,CAACD,SAAS,CAAC,EAAE;QACnCH,OAAO,CAACG,SAAS,CAAC,GAAGF,OAAO,CAACI,YAAY,CAACF,SAAS,CAAC;MACtD;MACA,OAAOH,OAAO;IAChB,CAAC,EACD,CAAC,CACH,CAAC;EACH;EAEA,OAAOM,KAAKA,CAACC,GAAW,EAAE;IACxB,OAAO,oBAAoB,CAACC,IAAI,CAACD,GAAG,CAAC,IAAI,wBAAwB,CAACC,IAAI,CAACD,GAAG,CAAC;EAC7E;EAEA,OAAOhB,QAAQA,CAACgB,GAAW,EAAE;IAC3B,OAAOhB,QAAQ,CAACgB,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,GAAGA,GAAG,GAAG,MAAM;EAChE;EAEA,OAAOV,KAAKA,CAACI,OAAgB,EAAE;IAC7B,OAAOA,OAAO,CAACI,YAAY,CAAC,KAAK,CAAC;EACpC;EAIAI,MAAMA,CAACC,IAAY,EAAEb,KAAa,EAAE;IAClC,IAAIL,UAAU,CAACmB,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MACjC,IAAIb,KAAK,EAAE;QACT,IAAI,CAACI,OAAO,CAACF,YAAY,CAACW,IAAI,EAAEb,KAAK,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAACI,OAAO,CAACW,eAAe,CAACF,IAAI,CAAC;MACpC;IACF,CAAC,MAAM;MACL,KAAK,CAACD,MAAM,CAACC,IAAI,EAAEb,KAAK,CAAC;IAC3B;EACF;AACF;AAEA,eAAeJ,KAAK", "ignoreList": []}