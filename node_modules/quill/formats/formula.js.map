{"version": 3, "file": "formula.js", "names": ["Embed", "Formula", "blotName", "className", "tagName", "create", "value", "window", "katex", "Error", "node", "render", "throwOnError", "errorColor", "setAttribute", "domNode", "getAttribute", "html", "formula"], "sources": ["../../src/formats/formula.ts"], "sourcesContent": ["import Embed from '../blots/embed.js';\n\nclass Formula extends Embed {\n  static blotName = 'formula';\n  static className = 'ql-formula';\n  static tagName = 'SPAN';\n\n  static create(value: string) {\n    // @ts-expect-error\n    if (window.katex == null) {\n      throw new Error('Formula module requires KaTeX.');\n    }\n    const node = super.create(value) as Element;\n    if (typeof value === 'string') {\n      // @ts-expect-error\n      window.katex.render(value, node, {\n        throwOnError: false,\n        errorColor: '#f00',\n      });\n      node.setAttribute('data-value', value);\n    }\n    return node;\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('data-value');\n  }\n\n  html() {\n    const { formula } = this.value();\n    return `<span>${formula}</span>`;\n  }\n}\n\nexport default Formula;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AAErC,MAAMC,OAAO,SAASD,KAAK,CAAC;EAC1B,OAAOE,QAAQ,GAAG,SAAS;EAC3B,OAAOC,SAAS,GAAG,YAAY;EAC/B,OAAOC,OAAO,GAAG,MAAM;EAEvB,OAAOC,MAAMA,CAACC,KAAa,EAAE;IAC3B;IACA,IAAIC,MAAM,CAACC,KAAK,IAAI,IAAI,EAAE;MACxB,MAAM,IAAIC,KAAK,CAAC,gCAAgC,CAAC;IACnD;IACA,MAAMC,IAAI,GAAG,KAAK,CAACL,MAAM,CAACC,KAAK,CAAY;IAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7B;MACAC,MAAM,CAACC,KAAK,CAACG,MAAM,CAACL,KAAK,EAAEI,IAAI,EAAE;QAC/BE,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE;MACd,CAAC,CAAC;MACFH,IAAI,CAACI,YAAY,CAAC,YAAY,EAAER,KAAK,CAAC;IACxC;IACA,OAAOI,IAAI;EACb;EAEA,OAAOJ,KAAKA,CAACS,OAAgB,EAAE;IAC7B,OAAOA,OAAO,CAACC,YAAY,CAAC,YAAY,CAAC;EAC3C;EAEAC,IAAIA,CAAA,EAAG;IACL,MAAM;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACZ,KAAK,CAAC,CAAC;IAChC,OAAQ,SAAQY,OAAQ,SAAQ;EAClC;AACF;AAEA,eAAejB,OAAO", "ignoreList": []}