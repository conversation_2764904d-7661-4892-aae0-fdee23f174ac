{"version": 3, "file": "indent.js", "names": ["ClassAttributor", "<PERSON><PERSON>", "IndentAttributor", "add", "node", "value", "normalizedValue", "indent", "remove", "toString", "canAdd", "parseInt", "undefined", "IndentClass", "scope", "BLOCK", "whitelist"], "sources": ["../../src/formats/indent.ts"], "sourcesContent": ["import { ClassAttributor, Scope } from 'parchment';\n\nclass IndentAttributor extends ClassAttributor {\n  add(node: HTMLElement, value: string | number) {\n    let normalizedValue = 0;\n    if (value === '+1' || value === '-1') {\n      const indent = this.value(node) || 0;\n      normalizedValue = value === '+1' ? indent + 1 : indent - 1;\n    } else if (typeof value === 'number') {\n      normalizedValue = value;\n    }\n    if (normalizedValue === 0) {\n      this.remove(node);\n      return true;\n    }\n    return super.add(node, normalizedValue.toString());\n  }\n\n  canAdd(node: HTMLElement, value: string) {\n    return super.canAdd(node, value) || super.canAdd(node, parseInt(value, 10));\n  }\n\n  value(node: HTMLElement) {\n    return parseInt(super.value(node), 10) || undefined; // Don't return NaN\n  }\n}\n\nconst IndentClass = new IndentAttributor('indent', 'ql-indent', {\n  scope: Scope.BLOCK,\n  // @ts-expect-error\n  whitelist: [1, 2, 3, 4, 5, 6, 7, 8],\n});\n\nexport default IndentClass;\n"], "mappings": "AAAA,SAASA,eAAe,EAAEC,KAAK,QAAQ,WAAW;AAElD,MAAMC,gBAAgB,SAASF,eAAe,CAAC;EAC7CG,GAAGA,CAACC,IAAiB,EAAEC,KAAsB,EAAE;IAC7C,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAID,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,IAAI,EAAE;MACpC,MAAME,MAAM,GAAG,IAAI,CAACF,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;MACpCE,eAAe,GAAGD,KAAK,KAAK,IAAI,GAAGE,MAAM,GAAG,CAAC,GAAGA,MAAM,GAAG,CAAC;IAC5D,CAAC,MAAM,IAAI,OAAOF,KAAK,KAAK,QAAQ,EAAE;MACpCC,eAAe,GAAGD,KAAK;IACzB;IACA,IAAIC,eAAe,KAAK,CAAC,EAAE;MACzB,IAAI,CAACE,MAAM,CAACJ,IAAI,CAAC;MACjB,OAAO,IAAI;IACb;IACA,OAAO,KAAK,CAACD,GAAG,CAACC,IAAI,EAAEE,eAAe,CAACG,QAAQ,CAAC,CAAC,CAAC;EACpD;EAEAC,MAAMA,CAACN,IAAiB,EAAEC,KAAa,EAAE;IACvC,OAAO,KAAK,CAACK,MAAM,CAACN,IAAI,EAAEC,KAAK,CAAC,IAAI,KAAK,CAACK,MAAM,CAACN,IAAI,EAAEO,QAAQ,CAACN,KAAK,EAAE,EAAE,CAAC,CAAC;EAC7E;EAEAA,KAAKA,CAACD,IAAiB,EAAE;IACvB,OAAOO,QAAQ,CAAC,KAAK,CAACN,KAAK,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,IAAIQ,SAAS,CAAC,CAAC;EACvD;AACF;AAEA,MAAMC,WAAW,GAAG,IAAIX,gBAAgB,CAAC,QAAQ,EAAE,WAAW,EAAE;EAC9DY,KAAK,EAAEb,KAAK,CAACc,KAAK;EAClB;EACAC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACpC,CAAC,CAAC;AAEF,eAAeH,WAAW", "ignoreList": []}