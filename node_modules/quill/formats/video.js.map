{"version": 3, "file": "video.js", "names": ["BlockEmbed", "Link", "ATTRIBUTES", "Video", "blotName", "className", "tagName", "create", "value", "node", "setAttribute", "sanitize", "formats", "domNode", "reduce", "attribute", "hasAttribute", "getAttribute", "url", "format", "name", "indexOf", "removeAttribute", "html", "video"], "sources": ["../../src/formats/video.ts"], "sourcesContent": ["import { BlockEmbed } from '../blots/block.js';\nimport Link from './link.js';\n\nconst ATTRIBUTES = ['height', 'width'];\n\nclass Video extends BlockEmbed {\n  static blotName = 'video';\n  static className = 'ql-video';\n  static tagName = 'IFRAME';\n\n  static create(value: string) {\n    const node = super.create(value) as Element;\n    node.setAttribute('frameborder', '0');\n    node.setAttribute('allowfullscreen', 'true');\n    node.setAttribute('src', this.sanitize(value));\n    return node;\n  }\n\n  static formats(domNode: Element) {\n    return ATTRIBUTES.reduce(\n      (formats: Record<string, string | null>, attribute) => {\n        if (domNode.hasAttribute(attribute)) {\n          formats[attribute] = domNode.getAttribute(attribute);\n        }\n        return formats;\n      },\n      {},\n    );\n  }\n\n  static sanitize(url: string) {\n    return Link.sanitize(url);\n  }\n\n  static value(domNode: Element) {\n    return domNode.getAttribute('src');\n  }\n\n  domNode: HTMLVideoElement;\n\n  format(name: string, value: string) {\n    if (ATTRIBUTES.indexOf(name) > -1) {\n      if (value) {\n        this.domNode.setAttribute(name, value);\n      } else {\n        this.domNode.removeAttribute(name);\n      }\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  html() {\n    const { video } = this.value();\n    return `<a href=\"${video}\">${video}</a>`;\n  }\n}\n\nexport default Video;\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,IAAI,MAAM,WAAW;AAE5B,MAAMC,UAAU,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC;AAEtC,MAAMC,KAAK,SAASH,UAAU,CAAC;EAC7B,OAAOI,QAAQ,GAAG,OAAO;EACzB,OAAOC,SAAS,GAAG,UAAU;EAC7B,OAAOC,OAAO,GAAG,QAAQ;EAEzB,OAAOC,MAAMA,CAACC,KAAa,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAACC,KAAK,CAAY;IAC3CC,IAAI,CAACC,YAAY,CAAC,aAAa,EAAE,GAAG,CAAC;IACrCD,IAAI,CAACC,YAAY,CAAC,iBAAiB,EAAE,MAAM,CAAC;IAC5CD,IAAI,CAACC,YAAY,CAAC,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACH,KAAK,CAAC,CAAC;IAC9C,OAAOC,IAAI;EACb;EAEA,OAAOG,OAAOA,CAACC,OAAgB,EAAE;IAC/B,OAAOX,UAAU,CAACY,MAAM,CACtB,CAACF,OAAsC,EAAEG,SAAS,KAAK;MACrD,IAAIF,OAAO,CAACG,YAAY,CAACD,SAAS,CAAC,EAAE;QACnCH,OAAO,CAACG,SAAS,CAAC,GAAGF,OAAO,CAACI,YAAY,CAACF,SAAS,CAAC;MACtD;MACA,OAAOH,OAAO;IAChB,CAAC,EACD,CAAC,CACH,CAAC;EACH;EAEA,OAAOD,QAAQA,CAACO,GAAW,EAAE;IAC3B,OAAOjB,IAAI,CAACU,QAAQ,CAACO,GAAG,CAAC;EAC3B;EAEA,OAAOV,KAAKA,CAACK,OAAgB,EAAE;IAC7B,OAAOA,OAAO,CAACI,YAAY,CAAC,KAAK,CAAC;EACpC;EAIAE,MAAMA,CAACC,IAAY,EAAEZ,KAAa,EAAE;IAClC,IAAIN,UAAU,CAACmB,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MACjC,IAAIZ,KAAK,EAAE;QACT,IAAI,CAACK,OAAO,CAACH,YAAY,CAACU,IAAI,EAAEZ,KAAK,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAACK,OAAO,CAACS,eAAe,CAACF,IAAI,CAAC;MACpC;IACF,CAAC,MAAM;MACL,KAAK,CAACD,MAAM,CAACC,IAAI,EAAEZ,KAAK,CAAC;IAC3B;EACF;EAEAe,IAAIA,CAAA,EAAG;IACL,MAAM;MAAEC;IAAM,CAAC,GAAG,IAAI,CAAChB,KAAK,CAAC,CAAC;IAC9B,OAAQ,YAAWgB,KAAM,KAAIA,KAAM,MAAK;EAC1C;AACF;AAEA,eAAerB,KAAK", "ignoreList": []}