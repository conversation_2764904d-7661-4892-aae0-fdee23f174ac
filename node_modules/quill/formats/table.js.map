{"version": 3, "file": "table.js", "names": ["Block", "Container", "TableCell", "blotName", "tagName", "create", "value", "node", "setAttribute", "tableId", "formats", "domNode", "hasAttribute", "getAttribute", "undefined", "cellOffset", "parent", "children", "indexOf", "format", "name", "row", "rowOffset", "table", "TableRow", "checkMerge", "next", "head", "thisHead", "thisTail", "tail", "nextHead", "nextTail", "optimize", "context", "for<PERSON>ach", "child", "childFormats", "nextFormats", "splitAfter", "prev", "TableBody", "TableContainer", "balanceCells", "rows", "descendants", "maxColumns", "reduce", "max", "Math", "length", "Array", "fill", "blot", "scroll", "append<PERSON><PERSON><PERSON>", "cells", "column", "map", "at", "deleteColumn", "index", "body", "descendant", "cell", "remove", "insertColumn", "ref", "insertBefore", "insertRow", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requiredC<PERSON><PERSON>", "random", "toString", "slice"], "sources": ["../../src/formats/table.ts"], "sourcesContent": ["import type { LinkedList } from 'parchment';\nimport Block from '../blots/block.js';\nimport Container from '../blots/container.js';\n\nclass TableCell extends Block {\n  static blotName = 'table';\n  static tagName = 'TD';\n\n  static create(value: string) {\n    const node = super.create() as HTMLElement;\n    if (value) {\n      node.setAttribute('data-row', value);\n    } else {\n      node.setAttribute('data-row', tableId());\n    }\n    return node;\n  }\n\n  static formats(domNode: HTMLElement) {\n    if (domNode.hasAttribute('data-row')) {\n      return domNode.getAttribute('data-row');\n    }\n    return undefined;\n  }\n\n  next: this | null;\n\n  cellOffset() {\n    if (this.parent) {\n      return this.parent.children.indexOf(this);\n    }\n    return -1;\n  }\n\n  format(name: string, value: string) {\n    if (name === TableCell.blotName && value) {\n      this.domNode.setAttribute('data-row', value);\n    } else {\n      super.format(name, value);\n    }\n  }\n\n  row(): TableRow {\n    return this.parent as TableRow;\n  }\n\n  rowOffset() {\n    if (this.row()) {\n      return this.row().rowOffset();\n    }\n    return -1;\n  }\n\n  table() {\n    return this.row() && this.row().table();\n  }\n}\n\nclass TableRow extends Container {\n  static blotName = 'table-row';\n  static tagName = 'TR';\n\n  children: LinkedList<TableCell>;\n  next: this | null;\n\n  checkMerge() {\n    // @ts-expect-error\n    if (super.checkMerge() && this.next.children.head != null) {\n      // @ts-expect-error\n      const thisHead = this.children.head.formats();\n      // @ts-expect-error\n      const thisTail = this.children.tail.formats();\n      // @ts-expect-error\n      const nextHead = this.next.children.head.formats();\n      // @ts-expect-error\n      const nextTail = this.next.children.tail.formats();\n      return (\n        thisHead.table === thisTail.table &&\n        thisHead.table === nextHead.table &&\n        thisHead.table === nextTail.table\n      );\n    }\n    return false;\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    this.children.forEach((child) => {\n      if (child.next == null) return;\n      const childFormats = child.formats();\n      const nextFormats = child.next.formats();\n      if (childFormats.table !== nextFormats.table) {\n        const next = this.splitAfter(child);\n        if (next) {\n          // @ts-expect-error TODO: parameters of optimize() should be a optional\n          next.optimize();\n        }\n        // We might be able to merge with prev now\n        if (this.prev) {\n          // @ts-expect-error TODO: parameters of optimize() should be a optional\n          this.prev.optimize();\n        }\n      }\n    });\n  }\n\n  rowOffset() {\n    if (this.parent) {\n      return this.parent.children.indexOf(this);\n    }\n    return -1;\n  }\n\n  table() {\n    return this.parent && this.parent.parent;\n  }\n}\n\nclass TableBody extends Container {\n  static blotName = 'table-body';\n  static tagName = 'TBODY';\n\n  children: LinkedList<TableRow>;\n}\n\nclass TableContainer extends Container {\n  static blotName = 'table-container';\n  static tagName = 'TABLE';\n\n  children: LinkedList<TableBody>;\n\n  balanceCells() {\n    const rows = this.descendants(TableRow);\n    const maxColumns = rows.reduce((max, row) => {\n      return Math.max(row.children.length, max);\n    }, 0);\n    rows.forEach((row) => {\n      new Array(maxColumns - row.children.length).fill(0).forEach(() => {\n        let value;\n        if (row.children.head != null) {\n          value = TableCell.formats(row.children.head.domNode);\n        }\n        const blot = this.scroll.create(TableCell.blotName, value);\n        row.appendChild(blot);\n        // @ts-expect-error TODO: parameters of optimize() should be a optional\n        blot.optimize(); // Add break blot\n      });\n    });\n  }\n\n  cells(column: number) {\n    return this.rows().map((row) => row.children.at(column));\n  }\n\n  deleteColumn(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    body.children.forEach((row) => {\n      const cell = row.children.at(index);\n      if (cell != null) {\n        cell.remove();\n      }\n    });\n  }\n\n  insertColumn(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    body.children.forEach((row) => {\n      const ref = row.children.at(index);\n      // @ts-expect-error\n      const value = TableCell.formats(row.children.head.domNode);\n      const cell = this.scroll.create(TableCell.blotName, value);\n      row.insertBefore(cell, ref);\n    });\n  }\n\n  insertRow(index: number) {\n    // @ts-expect-error\n    const [body] = this.descendant(TableBody) as TableBody[];\n    if (body == null || body.children.head == null) return;\n    const id = tableId();\n    const row = this.scroll.create(TableRow.blotName) as TableRow;\n    body.children.head.children.forEach(() => {\n      const cell = this.scroll.create(TableCell.blotName, id);\n      row.appendChild(cell);\n    });\n    const ref = body.children.at(index);\n    body.insertBefore(row, ref);\n  }\n\n  rows() {\n    const body = this.children.head;\n    if (body == null) return [];\n    return body.children.map((row) => row);\n  }\n}\n\nTableContainer.allowedChildren = [TableBody];\nTableBody.requiredContainer = TableContainer;\n\nTableBody.allowedChildren = [TableRow];\nTableRow.requiredContainer = TableBody;\n\nTableRow.allowedChildren = [TableCell];\nTableCell.requiredContainer = TableRow;\n\nfunction tableId() {\n  const id = Math.random().toString(36).slice(2, 6);\n  return `row-${id}`;\n}\n\nexport { TableCell, TableRow, TableBody, TableContainer, tableId };\n"], "mappings": "AACA,OAAOA,KAAK,MAAM,mBAAmB;AACrC,OAAOC,SAAS,MAAM,uBAAuB;AAE7C,MAAMC,SAAS,SAASF,KAAK,CAAC;EAC5B,OAAOG,QAAQ,GAAG,OAAO;EACzB,OAAOC,OAAO,GAAG,IAAI;EAErB,OAAOC,MAAMA,CAACC,KAAa,EAAE;IAC3B,MAAMC,IAAI,GAAG,KAAK,CAACF,MAAM,CAAC,CAAgB;IAC1C,IAAIC,KAAK,EAAE;MACTC,IAAI,CAACC,YAAY,CAAC,UAAU,EAAEF,KAAK,CAAC;IACtC,CAAC,MAAM;MACLC,IAAI,CAACC,YAAY,CAAC,UAAU,EAAEC,OAAO,CAAC,CAAC,CAAC;IAC1C;IACA,OAAOF,IAAI;EACb;EAEA,OAAOG,OAAOA,CAACC,OAAoB,EAAE;IACnC,IAAIA,OAAO,CAACC,YAAY,CAAC,UAAU,CAAC,EAAE;MACpC,OAAOD,OAAO,CAACE,YAAY,CAAC,UAAU,CAAC;IACzC;IACA,OAAOC,SAAS;EAClB;EAIAC,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACC,MAAM,EAAE;MACf,OAAO,IAAI,CAACA,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC,CAAC;EACX;EAEAC,MAAMA,CAACC,IAAY,EAAEd,KAAa,EAAE;IAClC,IAAIc,IAAI,KAAKlB,SAAS,CAACC,QAAQ,IAAIG,KAAK,EAAE;MACxC,IAAI,CAACK,OAAO,CAACH,YAAY,CAAC,UAAU,EAAEF,KAAK,CAAC;IAC9C,CAAC,MAAM;MACL,KAAK,CAACa,MAAM,CAACC,IAAI,EAAEd,KAAK,CAAC;IAC3B;EACF;EAEAe,GAAGA,CAAA,EAAa;IACd,OAAO,IAAI,CAACL,MAAM;EACpB;EAEAM,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAACD,GAAG,CAAC,CAAC,EAAE;MACd,OAAO,IAAI,CAACA,GAAG,CAAC,CAAC,CAACC,SAAS,CAAC,CAAC;IAC/B;IACA,OAAO,CAAC,CAAC;EACX;EAEAC,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACF,GAAG,CAAC,CAAC,IAAI,IAAI,CAACA,GAAG,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;EACzC;AACF;AAEA,MAAMC,QAAQ,SAASvB,SAAS,CAAC;EAC/B,OAAOE,QAAQ,GAAG,WAAW;EAC7B,OAAOC,OAAO,GAAG,IAAI;EAKrBqB,UAAUA,CAAA,EAAG;IACX;IACA,IAAI,KAAK,CAACA,UAAU,CAAC,CAAC,IAAI,IAAI,CAACC,IAAI,CAACT,QAAQ,CAACU,IAAI,IAAI,IAAI,EAAE;MACzD;MACA,MAAMC,QAAQ,GAAG,IAAI,CAACX,QAAQ,CAACU,IAAI,CAACjB,OAAO,CAAC,CAAC;MAC7C;MACA,MAAMmB,QAAQ,GAAG,IAAI,CAACZ,QAAQ,CAACa,IAAI,CAACpB,OAAO,CAAC,CAAC;MAC7C;MACA,MAAMqB,QAAQ,GAAG,IAAI,CAACL,IAAI,CAACT,QAAQ,CAACU,IAAI,CAACjB,OAAO,CAAC,CAAC;MAClD;MACA,MAAMsB,QAAQ,GAAG,IAAI,CAACN,IAAI,CAACT,QAAQ,CAACa,IAAI,CAACpB,OAAO,CAAC,CAAC;MAClD,OACEkB,QAAQ,CAACL,KAAK,KAAKM,QAAQ,CAACN,KAAK,IACjCK,QAAQ,CAACL,KAAK,KAAKQ,QAAQ,CAACR,KAAK,IACjCK,QAAQ,CAACL,KAAK,KAAKS,QAAQ,CAACT,KAAK;IAErC;IACA,OAAO,KAAK;EACd;EAEAU,QAAQA,CAACC,OAA+B,EAAE;IACxC,KAAK,CAACD,QAAQ,CAACC,OAAO,CAAC;IACvB,IAAI,CAACjB,QAAQ,CAACkB,OAAO,CAAEC,KAAK,IAAK;MAC/B,IAAIA,KAAK,CAACV,IAAI,IAAI,IAAI,EAAE;MACxB,MAAMW,YAAY,GAAGD,KAAK,CAAC1B,OAAO,CAAC,CAAC;MACpC,MAAM4B,WAAW,GAAGF,KAAK,CAACV,IAAI,CAAChB,OAAO,CAAC,CAAC;MACxC,IAAI2B,YAAY,CAACd,KAAK,KAAKe,WAAW,CAACf,KAAK,EAAE;QAC5C,MAAMG,IAAI,GAAG,IAAI,CAACa,UAAU,CAACH,KAAK,CAAC;QACnC,IAAIV,IAAI,EAAE;UACR;UACAA,IAAI,CAACO,QAAQ,CAAC,CAAC;QACjB;QACA;QACA,IAAI,IAAI,CAACO,IAAI,EAAE;UACb;UACA,IAAI,CAACA,IAAI,CAACP,QAAQ,CAAC,CAAC;QACtB;MACF;IACF,CAAC,CAAC;EACJ;EAEAX,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAACN,MAAM,EAAE;MACf,OAAO,IAAI,CAACA,MAAM,CAACC,QAAQ,CAACC,OAAO,CAAC,IAAI,CAAC;IAC3C;IACA,OAAO,CAAC,CAAC;EACX;EAEAK,KAAKA,CAAA,EAAG;IACN,OAAO,IAAI,CAACP,MAAM,IAAI,IAAI,CAACA,MAAM,CAACA,MAAM;EAC1C;AACF;AAEA,MAAMyB,SAAS,SAASxC,SAAS,CAAC;EAChC,OAAOE,QAAQ,GAAG,YAAY;EAC9B,OAAOC,OAAO,GAAG,OAAO;AAG1B;AAEA,MAAMsC,cAAc,SAASzC,SAAS,CAAC;EACrC,OAAOE,QAAQ,GAAG,iBAAiB;EACnC,OAAOC,OAAO,GAAG,OAAO;EAIxBuC,YAAYA,CAAA,EAAG;IACb,MAAMC,IAAI,GAAG,IAAI,CAACC,WAAW,CAACrB,QAAQ,CAAC;IACvC,MAAMsB,UAAU,GAAGF,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAE3B,GAAG,KAAK;MAC3C,OAAO4B,IAAI,CAACD,GAAG,CAAC3B,GAAG,CAACJ,QAAQ,CAACiC,MAAM,EAAEF,GAAG,CAAC;IAC3C,CAAC,EAAE,CAAC,CAAC;IACLJ,IAAI,CAACT,OAAO,CAAEd,GAAG,IAAK;MACpB,IAAI8B,KAAK,CAACL,UAAU,GAAGzB,GAAG,CAACJ,QAAQ,CAACiC,MAAM,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC,CAACjB,OAAO,CAAC,MAAM;QAChE,IAAI7B,KAAK;QACT,IAAIe,GAAG,CAACJ,QAAQ,CAACU,IAAI,IAAI,IAAI,EAAE;UAC7BrB,KAAK,GAAGJ,SAAS,CAACQ,OAAO,CAACW,GAAG,CAACJ,QAAQ,CAACU,IAAI,CAAChB,OAAO,CAAC;QACtD;QACA,MAAM0C,IAAI,GAAG,IAAI,CAACC,MAAM,CAACjD,MAAM,CAACH,SAAS,CAACC,QAAQ,EAAEG,KAAK,CAAC;QAC1De,GAAG,CAACkC,WAAW,CAACF,IAAI,CAAC;QACrB;QACAA,IAAI,CAACpB,QAAQ,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAuB,KAAKA,CAACC,MAAc,EAAE;IACpB,OAAO,IAAI,CAACb,IAAI,CAAC,CAAC,CAACc,GAAG,CAAErC,GAAG,IAAKA,GAAG,CAACJ,QAAQ,CAAC0C,EAAE,CAACF,MAAM,CAAC,CAAC;EAC1D;EAEAG,YAAYA,CAACC,KAAa,EAAE;IAC1B;IACA,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,UAAU,CAACtB,SAAS,CAAgB;IACxD,IAAIqB,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC7C,QAAQ,CAACU,IAAI,IAAI,IAAI,EAAE;IAChDmC,IAAI,CAAC7C,QAAQ,CAACkB,OAAO,CAAEd,GAAG,IAAK;MAC7B,MAAM2C,IAAI,GAAG3C,GAAG,CAACJ,QAAQ,CAAC0C,EAAE,CAACE,KAAK,CAAC;MACnC,IAAIG,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,CAACC,MAAM,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAACL,KAAa,EAAE;IAC1B;IACA,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,UAAU,CAACtB,SAAS,CAAgB;IACxD,IAAIqB,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC7C,QAAQ,CAACU,IAAI,IAAI,IAAI,EAAE;IAChDmC,IAAI,CAAC7C,QAAQ,CAACkB,OAAO,CAAEd,GAAG,IAAK;MAC7B,MAAM8C,GAAG,GAAG9C,GAAG,CAACJ,QAAQ,CAAC0C,EAAE,CAACE,KAAK,CAAC;MAClC;MACA,MAAMvD,KAAK,GAAGJ,SAAS,CAACQ,OAAO,CAACW,GAAG,CAACJ,QAAQ,CAACU,IAAI,CAAChB,OAAO,CAAC;MAC1D,MAAMqD,IAAI,GAAG,IAAI,CAACV,MAAM,CAACjD,MAAM,CAACH,SAAS,CAACC,QAAQ,EAAEG,KAAK,CAAC;MAC1De,GAAG,CAAC+C,YAAY,CAACJ,IAAI,EAAEG,GAAG,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAE,SAASA,CAACR,KAAa,EAAE;IACvB;IACA,MAAM,CAACC,IAAI,CAAC,GAAG,IAAI,CAACC,UAAU,CAACtB,SAAS,CAAgB;IACxD,IAAIqB,IAAI,IAAI,IAAI,IAAIA,IAAI,CAAC7C,QAAQ,CAACU,IAAI,IAAI,IAAI,EAAE;IAChD,MAAM2C,EAAE,GAAG7D,OAAO,CAAC,CAAC;IACpB,MAAMY,GAAG,GAAG,IAAI,CAACiC,MAAM,CAACjD,MAAM,CAACmB,QAAQ,CAACrB,QAAQ,CAAa;IAC7D2D,IAAI,CAAC7C,QAAQ,CAACU,IAAI,CAACV,QAAQ,CAACkB,OAAO,CAAC,MAAM;MACxC,MAAM6B,IAAI,GAAG,IAAI,CAACV,MAAM,CAACjD,MAAM,CAACH,SAAS,CAACC,QAAQ,EAAEmE,EAAE,CAAC;MACvDjD,GAAG,CAACkC,WAAW,CAACS,IAAI,CAAC;IACvB,CAAC,CAAC;IACF,MAAMG,GAAG,GAAGL,IAAI,CAAC7C,QAAQ,CAAC0C,EAAE,CAACE,KAAK,CAAC;IACnCC,IAAI,CAACM,YAAY,CAAC/C,GAAG,EAAE8C,GAAG,CAAC;EAC7B;EAEAvB,IAAIA,CAAA,EAAG;IACL,MAAMkB,IAAI,GAAG,IAAI,CAAC7C,QAAQ,CAACU,IAAI;IAC/B,IAAImC,IAAI,IAAI,IAAI,EAAE,OAAO,EAAE;IAC3B,OAAOA,IAAI,CAAC7C,QAAQ,CAACyC,GAAG,CAAErC,GAAG,IAAKA,GAAG,CAAC;EACxC;AACF;AAEAqB,cAAc,CAAC6B,eAAe,GAAG,CAAC9B,SAAS,CAAC;AAC5CA,SAAS,CAAC+B,iBAAiB,GAAG9B,cAAc;AAE5CD,SAAS,CAAC8B,eAAe,GAAG,CAAC/C,QAAQ,CAAC;AACtCA,QAAQ,CAACgD,iBAAiB,GAAG/B,SAAS;AAEtCjB,QAAQ,CAAC+C,eAAe,GAAG,CAACrE,SAAS,CAAC;AACtCA,SAAS,CAACsE,iBAAiB,GAAGhD,QAAQ;AAEtC,SAASf,OAAOA,CAAA,EAAG;EACjB,MAAM6D,EAAE,GAAGrB,IAAI,CAACwB,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD,OAAQ,OAAML,EAAG,EAAC;AACpB;AAEA,SAASpE,SAAS,EAAEsB,QAAQ,EAAEiB,SAAS,EAAEC,cAAc,EAAEjC,OAAO", "ignoreList": []}