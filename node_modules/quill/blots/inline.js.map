{"version": 3, "file": "inline.js", "names": ["EmbedBlot", "InlineBlot", "<PERSON><PERSON>", "Break", "Text", "Inline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order", "compare", "self", "other", "selfIndex", "indexOf", "otherIndex", "formatAt", "index", "length", "name", "value", "statics", "blotName", "scroll", "query", "BLOT", "blot", "isolate", "wrap", "optimize", "context", "parent", "offset", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../src/blots/inline.ts"], "sourcesContent": ["import { EmbedBlot, InlineBlot, Scope } from 'parchment';\nimport type { BlotConstructor } from 'parchment';\nimport Break from './break.js';\nimport Text from './text.js';\n\nclass Inline extends InlineBlot {\n  static allowedChildren: BlotConstructor[] = [Inline, Break, EmbedBlot, Text];\n  // Lower index means deeper in the DOM tree, since not found (-1) is for embeds\n  static order = [\n    'cursor',\n    'inline', // Must be lower\n    'link', // Chrome wants <a> to be lower\n    'underline',\n    'strike',\n    'italic',\n    'bold',\n    'script',\n    'code', // Must be higher\n  ];\n\n  static compare(self: string, other: string) {\n    const selfIndex = Inline.order.indexOf(self);\n    const otherIndex = Inline.order.indexOf(other);\n    if (selfIndex >= 0 || otherIndex >= 0) {\n      return selfIndex - otherIndex;\n    }\n    if (self === other) {\n      return 0;\n    }\n    if (self < other) {\n      return -1;\n    }\n    return 1;\n  }\n\n  formatAt(index: number, length: number, name: string, value: unknown) {\n    if (\n      Inline.compare(this.statics.blotName, name) < 0 &&\n      this.scroll.query(name, Scope.BLOT)\n    ) {\n      const blot = this.isolate(index, length);\n      if (value) {\n        blot.wrap(name, value);\n      }\n    } else {\n      super.formatAt(index, length, name, value);\n    }\n  }\n\n  optimize(context: { [key: string]: any }) {\n    super.optimize(context);\n    if (\n      this.parent instanceof Inline &&\n      Inline.compare(this.statics.blotName, this.parent.statics.blotName) > 0\n    ) {\n      const parent = this.parent.isolate(this.offset(), this.length());\n      // @ts-expect-error TODO: make isolate generic\n      this.moveChildren(parent);\n      parent.wrap(this);\n    }\n  }\n}\n\nexport default Inline;\n"], "mappings": "AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,KAAK,QAAQ,WAAW;AAExD,OAAOC,KAAK,MAAM,YAAY;AAC9B,OAAOC,IAAI,MAAM,WAAW;AAE5B,MAAMC,MAAM,SAASJ,UAAU,CAAC;EAC9B,OAAOK,eAAe,GAAsB,CAACD,MAAM,EAAEF,KAAK,EAAEH,SAAS,EAAEI,IAAI,CAAC;EAC5E;EACA,OAAOG,KAAK,GAAG,CACb,QAAQ,EACR,QAAQ;EAAE;EACV,MAAM;EAAE;EACR,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,CAAE;EAAA,CACT;EAED,OAAOC,OAAOA,CAACC,IAAY,EAAEC,KAAa,EAAE;IAC1C,MAAMC,SAAS,GAAGN,MAAM,CAACE,KAAK,CAACK,OAAO,CAACH,IAAI,CAAC;IAC5C,MAAMI,UAAU,GAAGR,MAAM,CAACE,KAAK,CAACK,OAAO,CAACF,KAAK,CAAC;IAC9C,IAAIC,SAAS,IAAI,CAAC,IAAIE,UAAU,IAAI,CAAC,EAAE;MACrC,OAAOF,SAAS,GAAGE,UAAU;IAC/B;IACA,IAAIJ,IAAI,KAAKC,KAAK,EAAE;MAClB,OAAO,CAAC;IACV;IACA,IAAID,IAAI,GAAGC,KAAK,EAAE;MAChB,OAAO,CAAC,CAAC;IACX;IACA,OAAO,CAAC;EACV;EAEAI,QAAQA,CAACC,KAAa,EAAEC,MAAc,EAAEC,IAAY,EAAEC,KAAc,EAAE;IACpE,IACEb,MAAM,CAACG,OAAO,CAAC,IAAI,CAACW,OAAO,CAACC,QAAQ,EAAEH,IAAI,CAAC,GAAG,CAAC,IAC/C,IAAI,CAACI,MAAM,CAACC,KAAK,CAACL,IAAI,EAAEf,KAAK,CAACqB,IAAI,CAAC,EACnC;MACA,MAAMC,IAAI,GAAG,IAAI,CAACC,OAAO,CAACV,KAAK,EAAEC,MAAM,CAAC;MACxC,IAAIE,KAAK,EAAE;QACTM,IAAI,CAACE,IAAI,CAACT,IAAI,EAAEC,KAAK,CAAC;MACxB;IACF,CAAC,MAAM;MACL,KAAK,CAACJ,QAAQ,CAACC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,CAAC;IAC5C;EACF;EAEAS,QAAQA,CAACC,OAA+B,EAAE;IACxC,KAAK,CAACD,QAAQ,CAACC,OAAO,CAAC;IACvB,IACE,IAAI,CAACC,MAAM,YAAYxB,MAAM,IAC7BA,MAAM,CAACG,OAAO,CAAC,IAAI,CAACW,OAAO,CAACC,QAAQ,EAAE,IAAI,CAACS,MAAM,CAACV,OAAO,CAACC,QAAQ,CAAC,GAAG,CAAC,EACvE;MACA,MAAMS,MAAM,GAAG,IAAI,CAACA,MAAM,CAACJ,OAAO,CAAC,IAAI,CAACK,MAAM,CAAC,CAAC,EAAE,IAAI,CAACd,MAAM,CAAC,CAAC,CAAC;MAChE;MACA,IAAI,CAACe,YAAY,CAACF,MAAM,CAAC;MACzBA,MAAM,CAACH,IAAI,CAAC,IAAI,CAAC;IACnB;EACF;AACF;AAEA,eAAerB,MAAM", "ignoreList": []}