{"version": 3, "file": "embed.js", "names": ["EmbedBlot", "TextBlot", "GUARD_TEXT", "Embed", "constructor", "scroll", "node", "contentNode", "document", "createElement", "setAttribute", "Array", "from", "domNode", "childNodes", "for<PERSON>ach", "childNode", "append<PERSON><PERSON><PERSON>", "leftGuard", "createTextNode", "<PERSON><PERSON><PERSON>", "index", "offset", "restore", "range", "textNode", "text", "data", "split", "join", "prev", "prevLength", "length", "insertAt", "startNode", "startOffset", "parent", "insertBefore", "create", "next", "update", "mutations", "context", "mutation", "type", "target"], "sources": ["../../src/blots/embed.ts"], "sourcesContent": ["import type { ScrollBlot } from 'parchment';\nimport { EmbedBlot } from 'parchment';\nimport TextBlot from './text.js';\n\nconst GUARD_TEXT = '\\uFEFF';\n\nexport interface EmbedContextRange {\n  startNode: Node | Text;\n  startOffset: number;\n  endNode?: Node | Text;\n  endOffset?: number;\n}\n\nclass Embed extends EmbedBlot {\n  contentNode: HTMLSpanElement;\n  leftGuard: Text;\n  rightGuard: Text;\n\n  constructor(scroll: ScrollBlot, node: Node) {\n    super(scroll, node);\n    this.contentNode = document.createElement('span');\n    this.contentNode.setAttribute('contenteditable', 'false');\n    Array.from(this.domNode.childNodes).forEach((childNode) => {\n      this.contentNode.appendChild(childNode);\n    });\n    this.leftGuard = document.createTextNode(GUARD_TEXT);\n    this.rightGuard = document.createTextNode(GUARD_TEXT);\n    this.domNode.appendChild(this.leftGuard);\n    this.domNode.appendChild(this.contentNode);\n    this.domNode.appendChild(this.rightGuard);\n  }\n\n  index(node: Node, offset: number) {\n    if (node === this.leftGuard) return 0;\n    if (node === this.rightGuard) return 1;\n    return super.index(node, offset);\n  }\n\n  restore(node: Text): EmbedContextRange | null {\n    let range: EmbedContextRange | null = null;\n    let textNode: Text;\n    const text = node.data.split(GUARD_TEXT).join('');\n    if (node === this.leftGuard) {\n      if (this.prev instanceof TextBlot) {\n        const prevLength = this.prev.length();\n        this.prev.insertAt(prevLength, text);\n        range = {\n          startNode: this.prev.domNode,\n          startOffset: prevLength + text.length,\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this);\n        range = {\n          startNode: textNode,\n          startOffset: text.length,\n        };\n      }\n    } else if (node === this.rightGuard) {\n      if (this.next instanceof TextBlot) {\n        this.next.insertAt(0, text);\n        range = {\n          startNode: this.next.domNode,\n          startOffset: text.length,\n        };\n      } else {\n        textNode = document.createTextNode(text);\n        this.parent.insertBefore(this.scroll.create(textNode), this.next);\n        range = {\n          startNode: textNode,\n          startOffset: text.length,\n        };\n      }\n    }\n    node.data = GUARD_TEXT;\n    return range;\n  }\n\n  update(mutations: MutationRecord[], context: Record<string, unknown>) {\n    mutations.forEach((mutation) => {\n      if (\n        mutation.type === 'characterData' &&\n        (mutation.target === this.leftGuard ||\n          mutation.target === this.rightGuard)\n      ) {\n        const range = this.restore(mutation.target as Text);\n        if (range) context.range = range;\n      }\n    });\n  }\n}\n\nexport default Embed;\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,WAAW;AACrC,OAAOC,QAAQ,MAAM,WAAW;AAEhC,MAAMC,UAAU,GAAG,QAAQ;AAS3B,MAAMC,KAAK,SAASH,SAAS,CAAC;EAK5BI,WAAWA,CAACC,MAAkB,EAAEC,IAAU,EAAE;IAC1C,KAAK,CAACD,MAAM,EAAEC,IAAI,CAAC;IACnB,IAAI,CAACC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACjD,IAAI,CAACF,WAAW,CAACG,YAAY,CAAC,iBAAiB,EAAE,OAAO,CAAC;IACzDC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACC,OAAO,CAACC,UAAU,CAAC,CAACC,OAAO,CAAEC,SAAS,IAAK;MACzD,IAAI,CAACT,WAAW,CAACU,WAAW,CAACD,SAAS,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAACE,SAAS,GAAGV,QAAQ,CAACW,cAAc,CAACjB,UAAU,CAAC;IACpD,IAAI,CAACkB,UAAU,GAAGZ,QAAQ,CAACW,cAAc,CAACjB,UAAU,CAAC;IACrD,IAAI,CAACW,OAAO,CAACI,WAAW,CAAC,IAAI,CAACC,SAAS,CAAC;IACxC,IAAI,CAACL,OAAO,CAACI,WAAW,CAAC,IAAI,CAACV,WAAW,CAAC;IAC1C,IAAI,CAACM,OAAO,CAACI,WAAW,CAAC,IAAI,CAACG,UAAU,CAAC;EAC3C;EAEAC,KAAKA,CAACf,IAAU,EAAEgB,MAAc,EAAE;IAChC,IAAIhB,IAAI,KAAK,IAAI,CAACY,SAAS,EAAE,OAAO,CAAC;IACrC,IAAIZ,IAAI,KAAK,IAAI,CAACc,UAAU,EAAE,OAAO,CAAC;IACtC,OAAO,KAAK,CAACC,KAAK,CAACf,IAAI,EAAEgB,MAAM,CAAC;EAClC;EAEAC,OAAOA,CAACjB,IAAU,EAA4B;IAC5C,IAAIkB,KAA+B,GAAG,IAAI;IAC1C,IAAIC,QAAc;IAClB,MAAMC,IAAI,GAAGpB,IAAI,CAACqB,IAAI,CAACC,KAAK,CAAC1B,UAAU,CAAC,CAAC2B,IAAI,CAAC,EAAE,CAAC;IACjD,IAAIvB,IAAI,KAAK,IAAI,CAACY,SAAS,EAAE;MAC3B,IAAI,IAAI,CAACY,IAAI,YAAY7B,QAAQ,EAAE;QACjC,MAAM8B,UAAU,GAAG,IAAI,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC;QACrC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAACF,UAAU,EAAEL,IAAI,CAAC;QACpCF,KAAK,GAAG;UACNU,SAAS,EAAE,IAAI,CAACJ,IAAI,CAACjB,OAAO;UAC5BsB,WAAW,EAAEJ,UAAU,GAAGL,IAAI,CAACM;QACjC,CAAC;MACH,CAAC,MAAM;QACLP,QAAQ,GAAGjB,QAAQ,CAACW,cAAc,CAACO,IAAI,CAAC;QACxC,IAAI,CAACU,MAAM,CAACC,YAAY,CAAC,IAAI,CAAChC,MAAM,CAACiC,MAAM,CAACb,QAAQ,CAAC,EAAE,IAAI,CAAC;QAC5DD,KAAK,GAAG;UACNU,SAAS,EAAET,QAAQ;UACnBU,WAAW,EAAET,IAAI,CAACM;QACpB,CAAC;MACH;IACF,CAAC,MAAM,IAAI1B,IAAI,KAAK,IAAI,CAACc,UAAU,EAAE;MACnC,IAAI,IAAI,CAACmB,IAAI,YAAYtC,QAAQ,EAAE;QACjC,IAAI,CAACsC,IAAI,CAACN,QAAQ,CAAC,CAAC,EAAEP,IAAI,CAAC;QAC3BF,KAAK,GAAG;UACNU,SAAS,EAAE,IAAI,CAACK,IAAI,CAAC1B,OAAO;UAC5BsB,WAAW,EAAET,IAAI,CAACM;QACpB,CAAC;MACH,CAAC,MAAM;QACLP,QAAQ,GAAGjB,QAAQ,CAACW,cAAc,CAACO,IAAI,CAAC;QACxC,IAAI,CAACU,MAAM,CAACC,YAAY,CAAC,IAAI,CAAChC,MAAM,CAACiC,MAAM,CAACb,QAAQ,CAAC,EAAE,IAAI,CAACc,IAAI,CAAC;QACjEf,KAAK,GAAG;UACNU,SAAS,EAAET,QAAQ;UACnBU,WAAW,EAAET,IAAI,CAACM;QACpB,CAAC;MACH;IACF;IACA1B,IAAI,CAACqB,IAAI,GAAGzB,UAAU;IACtB,OAAOsB,KAAK;EACd;EAEAgB,MAAMA,CAACC,SAA2B,EAAEC,OAAgC,EAAE;IACpED,SAAS,CAAC1B,OAAO,CAAE4B,QAAQ,IAAK;MAC9B,IACEA,QAAQ,CAACC,IAAI,KAAK,eAAe,KAChCD,QAAQ,CAACE,MAAM,KAAK,IAAI,CAAC3B,SAAS,IACjCyB,QAAQ,CAACE,MAAM,KAAK,IAAI,CAACzB,UAAU,CAAC,EACtC;QACA,MAAMI,KAAK,GAAG,IAAI,CAACD,OAAO,CAACoB,QAAQ,CAACE,MAAc,CAAC;QACnD,IAAIrB,KAAK,EAAEkB,OAAO,CAAClB,KAAK,GAAGA,KAAK;MAClC;IACF,CAAC,CAAC;EACJ;AACF;AAEA,eAAerB,KAAK", "ignoreList": []}