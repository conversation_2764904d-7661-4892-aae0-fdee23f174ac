{"version": 3, "file": "break.js", "names": ["EmbedBlot", "Break", "value", "undefined", "optimize", "prev", "next", "remove", "length", "blotName", "tagName"], "sources": ["../../src/blots/break.ts"], "sourcesContent": ["import { EmbedBlot } from 'parchment';\n\nclass Break extends EmbedBlot {\n  static value() {\n    return undefined;\n  }\n\n  optimize() {\n    if (this.prev || this.next) {\n      this.remove();\n    }\n  }\n\n  length() {\n    return 0;\n  }\n\n  value() {\n    return '';\n  }\n}\nBreak.blotName = 'break';\nBreak.tagName = 'BR';\n\nexport default Break;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,WAAW;AAErC,MAAMC,KAAK,SAASD,SAAS,CAAC;EAC5B,OAAOE,KAAKA,CAAA,EAAG;IACb,OAAOC,SAAS;EAClB;EAEAC,QAAQA,CAAA,EAAG;IACT,IAAI,IAAI,CAACC,IAAI,IAAI,IAAI,CAACC,IAAI,EAAE;MAC1B,IAAI,CAACC,MAAM,CAAC,CAAC;IACf;EACF;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO,CAAC;EACV;EAEAN,KAAKA,CAAA,EAAG;IACN,OAAO,EAAE;EACX;AACF;AACAD,KAAK,CAACQ,QAAQ,GAAG,OAAO;AACxBR,KAAK,CAACS,OAAO,GAAG,IAAI;AAEpB,eAAeT,KAAK", "ignoreList": []}