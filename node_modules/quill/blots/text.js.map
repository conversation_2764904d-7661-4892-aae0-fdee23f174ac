{"version": 3, "file": "text.js", "names": ["TextBlot", "Text", "entityMap", "escapeText", "text", "replace", "s", "default"], "sources": ["../../src/blots/text.ts"], "sourcesContent": ["import { TextBlot } from 'parchment';\n\nclass Text extends TextBlot {}\n\n// https://lodash.com/docs#escape\nconst entityMap: Record<string, string> = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n};\n\nfunction escapeText(text: string) {\n  return text.replace(/[&<>\"']/g, (s) => entityMap[s]);\n}\n\nexport { Text as default, escapeText };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,WAAW;AAEpC,MAAMC,IAAI,SAASD,QAAQ,CAAC;;AAE5B;AACA,MAAME,SAAiC,GAAG;EACxC,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACP,CAAC;AAED,SAASC,UAAUA,CAACC,IAAY,EAAE;EAChC,OAAOA,IAAI,CAACC,OAAO,CAAC,UAAU,EAAGC,CAAC,IAAKJ,SAAS,CAACI,CAAC,CAAC,CAAC;AACtD;AAEA,SAASL,IAAI,IAAIM,OAAO,EAAEJ,UAAU", "ignoreList": []}