{"version": 3, "file": "color-picker.js", "names": ["Picker", "ColorPicker", "constructor", "select", "label", "innerHTML", "container", "classList", "add", "Array", "from", "querySelectorAll", "slice", "for<PERSON>ach", "item", "buildItem", "option", "style", "backgroundColor", "getAttribute", "selectItem", "trigger", "colorLabel", "querySelector", "value", "tagName", "stroke", "fill"], "sources": ["../../src/ui/color-picker.ts"], "sourcesContent": ["import Picker from './picker.js';\n\nclass ColorPicker extends Picker {\n  constructor(select: HTMLSelectElement, label: string) {\n    super(select);\n    this.label.innerHTML = label;\n    this.container.classList.add('ql-color-picker');\n    Array.from(this.container.querySelectorAll('.ql-picker-item'))\n      .slice(0, 7)\n      .forEach((item) => {\n        item.classList.add('ql-primary');\n      });\n  }\n\n  buildItem(option: HTMLOptionElement) {\n    const item = super.buildItem(option);\n    item.style.backgroundColor = option.getAttribute('value') || '';\n    return item;\n  }\n\n  selectItem(item: HTMLElement | null, trigger?: boolean) {\n    super.selectItem(item, trigger);\n    const colorLabel = this.label.querySelector<HTMLElement>('.ql-color-label');\n    const value = item ? item.getAttribute('data-value') || '' : '';\n    if (colorLabel) {\n      if (colorLabel.tagName === 'line') {\n        colorLabel.style.stroke = value;\n      } else {\n        colorLabel.style.fill = value;\n      }\n    }\n  }\n}\n\nexport default ColorPicker;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAEhC,MAAMC,WAAW,SAASD,MAAM,CAAC;EAC/BE,WAAWA,CAACC,MAAyB,EAAEC,KAAa,EAAE;IACpD,KAAK,CAACD,MAAM,CAAC;IACb,IAAI,CAACC,KAAK,CAACC,SAAS,GAAGD,KAAK;IAC5B,IAAI,CAACE,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC/CC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACK,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAC3DC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXC,OAAO,CAAEC,IAAI,IAAK;MACjBA,IAAI,CAACP,SAAS,CAACC,GAAG,CAAC,YAAY,CAAC;IAClC,CAAC,CAAC;EACN;EAEAO,SAASA,CAACC,MAAyB,EAAE;IACnC,MAAMF,IAAI,GAAG,KAAK,CAACC,SAAS,CAACC,MAAM,CAAC;IACpCF,IAAI,CAACG,KAAK,CAACC,eAAe,GAAGF,MAAM,CAACG,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;IAC/D,OAAOL,IAAI;EACb;EAEAM,UAAUA,CAACN,IAAwB,EAAEO,OAAiB,EAAE;IACtD,KAAK,CAACD,UAAU,CAACN,IAAI,EAAEO,OAAO,CAAC;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAAClB,KAAK,CAACmB,aAAa,CAAc,iBAAiB,CAAC;IAC3E,MAAMC,KAAK,GAAGV,IAAI,GAAGA,IAAI,CAACK,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,EAAE;IAC/D,IAAIG,UAAU,EAAE;MACd,IAAIA,UAAU,CAACG,OAAO,KAAK,MAAM,EAAE;QACjCH,UAAU,CAACL,KAAK,CAACS,MAAM,GAAGF,KAAK;MACjC,CAAC,MAAM;QACLF,UAAU,CAACL,KAAK,CAACU,IAAI,GAAGH,KAAK;MAC/B;IACF;EACF;AACF;AAEA,eAAevB,WAAW", "ignoreList": []}