{"version": 3, "file": "icon-picker.js", "names": ["Picker", "IconPicker", "constructor", "select", "icons", "container", "classList", "add", "Array", "from", "querySelectorAll", "for<PERSON>ach", "item", "innerHTML", "getAttribute", "defaultItem", "querySelector", "selectItem", "target", "trigger", "label"], "sources": ["../../src/ui/icon-picker.ts"], "sourcesContent": ["import Picker from './picker.js';\n\nclass IconPicker extends Picker {\n  defaultItem: HTMLElement | null;\n\n  constructor(select: HTMLSelectElement, icons: Record<string, string>) {\n    super(select);\n    this.container.classList.add('ql-icon-picker');\n    Array.from(this.container.querySelectorAll('.ql-picker-item')).forEach(\n      (item) => {\n        item.innerHTML = icons[item.getAttribute('data-value') || ''];\n      },\n    );\n    this.defaultItem = this.container.querySelector('.ql-selected');\n    this.selectItem(this.defaultItem);\n  }\n\n  selectItem(target: HTMLElement | null, trigger?: boolean) {\n    super.selectItem(target, trigger);\n    const item = target || this.defaultItem;\n    if (item != null) {\n      if (this.label.innerHTML === item.innerHTML) return;\n      this.label.innerHTML = item.innerHTML;\n    }\n  }\n}\n\nexport default IconPicker;\n"], "mappings": "AAAA,OAAOA,MAAM,MAAM,aAAa;AAEhC,MAAMC,UAAU,SAASD,MAAM,CAAC;EAG9BE,WAAWA,CAACC,MAAyB,EAAEC,KAA6B,EAAE;IACpE,KAAK,CAACD,MAAM,CAAC;IACb,IAAI,CAACE,SAAS,CAACC,SAAS,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC9CC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACJ,SAAS,CAACK,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,CAACC,OAAO,CACnEC,IAAI,IAAK;MACRA,IAAI,CAACC,SAAS,GAAGT,KAAK,CAACQ,IAAI,CAACE,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAC/D,CACF,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,IAAI,CAACV,SAAS,CAACW,aAAa,CAAC,cAAc,CAAC;IAC/D,IAAI,CAACC,UAAU,CAAC,IAAI,CAACF,WAAW,CAAC;EACnC;EAEAE,UAAUA,CAACC,MAA0B,EAAEC,OAAiB,EAAE;IACxD,KAAK,CAACF,UAAU,CAACC,MAAM,EAAEC,OAAO,CAAC;IACjC,MAAMP,IAAI,GAAGM,MAAM,IAAI,IAAI,CAACH,WAAW;IACvC,IAAIH,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI,IAAI,CAACQ,KAAK,CAACP,SAAS,KAAKD,IAAI,CAACC,SAAS,EAAE;MAC7C,IAAI,CAACO,KAAK,CAACP,SAAS,GAAGD,IAAI,CAACC,SAAS;IACvC;EACF;AACF;AAEA,eAAeZ,UAAU", "ignoreList": []}