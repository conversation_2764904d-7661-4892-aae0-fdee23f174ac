{"version": 3, "file": "icons.js", "names": ["align", "alignLeftIcon", "center", "alignCenterIcon", "right", "alignRightIcon", "justify", "alignJustifyIcon", "background", "backgroundIcon", "blockquote", "blockquoteIcon", "bold", "boldIcon", "clean", "cleanIcon", "code", "codeIcon", "color", "colorIcon", "direction", "directionLeftToRightIcon", "rtl", "directionRightToLeftIcon", "formula", "formulaIcon", "header", "headerIcon", "header2Icon", "header3Icon", "header4Icon", "header5Icon", "header6Icon", "italic", "italicIcon", "image", "imageIcon", "indent", "indentIcon", "outdentIcon", "link", "linkIcon", "list", "bullet", "listBulletIcon", "check", "listCheckIcon", "ordered", "listOrderedIcon", "script", "sub", "subscriptIcon", "super", "superscriptIcon", "strike", "strikeIcon", "table", "tableIcon", "underline", "underlineIcon", "video", "videoIcon"], "sources": ["../../src/ui/icons.ts"], "sourcesContent": ["import alignLeftIcon from '../assets/icons/align-left.svg';\nimport alignCenterIcon from '../assets/icons/align-center.svg';\nimport alignRightIcon from '../assets/icons/align-right.svg';\nimport alignJustifyIcon from '../assets/icons/align-justify.svg';\nimport backgroundIcon from '../assets/icons/background.svg';\nimport blockquoteIcon from '../assets/icons/blockquote.svg';\nimport boldIcon from '../assets/icons/bold.svg';\nimport cleanIcon from '../assets/icons/clean.svg';\nimport codeIcon from '../assets/icons/code.svg';\nimport colorIcon from '../assets/icons/color.svg';\nimport directionLeftToRightIcon from '../assets/icons/direction-ltr.svg';\nimport directionRightToLeftIcon from '../assets/icons/direction-rtl.svg';\nimport formulaIcon from '../assets/icons/formula.svg';\nimport headerIcon from '../assets/icons/header.svg';\nimport header2Icon from '../assets/icons/header-2.svg';\nimport header3Icon from '../assets/icons/header-3.svg';\nimport header4Icon from '../assets/icons/header-4.svg';\nimport header5Icon from '../assets/icons/header-5.svg';\nimport header6Icon from '../assets/icons/header-6.svg';\nimport italicIcon from '../assets/icons/italic.svg';\nimport imageIcon from '../assets/icons/image.svg';\nimport indentIcon from '../assets/icons/indent.svg';\nimport outdentIcon from '../assets/icons/outdent.svg';\nimport linkIcon from '../assets/icons/link.svg';\nimport listBulletIcon from '../assets/icons/list-bullet.svg';\nimport listCheckIcon from '../assets/icons/list-check.svg';\nimport listOrderedIcon from '../assets/icons/list-ordered.svg';\nimport subscriptIcon from '../assets/icons/subscript.svg';\nimport superscriptIcon from '../assets/icons/superscript.svg';\nimport strikeIcon from '../assets/icons/strike.svg';\nimport tableIcon from '../assets/icons/table.svg';\nimport underlineIcon from '../assets/icons/underline.svg';\nimport videoIcon from '../assets/icons/video.svg';\n\nexport default {\n  align: {\n    '': alignLeftIcon,\n    center: alignCenterIcon,\n    right: alignRightIcon,\n    justify: alignJustifyIcon,\n  },\n  background: backgroundIcon,\n  blockquote: blockquoteIcon,\n  bold: boldIcon,\n  clean: cleanIcon,\n  code: codeIcon,\n  'code-block': codeIcon,\n  color: colorIcon,\n  direction: {\n    '': directionLeftToRightIcon,\n    rtl: directionRightToLeftIcon,\n  },\n  formula: formulaIcon,\n  header: {\n    '1': headerIcon,\n    '2': header2Icon,\n    '3': header3Icon,\n    '4': header4Icon,\n    '5': header5Icon,\n    '6': header6Icon,\n  },\n  italic: italicIcon,\n  image: imageIcon,\n  indent: {\n    '+1': indentIcon,\n    '-1': outdentIcon,\n  },\n  link: linkIcon,\n  list: {\n    bullet: listBulletIcon,\n    check: listCheckIcon,\n    ordered: listOrderedIcon,\n  },\n  script: {\n    sub: subscriptIcon,\n    super: superscriptIcon,\n  },\n  strike: strikeIcon,\n  table: tableIcon,\n  underline: underlineIcon,\n  video: videoIcon,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA,eAAe;EACbA,KAAK,EAAE;IACL,EAAE,EAAEC,aAAa;IACjBC,MAAM,EAAEC,eAAe;IACvBC,KAAK,EAAEC,cAAc;IACrBC,OAAO,EAAEC;EACX,CAAC;EACDC,UAAU,EAAEC,cAAc;EAC1BC,UAAU,EAAEC,cAAc;EAC1BC,IAAI,EAAEC,QAAQ;EACdC,KAAK,EAAEC,SAAS;EAChBC,IAAI,EAAEC,QAAQ;EACd,YAAY,EAAEA,QAAQ;EACtBC,KAAK,EAAEC,SAAS;EAChBC,SAAS,EAAE;IACT,EAAE,EAAEC,wBAAwB;IAC5BC,GAAG,EAAEC;EACP,CAAC;EACDC,OAAO,EAAEC,WAAW;EACpBC,MAAM,EAAE;IACN,GAAG,EAAEC,UAAU;IACf,GAAG,EAAEC,WAAW;IAChB,GAAG,EAAEC,WAAW;IAChB,GAAG,EAAEC,WAAW;IAChB,GAAG,EAAEC,WAAW;IAChB,GAAG,EAAEC;EACP,CAAC;EACDC,MAAM,EAAEC,UAAU;EAClBC,KAAK,EAAEC,SAAS;EAChBC,MAAM,EAAE;IACN,IAAI,EAAEC,UAAU;IAChB,IAAI,EAAEC;EACR,CAAC;EACDC,IAAI,EAAEC,QAAQ;EACdC,IAAI,EAAE;IACJC,MAAM,EAAEC,cAAc;IACtBC,KAAK,EAAEC,aAAa;IACpBC,OAAO,EAAEC;EACX,CAAC;EACDC,MAAM,EAAE;IACNC,GAAG,EAAEC,aAAa;IAClBC,KAAK,EAAEC;EACT,CAAC;EACDC,MAAM,EAAEC,UAAU;EAClBC,KAAK,EAAEC,SAAS;EAChBC,SAAS,EAAEC,aAAa;EACxBC,KAAK,EAAEC;AACT,CAAC", "ignoreList": []}