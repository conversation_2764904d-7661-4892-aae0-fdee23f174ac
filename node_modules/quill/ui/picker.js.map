{"version": 3, "file": "picker.js", "names": ["optionsCounter", "toggleAriaAttribute", "element", "attribute", "setAttribute", "getAttribute", "Picker", "constructor", "select", "container", "document", "createElement", "buildPicker", "style", "display", "parentNode", "insertBefore", "label", "addEventListener", "togglePicker", "event", "key", "escape", "preventDefault", "update", "bind", "classList", "toggle", "options", "buildItem", "option", "item", "tabIndex", "add", "value", "textContent", "selectItem", "buildLabel", "innerHTML", "DropdownIcon", "append<PERSON><PERSON><PERSON>", "buildOptions", "id", "Array", "from", "for<PERSON>ach", "selected", "attributes", "name", "close", "setTimeout", "focus", "remove", "trigger", "arguments", "length", "undefined", "querySelector", "selectedIndex", "children", "indexOf", "hasAttribute", "removeAttribute", "dispatchEvent", "Event", "isActive"], "sources": ["../../src/ui/picker.ts"], "sourcesContent": ["import DropdownIcon from '../assets/icons/dropdown.svg';\n\nlet optionsCounter = 0;\n\nfunction toggleAriaAttribute(element: HTMLElement, attribute: string) {\n  element.setAttribute(\n    attribute,\n    `${!(element.getAttribute(attribute) === 'true')}`,\n  );\n}\n\nclass Picker {\n  select: HTMLSelectElement;\n  container: HTMLElement;\n  label: HTMLElement;\n\n  constructor(select: HTMLSelectElement) {\n    this.select = select;\n    this.container = document.createElement('span');\n    this.buildPicker();\n    this.select.style.display = 'none';\n    // @ts-expect-error Fix me later\n    this.select.parentNode.insertBefore(this.container, this.select);\n\n    this.label.addEventListener('mousedown', () => {\n      this.togglePicker();\n    });\n    this.label.addEventListener('keydown', (event) => {\n      switch (event.key) {\n        case 'Enter':\n          this.togglePicker();\n          break;\n        case 'Escape':\n          this.escape();\n          event.preventDefault();\n          break;\n        default:\n      }\n    });\n    this.select.addEventListener('change', this.update.bind(this));\n  }\n\n  togglePicker() {\n    this.container.classList.toggle('ql-expanded');\n    // Toggle aria-expanded and aria-hidden to make the picker accessible\n    toggleAriaAttribute(this.label, 'aria-expanded');\n    // @ts-expect-error\n    toggleAriaAttribute(this.options, 'aria-hidden');\n  }\n\n  buildItem(option: HTMLOptionElement) {\n    const item = document.createElement('span');\n    // @ts-expect-error\n    item.tabIndex = '0';\n    item.setAttribute('role', 'button');\n    item.classList.add('ql-picker-item');\n    const value = option.getAttribute('value');\n    if (value) {\n      item.setAttribute('data-value', value);\n    }\n    if (option.textContent) {\n      item.setAttribute('data-label', option.textContent);\n    }\n    item.addEventListener('click', () => {\n      this.selectItem(item, true);\n    });\n    item.addEventListener('keydown', (event) => {\n      switch (event.key) {\n        case 'Enter':\n          this.selectItem(item, true);\n          event.preventDefault();\n          break;\n        case 'Escape':\n          this.escape();\n          event.preventDefault();\n          break;\n        default:\n      }\n    });\n\n    return item;\n  }\n\n  buildLabel() {\n    const label = document.createElement('span');\n    label.classList.add('ql-picker-label');\n    label.innerHTML = DropdownIcon;\n    // @ts-expect-error\n    label.tabIndex = '0';\n    label.setAttribute('role', 'button');\n    label.setAttribute('aria-expanded', 'false');\n    this.container.appendChild(label);\n    return label;\n  }\n\n  buildOptions() {\n    const options = document.createElement('span');\n    options.classList.add('ql-picker-options');\n\n    // Don't want screen readers to read this until options are visible\n    options.setAttribute('aria-hidden', 'true');\n    // @ts-expect-error\n    options.tabIndex = '-1';\n\n    // Need a unique id for aria-controls\n    options.id = `ql-picker-options-${optionsCounter}`;\n    optionsCounter += 1;\n    this.label.setAttribute('aria-controls', options.id);\n\n    // @ts-expect-error\n    this.options = options;\n\n    Array.from(this.select.options).forEach((option) => {\n      const item = this.buildItem(option);\n      options.appendChild(item);\n      if (option.selected === true) {\n        this.selectItem(item);\n      }\n    });\n    this.container.appendChild(options);\n  }\n\n  buildPicker() {\n    Array.from(this.select.attributes).forEach((item) => {\n      this.container.setAttribute(item.name, item.value);\n    });\n    this.container.classList.add('ql-picker');\n    this.label = this.buildLabel();\n    this.buildOptions();\n  }\n\n  escape() {\n    // Close menu and return focus to trigger label\n    this.close();\n    // Need setTimeout for accessibility to ensure that the browser executes\n    // focus on the next process thread and after any DOM content changes\n    setTimeout(() => this.label.focus(), 1);\n  }\n\n  close() {\n    this.container.classList.remove('ql-expanded');\n    this.label.setAttribute('aria-expanded', 'false');\n    // @ts-expect-error\n    this.options.setAttribute('aria-hidden', 'true');\n  }\n\n  selectItem(item: HTMLElement | null, trigger = false) {\n    const selected = this.container.querySelector('.ql-selected');\n    if (item === selected) return;\n    if (selected != null) {\n      selected.classList.remove('ql-selected');\n    }\n    if (item == null) return;\n    item.classList.add('ql-selected');\n    // @ts-expect-error Fix me later\n    this.select.selectedIndex = Array.from(item.parentNode.children).indexOf(\n      item,\n    );\n    if (item.hasAttribute('data-value')) {\n      // @ts-expect-error Fix me later\n      this.label.setAttribute('data-value', item.getAttribute('data-value'));\n    } else {\n      this.label.removeAttribute('data-value');\n    }\n    if (item.hasAttribute('data-label')) {\n      // @ts-expect-error Fix me later\n      this.label.setAttribute('data-label', item.getAttribute('data-label'));\n    } else {\n      this.label.removeAttribute('data-label');\n    }\n    if (trigger) {\n      this.select.dispatchEvent(new Event('change'));\n      this.close();\n    }\n  }\n\n  update() {\n    let option;\n    if (this.select.selectedIndex > -1) {\n      const item =\n        // @ts-expect-error Fix me later\n        this.container.querySelector('.ql-picker-options').children[\n          this.select.selectedIndex\n        ];\n      option = this.select.options[this.select.selectedIndex];\n      // @ts-expect-error\n      this.selectItem(item);\n    } else {\n      this.selectItem(null);\n    }\n    const isActive =\n      option != null &&\n      option !== this.select.querySelector('option[selected]');\n    this.label.classList.toggle('ql-active', isActive);\n  }\n}\n\nexport default Picker;\n"], "mappings": ";AAEA,IAAIA,cAAc,GAAG,CAAC;AAEtB,SAASC,mBAAmBA,CAACC,OAAoB,EAAEC,SAAiB,EAAE;EACpED,OAAO,CAACE,YAAY,CAClBD,SAAS,EACR,GAAE,EAAED,OAAO,CAACG,YAAY,CAACF,SAAS,CAAC,KAAK,MAAM,CAAE,EACnD,CAAC;AACH;AAEA,MAAMG,MAAM,CAAC;EAKXC,WAAWA,CAACC,MAAyB,EAAE;IACrC,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACJ,MAAM,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;IAClC;IACA,IAAI,CAACN,MAAM,CAACO,UAAU,CAACC,YAAY,CAAC,IAAI,CAACP,SAAS,EAAE,IAAI,CAACD,MAAM,CAAC;IAEhE,IAAI,CAACS,KAAK,CAACC,gBAAgB,CAAC,WAAW,EAAE,MAAM;MAC7C,IAAI,CAACC,YAAY,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,IAAI,CAACF,KAAK,CAACC,gBAAgB,CAAC,SAAS,EAAGE,KAAK,IAAK;MAChD,QAAQA,KAAK,CAACC,GAAG;QACf,KAAK,OAAO;UACV,IAAI,CAACF,YAAY,CAAC,CAAC;UACnB;QACF,KAAK,QAAQ;UACX,IAAI,CAACG,MAAM,CAAC,CAAC;UACbF,KAAK,CAACG,cAAc,CAAC,CAAC;UACtB;QACF;MACF;IACF,CAAC,CAAC;IACF,IAAI,CAACf,MAAM,CAACU,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACM,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;EAChE;EAEAN,YAAYA,CAAA,EAAG;IACb,IAAI,CAACV,SAAS,CAACiB,SAAS,CAACC,MAAM,CAAC,aAAa,CAAC;IAC9C;IACA1B,mBAAmB,CAAC,IAAI,CAACgB,KAAK,EAAE,eAAe,CAAC;IAChD;IACAhB,mBAAmB,CAAC,IAAI,CAAC2B,OAAO,EAAE,aAAa,CAAC;EAClD;EAEAC,SAASA,CAACC,MAAyB,EAAE;IACnC,MAAMC,IAAI,GAAGrB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC3C;IACAoB,IAAI,CAACC,QAAQ,GAAG,GAAG;IACnBD,IAAI,CAAC3B,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IACnC2B,IAAI,CAACL,SAAS,CAACO,GAAG,CAAC,gBAAgB,CAAC;IACpC,MAAMC,KAAK,GAAGJ,MAAM,CAACzB,YAAY,CAAC,OAAO,CAAC;IAC1C,IAAI6B,KAAK,EAAE;MACTH,IAAI,CAAC3B,YAAY,CAAC,YAAY,EAAE8B,KAAK,CAAC;IACxC;IACA,IAAIJ,MAAM,CAACK,WAAW,EAAE;MACtBJ,IAAI,CAAC3B,YAAY,CAAC,YAAY,EAAE0B,MAAM,CAACK,WAAW,CAAC;IACrD;IACAJ,IAAI,CAACb,gBAAgB,CAAC,OAAO,EAAE,MAAM;MACnC,IAAI,CAACkB,UAAU,CAACL,IAAI,EAAE,IAAI,CAAC;IAC7B,CAAC,CAAC;IACFA,IAAI,CAACb,gBAAgB,CAAC,SAAS,EAAGE,KAAK,IAAK;MAC1C,QAAQA,KAAK,CAACC,GAAG;QACf,KAAK,OAAO;UACV,IAAI,CAACe,UAAU,CAACL,IAAI,EAAE,IAAI,CAAC;UAC3BX,KAAK,CAACG,cAAc,CAAC,CAAC;UACtB;QACF,KAAK,QAAQ;UACX,IAAI,CAACD,MAAM,CAAC,CAAC;UACbF,KAAK,CAACG,cAAc,CAAC,CAAC;UACtB;QACF;MACF;IACF,CAAC,CAAC;IAEF,OAAOQ,IAAI;EACb;EAEAM,UAAUA,CAAA,EAAG;IACX,MAAMpB,KAAK,GAAGP,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC5CM,KAAK,CAACS,SAAS,CAACO,GAAG,CAAC,iBAAiB,CAAC;IACtChB,KAAK,CAACqB,SAAS,GAAGC,YAAY;IAC9B;IACAtB,KAAK,CAACe,QAAQ,GAAG,GAAG;IACpBf,KAAK,CAACb,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IACpCa,KAAK,CAACb,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;IAC5C,IAAI,CAACK,SAAS,CAAC+B,WAAW,CAACvB,KAAK,CAAC;IACjC,OAAOA,KAAK;EACd;EAEAwB,YAAYA,CAAA,EAAG;IACb,MAAMb,OAAO,GAAGlB,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IAC9CiB,OAAO,CAACF,SAAS,CAACO,GAAG,CAAC,mBAAmB,CAAC;;IAE1C;IACAL,OAAO,CAACxB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC3C;IACAwB,OAAO,CAACI,QAAQ,GAAG,IAAI;;IAEvB;IACAJ,OAAO,CAACc,EAAE,GAAI,qBAAoB1C,cAAe,EAAC;IAClDA,cAAc,IAAI,CAAC;IACnB,IAAI,CAACiB,KAAK,CAACb,YAAY,CAAC,eAAe,EAAEwB,OAAO,CAACc,EAAE,CAAC;;IAEpD;IACA,IAAI,CAACd,OAAO,GAAGA,OAAO;IAEtBe,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpC,MAAM,CAACoB,OAAO,CAAC,CAACiB,OAAO,CAAEf,MAAM,IAAK;MAClD,MAAMC,IAAI,GAAG,IAAI,CAACF,SAAS,CAACC,MAAM,CAAC;MACnCF,OAAO,CAACY,WAAW,CAACT,IAAI,CAAC;MACzB,IAAID,MAAM,CAACgB,QAAQ,KAAK,IAAI,EAAE;QAC5B,IAAI,CAACV,UAAU,CAACL,IAAI,CAAC;MACvB;IACF,CAAC,CAAC;IACF,IAAI,CAACtB,SAAS,CAAC+B,WAAW,CAACZ,OAAO,CAAC;EACrC;EAEAhB,WAAWA,CAAA,EAAG;IACZ+B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACpC,MAAM,CAACuC,UAAU,CAAC,CAACF,OAAO,CAAEd,IAAI,IAAK;MACnD,IAAI,CAACtB,SAAS,CAACL,YAAY,CAAC2B,IAAI,CAACiB,IAAI,EAAEjB,IAAI,CAACG,KAAK,CAAC;IACpD,CAAC,CAAC;IACF,IAAI,CAACzB,SAAS,CAACiB,SAAS,CAACO,GAAG,CAAC,WAAW,CAAC;IACzC,IAAI,CAAChB,KAAK,GAAG,IAAI,CAACoB,UAAU,CAAC,CAAC;IAC9B,IAAI,CAACI,YAAY,CAAC,CAAC;EACrB;EAEAnB,MAAMA,CAAA,EAAG;IACP;IACA,IAAI,CAAC2B,KAAK,CAAC,CAAC;IACZ;IACA;IACAC,UAAU,CAAC,MAAM,IAAI,CAACjC,KAAK,CAACkC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACzC;EAEAF,KAAKA,CAAA,EAAG;IACN,IAAI,CAACxC,SAAS,CAACiB,SAAS,CAAC0B,MAAM,CAAC,aAAa,CAAC;IAC9C,IAAI,CAACnC,KAAK,CAACb,YAAY,CAAC,eAAe,EAAE,OAAO,CAAC;IACjD;IACA,IAAI,CAACwB,OAAO,CAACxB,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;EAClD;EAEAgC,UAAUA,CAACL,IAAwB,EAAmB;IAAA,IAAjBsB,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAClD,MAAMR,QAAQ,GAAG,IAAI,CAACrC,SAAS,CAACgD,aAAa,CAAC,cAAc,CAAC;IAC7D,IAAI1B,IAAI,KAAKe,QAAQ,EAAE;IACvB,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpBA,QAAQ,CAACpB,SAAS,CAAC0B,MAAM,CAAC,aAAa,CAAC;IAC1C;IACA,IAAIrB,IAAI,IAAI,IAAI,EAAE;IAClBA,IAAI,CAACL,SAAS,CAACO,GAAG,CAAC,aAAa,CAAC;IACjC;IACA,IAAI,CAACzB,MAAM,CAACkD,aAAa,GAAGf,KAAK,CAACC,IAAI,CAACb,IAAI,CAAChB,UAAU,CAAC4C,QAAQ,CAAC,CAACC,OAAO,CACtE7B,IACF,CAAC;IACD,IAAIA,IAAI,CAAC8B,YAAY,CAAC,YAAY,CAAC,EAAE;MACnC;MACA,IAAI,CAAC5C,KAAK,CAACb,YAAY,CAAC,YAAY,EAAE2B,IAAI,CAAC1B,YAAY,CAAC,YAAY,CAAC,CAAC;IACxE,CAAC,MAAM;MACL,IAAI,CAACY,KAAK,CAAC6C,eAAe,CAAC,YAAY,CAAC;IAC1C;IACA,IAAI/B,IAAI,CAAC8B,YAAY,CAAC,YAAY,CAAC,EAAE;MACnC;MACA,IAAI,CAAC5C,KAAK,CAACb,YAAY,CAAC,YAAY,EAAE2B,IAAI,CAAC1B,YAAY,CAAC,YAAY,CAAC,CAAC;IACxE,CAAC,MAAM;MACL,IAAI,CAACY,KAAK,CAAC6C,eAAe,CAAC,YAAY,CAAC;IAC1C;IACA,IAAIT,OAAO,EAAE;MACX,IAAI,CAAC7C,MAAM,CAACuD,aAAa,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC;MAC9C,IAAI,CAACf,KAAK,CAAC,CAAC;IACd;EACF;EAEAzB,MAAMA,CAAA,EAAG;IACP,IAAIM,MAAM;IACV,IAAI,IAAI,CAACtB,MAAM,CAACkD,aAAa,GAAG,CAAC,CAAC,EAAE;MAClC,MAAM3B,IAAI;MACR;MACA,IAAI,CAACtB,SAAS,CAACgD,aAAa,CAAC,oBAAoB,CAAC,CAACE,QAAQ,CACzD,IAAI,CAACnD,MAAM,CAACkD,aAAa,CAC1B;MACH5B,MAAM,GAAG,IAAI,CAACtB,MAAM,CAACoB,OAAO,CAAC,IAAI,CAACpB,MAAM,CAACkD,aAAa,CAAC;MACvD;MACA,IAAI,CAACtB,UAAU,CAACL,IAAI,CAAC;IACvB,CAAC,MAAM;MACL,IAAI,CAACK,UAAU,CAAC,IAAI,CAAC;IACvB;IACA,MAAM6B,QAAQ,GACZnC,MAAM,IAAI,IAAI,IACdA,MAAM,KAAK,IAAI,CAACtB,MAAM,CAACiD,aAAa,CAAC,kBAAkB,CAAC;IAC1D,IAAI,CAACxC,KAAK,CAACS,SAAS,CAACC,MAAM,CAAC,WAAW,EAAEsC,QAAQ,CAAC;EACpD;AACF;AAEA,eAAe3D,MAAM", "ignoreList": []}