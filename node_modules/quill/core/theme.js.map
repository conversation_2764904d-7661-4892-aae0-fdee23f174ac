{"version": 3, "file": "theme.js", "names": ["Theme", "DEFAULTS", "modules", "themes", "default", "constructor", "quill", "options", "init", "Object", "keys", "for<PERSON>ach", "name", "addModule", "ModuleClass", "import"], "sources": ["../../src/core/theme.ts"], "sourcesContent": ["import type Quill from '../core.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type { ToolbarProps } from '../modules/toolbar.js';\nimport type Uploader from '../modules/uploader.js';\n\nexport interface ThemeOptions {\n  modules: Record<string, unknown> & {\n    toolbar?: null | ToolbarProps;\n  };\n}\n\nclass Theme {\n  static DEFAULTS: ThemeOptions = {\n    modules: {},\n  };\n\n  static themes = {\n    default: Theme,\n  };\n\n  modules: ThemeOptions['modules'] = {};\n\n  constructor(\n    protected quill: Quill,\n    protected options: ThemeOptions,\n  ) {}\n\n  init() {\n    Object.keys(this.options.modules).forEach((name) => {\n      if (this.modules[name] == null) {\n        this.addModule(name);\n      }\n    });\n  }\n\n  addModule(name: 'clipboard'): Clipboard;\n  addModule(name: 'keyboard'): Keyboard;\n  addModule(name: 'uploader'): Uploader;\n  addModule(name: 'history'): History;\n  addModule(name: string): unknown;\n  addModule(name: string) {\n    // @ts-expect-error\n    const ModuleClass = this.quill.constructor.import(`modules/${name}`);\n    this.modules[name] = new ModuleClass(\n      this.quill,\n      this.options.modules[name] || {},\n    );\n    return this.modules[name];\n  }\n}\n\nexport interface ThemeConstructor {\n  new (quill: Quill, options: unknown): Theme;\n  DEFAULTS: ThemeOptions;\n}\n\nexport default Theme;\n"], "mappings": "AAaA,MAAMA,KAAK,CAAC;EACV,OAAOC,QAAQ,GAAiB;IAC9BC,OAAO,EAAE,CAAC;EACZ,CAAC;EAED,OAAOC,MAAM,GAAG;IACdC,OAAO,EAAEJ;EACX,CAAC;EAEDE,OAAO,GAA4B,CAAC,CAAC;EAErCG,WAAWA,CACCC,KAAY,EACZC,OAAqB,EAC/B;IAAA,KAFUD,KAAY,GAAZA,KAAY;IAAA,KACZC,OAAqB,GAArBA,OAAqB;EAC9B;EAEHC,IAAIA,CAAA,EAAG;IACLC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACH,OAAO,CAACL,OAAO,CAAC,CAACS,OAAO,CAAEC,IAAI,IAAK;MAClD,IAAI,IAAI,CAACV,OAAO,CAACU,IAAI,CAAC,IAAI,IAAI,EAAE;QAC9B,IAAI,CAACC,SAAS,CAACD,IAAI,CAAC;MACtB;IACF,CAAC,CAAC;EACJ;EAOAC,SAASA,CAACD,IAAY,EAAE;IACtB;IACA,MAAME,WAAW,GAAG,IAAI,CAACR,KAAK,CAACD,WAAW,CAACU,MAAM,CAAE,WAAUH,IAAK,EAAC,CAAC;IACpE,IAAI,CAACV,OAAO,CAACU,IAAI,CAAC,GAAG,IAAIE,WAAW,CAClC,IAAI,CAACR,KAAK,EACV,IAAI,CAACC,OAAO,CAACL,OAAO,CAACU,IAAI,CAAC,IAAI,CAAC,CACjC,CAAC;IACD,OAAO,IAAI,CAACV,OAAO,CAACU,IAAI,CAAC;EAC3B;AACF;AAOA,eAAeZ,KAAK", "ignoreList": []}