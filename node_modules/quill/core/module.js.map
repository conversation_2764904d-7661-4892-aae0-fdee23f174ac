{"version": 3, "file": "module.js", "names": ["<PERSON><PERSON><PERSON>", "DEFAULTS", "constructor", "quill", "options", "arguments", "length", "undefined"], "sources": ["../../src/core/module.ts"], "sourcesContent": ["import type Quill from './quill.js';\n\nabstract class Module<T extends {} = {}> {\n  static DEFAULTS = {};\n\n  constructor(\n    public quill: Quill,\n    protected options: Partial<T> = {},\n  ) {}\n}\n\nexport default Module;\n"], "mappings": "AAEA,MAAeA,MAAM,CAAoB;EACvC,OAAOC,QAAQ,GAAG,CAAC,CAAC;EAEpBC,WAAWA,CACFC,KAAY,EAEnB;IAAA,IADUC,OAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,KAD3BF,KAAY,GAAZA,KAAY;IAAA,KACTC,OAAmB,GAAnBA,OAAmB;EAC5B;AACL;AAEA,eAAeJ,MAAM", "ignoreList": []}