{"version": 3, "file": "logger.js", "names": ["levels", "level", "debug", "method", "indexOf", "_len", "arguments", "length", "args", "Array", "_key", "console", "namespace", "ns", "reduce", "logger", "bind", "newLevel"], "sources": ["../../src/core/logger.ts"], "sourcesContent": ["const levels = ['error', 'warn', 'log', 'info'] as const;\nexport type DebugLevel = (typeof levels)[number];\nlet level: DebugLevel | false = 'warn';\n\nfunction debug(method: DebugLevel, ...args: unknown[]) {\n  if (level) {\n    if (levels.indexOf(method) <= levels.indexOf(level)) {\n      console[method](...args); // eslint-disable-line no-console\n    }\n  }\n}\n\nfunction namespace(\n  ns: string,\n): Record<DebugLevel, (...args: unknown[]) => void> {\n  return levels.reduce(\n    (logger, method) => {\n      logger[method] = debug.bind(console, method, ns);\n      return logger;\n    },\n    {} as Record<DebugLevel, (...args: unknown[]) => void>,\n  );\n}\n\nnamespace.level = (newLevel: DebugLevel | false) => {\n  level = newLevel;\n};\ndebug.level = namespace.level;\n\nexport default namespace;\n"], "mappings": "AAAA,MAAMA,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAU;AAExD,IAAIC,KAAyB,GAAG,MAAM;AAEtC,SAASC,KAAKA,CAACC,MAAkB,EAAsB;EACrD,IAAIF,KAAK,EAAE;IACT,IAAID,MAAM,CAACI,OAAO,CAACD,MAAM,CAAC,IAAIH,MAAM,CAACI,OAAO,CAACH,KAAK,CAAC,EAAE;MAAA,SAAAI,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAFnBC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;QAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;MAAA;MAGpCC,OAAO,CAACR,MAAM,CAAC,CAAC,GAAGK,IAAI,CAAC,CAAC,CAAC;IAC5B;EACF;AACF;AAEA,SAASI,SAASA,CAChBC,EAAU,EACwC;EAClD,OAAOb,MAAM,CAACc,MAAM,CAClB,CAACC,MAAM,EAAEZ,MAAM,KAAK;IAClBY,MAAM,CAACZ,MAAM,CAAC,GAAGD,KAAK,CAACc,IAAI,CAACL,OAAO,EAAER,MAAM,EAAEU,EAAE,CAAC;IAChD,OAAOE,MAAM;EACf,CAAC,EACD,CAAC,CACH,CAAC;AACH;AAEAH,SAAS,CAACX,KAAK,GAAIgB,QAA4B,IAAK;EAClDhB,KAAK,GAAGgB,QAAQ;AAClB,CAAC;AACDf,KAAK,CAACD,KAAK,GAAGW,SAAS,CAACX,KAAK;AAE7B,eAAeW,SAAS", "ignoreList": []}