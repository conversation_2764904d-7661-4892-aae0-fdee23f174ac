{"version": 3, "file": "createRegistryWithFormats.js", "names": ["Registry", "MAX_REGISTER_ITERATIONS", "CORE_FORMATS", "createRegistryWithFormats", "formats", "sourceRegistry", "debug", "registry", "for<PERSON>ach", "name", "coreBlot", "query", "register", "format", "error", "iterations", "requiredC<PERSON><PERSON>"], "sources": ["../../../src/core/utils/createRegistryWithFormats.ts"], "sourcesContent": ["import { Registry } from 'parchment';\n\nconst MAX_REGISTER_ITERATIONS = 100;\nconst CORE_FORMATS = ['block', 'break', 'cursor', 'inline', 'scroll', 'text'];\n\nconst createRegistryWithFormats = (\n  formats: string[],\n  sourceRegistry: Registry,\n  debug: { error: (errorMessage: string) => void },\n) => {\n  const registry = new Registry();\n  CORE_FORMATS.forEach((name) => {\n    const coreBlot = sourceRegistry.query(name);\n    if (coreBlot) registry.register(coreBlot);\n  });\n\n  formats.forEach((name) => {\n    let format = sourceRegistry.query(name);\n    if (!format) {\n      debug.error(\n        `Cannot register \"${name}\" specified in \"formats\" config. Are you sure it was registered?`,\n      );\n    }\n    let iterations = 0;\n    while (format) {\n      registry.register(format);\n      format = 'blotName' in format ? format.requiredContainer ?? null : null;\n\n      iterations += 1;\n      if (iterations > MAX_REGISTER_ITERATIONS) {\n        debug.error(\n          `Cycle detected in registering blot requiredContainer: \"${name}\"`,\n        );\n        break;\n      }\n    }\n  });\n\n  return registry;\n};\n\nexport default createRegistryWithFormats;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,WAAW;AAEpC,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAMC,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;AAE7E,MAAMC,yBAAyB,GAAGA,CAChCC,OAAiB,EACjBC,cAAwB,EACxBC,KAAgD,KAC7C;EACH,MAAMC,QAAQ,GAAG,IAAIP,QAAQ,CAAC,CAAC;EAC/BE,YAAY,CAACM,OAAO,CAAEC,IAAI,IAAK;IAC7B,MAAMC,QAAQ,GAAGL,cAAc,CAACM,KAAK,CAACF,IAAI,CAAC;IAC3C,IAAIC,QAAQ,EAAEH,QAAQ,CAACK,QAAQ,CAACF,QAAQ,CAAC;EAC3C,CAAC,CAAC;EAEFN,OAAO,CAACI,OAAO,CAAEC,IAAI,IAAK;IACxB,IAAII,MAAM,GAAGR,cAAc,CAACM,KAAK,CAACF,IAAI,CAAC;IACvC,IAAI,CAACI,MAAM,EAAE;MACXP,KAAK,CAACQ,KAAK,CACR,oBAAmBL,IAAK,kEAC3B,CAAC;IACH;IACA,IAAIM,UAAU,GAAG,CAAC;IAClB,OAAOF,MAAM,EAAE;MACbN,QAAQ,CAACK,QAAQ,CAACC,MAAM,CAAC;MACzBA,MAAM,GAAG,UAAU,IAAIA,MAAM,GAAGA,MAAM,CAACG,iBAAiB,IAAI,IAAI,GAAG,IAAI;MAEvED,UAAU,IAAI,CAAC;MACf,IAAIA,UAAU,GAAGd,uBAAuB,EAAE;QACxCK,KAAK,CAACQ,KAAK,CACR,0DAAyDL,IAAK,GACjE,CAAC;QACD;MACF;IACF;EACF,CAAC,CAAC;EAEF,OAAOF,QAAQ;AACjB,CAAC;AAED,eAAeJ,yBAAyB", "ignoreList": []}