{"version": 3, "file": "quill.js", "names": ["merge", "Parchment", "Delta", "Editor", "Emitter", "instances", "logger", "<PERSON><PERSON><PERSON>", "Selection", "Range", "Composition", "Theme", "scrollRectIntoView", "createRegistryWithFormats", "debug", "globalRegistry", "Registry", "ParentBlot", "uiClass", "<PERSON><PERSON><PERSON>", "DEFAULTS", "bounds", "modules", "clipboard", "keyboard", "history", "uploader", "placeholder", "readOnly", "registry", "theme", "events", "sources", "version", "imports", "delta", "parchment", "limit", "level", "find", "node", "bubble", "arguments", "length", "undefined", "get", "import", "name", "error", "register", "target", "overwrite", "attrName", "blotName", "Object", "keys", "for<PERSON>ach", "key", "path", "warn", "startsWith", "constructor", "container", "options", "expandConfig", "html", "innerHTML", "trim", "classList", "add", "set", "root", "addContainer", "emitter", "scrollBlotName", "ScrollBlot", "query", "Error", "scroll", "editor", "selection", "composition", "addModule", "init", "on", "EDITOR_CHANGE", "type", "TEXT_CHANGE", "toggle", "isBlank", "SCROLL_UPDATE", "source", "mutations", "oldRange", "<PERSON><PERSON><PERSON><PERSON>", "newRange", "getRange", "selectionInfo", "modify", "call", "update", "SCROLL_EMBED_UPDATE", "blot", "change", "retain", "offset", "statics", "USER", "contents", "convert", "text", "setContents", "clear", "setAttribute", "disable", "allowReadOnlyEdits", "refNode", "className", "document", "createElement", "insertBefore", "blur", "setRang<PERSON>", "deleteText", "index", "overload", "enable", "editReadOnly", "modifier", "value", "enabled", "focus", "preventScroll", "scrollSelectionIntoView", "format", "API", "range", "getSelection", "<PERSON><PERSON>", "BLOCK", "formatLine", "formatText", "setSelection", "SILENT", "formats", "getBounds", "containerBounds", "getBoundingClientRect", "bottom", "top", "height", "left", "right", "width", "getContents", "<PERSON><PERSON><PERSON><PERSON>", "getFormat", "getIndex", "<PERSON><PERSON><PERSON><PERSON>", "leaf", "getLine", "line", "getLines", "Number", "MAX_VALUE", "lines", "getModule", "getSemanticHTML", "getHTML", "getText", "hasFocus", "insertEmbed", "embed", "insertText", "isEnabled", "off", "once", "removeFormat", "rect", "scrollIntoView", "console", "delete1", "applied", "insertContents", "delete2", "compose", "Math", "max", "setText", "insert", "updateContents", "<PERSON><PERSON><PERSON><PERSON>", "resolveSelector", "selector", "querySelector", "expandModuleConfig", "config", "entries", "reduce", "expanded", "_ref", "omitUndefinedValuesFromOptions", "obj", "fromEntries", "filter", "entry", "containerOrSelector", "shouldUseDefaultTheme", "quillModuleDefaults", "quill<PERSON><PERSON><PERSON><PERSON>", "themeModuleDefaults", "themeDefaults", "userModuleOptions", "toolbar", "modulesWithDefaults", "_ref2", "moduleClass", "shift", "<PERSON><PERSON><PERSON><PERSON>", "shiftRange", "args", "emit", "lengthOrSource", "start", "end", "transformPosition", "map", "pos", "default"], "sources": ["../../src/core/quill.ts"], "sourcesContent": ["import { merge } from 'lodash-es';\nimport * as Parchment from 'parchment';\nimport type { Op } from 'quill-delta';\nimport Delta from 'quill-delta';\nimport type { BlockEmbed } from '../blots/block.js';\nimport type Block from '../blots/block.js';\nimport type Scroll from '../blots/scroll.js';\nimport type Clipboard from '../modules/clipboard.js';\nimport type History from '../modules/history.js';\nimport type Keyboard from '../modules/keyboard.js';\nimport type Uploader from '../modules/uploader.js';\nimport Editor from './editor.js';\nimport Emitter from './emitter.js';\nimport type { EmitterSource } from './emitter.js';\nimport instances from './instances.js';\nimport logger from './logger.js';\nimport type { DebugLevel } from './logger.js';\nimport Module from './module.js';\nimport Selection, { Range } from './selection.js';\nimport type { Bounds } from './selection.js';\nimport Composition from './composition.js';\nimport Theme from './theme.js';\nimport type { ThemeConstructor } from './theme.js';\nimport scrollRectIntoView from './utils/scrollRectIntoView.js';\nimport type { Rect } from './utils/scrollRectIntoView.js';\nimport createRegistryWithFormats from './utils/createRegistryWithFormats.js';\n\nconst debug = logger('quill');\n\nconst globalRegistry = new Parchment.Registry();\nParchment.ParentBlot.uiClass = 'ql-ui';\n\n/**\n * Options for initializing a Quill instance\n */\nexport interface QuillOptions {\n  theme?: string;\n  debug?: DebugLevel | boolean;\n  registry?: Parchment.Registry;\n  /**\n   * Whether to disable the editing\n   * @default false\n   */\n  readOnly?: boolean;\n\n  /**\n   * Placeholder text to display when the editor is empty\n   * @default \"\"\n   */\n  placeholder?: string;\n  bounds?: HTMLElement | string | null;\n  modules?: Record<string, unknown>;\n\n  /**\n   * A list of formats that are recognized and can exist within the editor contents.\n   * `null` means all formats are allowed.\n   * @default null\n   */\n  formats?: string[] | null;\n}\n\n/**\n * Similar to QuillOptions, but with all properties expanded to their default values,\n * and all selectors resolved to HTMLElements.\n */\nexport interface ExpandedQuillOptions\n  extends Omit<QuillOptions, 'theme' | 'formats'> {\n  theme: ThemeConstructor;\n  registry: Parchment.Registry;\n  container: HTMLElement;\n  modules: Record<string, unknown>;\n  bounds?: HTMLElement | null;\n  readOnly: boolean;\n}\n\nclass Quill {\n  static DEFAULTS = {\n    bounds: null,\n    modules: {\n      clipboard: true,\n      keyboard: true,\n      history: true,\n      uploader: true,\n    },\n    placeholder: '',\n    readOnly: false,\n    registry: globalRegistry,\n    theme: 'default',\n  } satisfies Partial<QuillOptions>;\n  static events = Emitter.events;\n  static sources = Emitter.sources;\n  static version = typeof QUILL_VERSION === 'undefined' ? 'dev' : QUILL_VERSION;\n\n  static imports: Record<string, unknown> = {\n    delta: Delta,\n    parchment: Parchment,\n    'core/module': Module,\n    'core/theme': Theme,\n  };\n\n  static debug(limit: DebugLevel | boolean) {\n    if (limit === true) {\n      limit = 'log';\n    }\n    logger.level(limit);\n  }\n\n  static find(node: Node, bubble = false) {\n    return instances.get(node) || globalRegistry.find(node, bubble);\n  }\n\n  static import(name: 'core/module'): typeof Module;\n  static import(name: `themes/${string}`): typeof Theme;\n  static import(name: 'parchment'): typeof Parchment;\n  static import(name: 'delta'): typeof Delta;\n  static import(name: string): unknown;\n  static import(name: string) {\n    if (this.imports[name] == null) {\n      debug.error(`Cannot import ${name}. Are you sure it was registered?`);\n    }\n    return this.imports[name];\n  }\n\n  static register(\n    targets: Record<\n      string,\n      | Parchment.RegistryDefinition\n      | Record<string, unknown> // any objects\n      | Theme\n      | Module\n      | Function // ES5 constructors\n    >,\n    overwrite?: boolean,\n  ): void;\n  static register(\n    target: Parchment.RegistryDefinition,\n    overwrite?: boolean,\n  ): void;\n  static register(path: string, target: any, overwrite?: boolean): void;\n  static register(...args: any[]): void {\n    if (typeof args[0] !== 'string') {\n      const target = args[0];\n      const overwrite = !!args[1];\n\n      const name = 'attrName' in target ? target.attrName : target.blotName;\n      if (typeof name === 'string') {\n        // Shortcut for formats:\n        // register(Blot | Attributor, overwrite)\n        this.register(`formats/${name}`, target, overwrite);\n      } else {\n        Object.keys(target).forEach((key) => {\n          this.register(key, target[key], overwrite);\n        });\n      }\n    } else {\n      const path = args[0];\n      const target = args[1];\n      const overwrite = !!args[2];\n\n      if (this.imports[path] != null && !overwrite) {\n        debug.warn(`Overwriting ${path} with`, target);\n      }\n      this.imports[path] = target;\n      if (\n        (path.startsWith('blots/') || path.startsWith('formats/')) &&\n        target &&\n        typeof target !== 'boolean' &&\n        target.blotName !== 'abstract'\n      ) {\n        globalRegistry.register(target);\n      }\n      if (typeof target.register === 'function') {\n        target.register(globalRegistry);\n      }\n    }\n  }\n\n  container: HTMLElement;\n  root: HTMLDivElement;\n  scroll: Scroll;\n  emitter: Emitter;\n  protected allowReadOnlyEdits: boolean;\n  editor: Editor;\n  composition: Composition;\n  selection: Selection;\n\n  theme: Theme;\n  keyboard: Keyboard;\n  clipboard: Clipboard;\n  history: History;\n  uploader: Uploader;\n\n  options: ExpandedQuillOptions;\n\n  constructor(container: HTMLElement | string, options: QuillOptions = {}) {\n    this.options = expandConfig(container, options);\n    this.container = this.options.container;\n    if (this.container == null) {\n      debug.error('Invalid Quill container', container);\n      return;\n    }\n    if (this.options.debug) {\n      Quill.debug(this.options.debug);\n    }\n    const html = this.container.innerHTML.trim();\n    this.container.classList.add('ql-container');\n    this.container.innerHTML = '';\n    instances.set(this.container, this);\n    this.root = this.addContainer('ql-editor');\n    this.root.classList.add('ql-blank');\n    this.emitter = new Emitter();\n    const scrollBlotName = Parchment.ScrollBlot.blotName;\n    const ScrollBlot = this.options.registry.query(scrollBlotName);\n    if (!ScrollBlot || !('blotName' in ScrollBlot)) {\n      throw new Error(\n        `Cannot initialize Quill without \"${scrollBlotName}\" blot`,\n      );\n    }\n    this.scroll = new ScrollBlot(this.options.registry, this.root, {\n      emitter: this.emitter,\n    }) as Scroll;\n    this.editor = new Editor(this.scroll);\n    this.selection = new Selection(this.scroll, this.emitter);\n    this.composition = new Composition(this.scroll, this.emitter);\n    this.theme = new this.options.theme(this, this.options); // eslint-disable-line new-cap\n    this.keyboard = this.theme.addModule('keyboard');\n    this.clipboard = this.theme.addModule('clipboard');\n    this.history = this.theme.addModule('history');\n    this.uploader = this.theme.addModule('uploader');\n    this.theme.addModule('input');\n    this.theme.addModule('uiNode');\n    this.theme.init();\n    this.emitter.on(Emitter.events.EDITOR_CHANGE, (type) => {\n      if (type === Emitter.events.TEXT_CHANGE) {\n        this.root.classList.toggle('ql-blank', this.editor.isBlank());\n      }\n    });\n    this.emitter.on(Emitter.events.SCROLL_UPDATE, (source, mutations) => {\n      const oldRange = this.selection.lastRange;\n      const [newRange] = this.selection.getRange();\n      const selectionInfo =\n        oldRange && newRange ? { oldRange, newRange } : undefined;\n      modify.call(\n        this,\n        () => this.editor.update(null, mutations, selectionInfo),\n        source,\n      );\n    });\n    this.emitter.on(Emitter.events.SCROLL_EMBED_UPDATE, (blot, delta) => {\n      const oldRange = this.selection.lastRange;\n      const [newRange] = this.selection.getRange();\n      const selectionInfo =\n        oldRange && newRange ? { oldRange, newRange } : undefined;\n      modify.call(\n        this,\n        () => {\n          const change = new Delta()\n            .retain(blot.offset(this))\n            .retain({ [blot.statics.blotName]: delta });\n          return this.editor.update(change, [], selectionInfo);\n        },\n        Quill.sources.USER,\n      );\n    });\n    if (html) {\n      const contents = this.clipboard.convert({\n        html: `${html}<p><br></p>`,\n        text: '\\n',\n      });\n      this.setContents(contents);\n    }\n    this.history.clear();\n    if (this.options.placeholder) {\n      this.root.setAttribute('data-placeholder', this.options.placeholder);\n    }\n    if (this.options.readOnly) {\n      this.disable();\n    }\n    this.allowReadOnlyEdits = false;\n  }\n\n  addContainer(container: string, refNode?: Node | null): HTMLDivElement;\n  addContainer(container: HTMLElement, refNode?: Node | null): HTMLElement;\n  addContainer(\n    container: string | HTMLElement,\n    refNode: Node | null = null,\n  ): HTMLDivElement | HTMLElement {\n    if (typeof container === 'string') {\n      const className = container;\n      container = document.createElement('div');\n      container.classList.add(className);\n    }\n    this.container.insertBefore(container, refNode);\n    return container;\n  }\n\n  blur() {\n    this.selection.setRange(null);\n  }\n\n  deleteText(range: Range, source?: EmitterSource): Delta;\n  deleteText(index: number, length: number, source?: EmitterSource): Delta;\n  deleteText(\n    index: number | Range,\n    length?: number | EmitterSource,\n    source?: EmitterSource,\n  ): Delta {\n    // @ts-expect-error\n    [index, length, , source] = overload(index, length, source);\n    return modify.call(\n      this,\n      () => {\n        return this.editor.deleteText(index, length);\n      },\n      source,\n      index,\n      -1 * length,\n    );\n  }\n\n  disable() {\n    this.enable(false);\n  }\n\n  editReadOnly<T>(modifier: () => T): T {\n    this.allowReadOnlyEdits = true;\n    const value = modifier();\n    this.allowReadOnlyEdits = false;\n    return value;\n  }\n\n  enable(enabled = true) {\n    this.scroll.enable(enabled);\n    this.container.classList.toggle('ql-disabled', !enabled);\n  }\n\n  focus(options: { preventScroll?: boolean } = {}) {\n    this.selection.focus();\n    if (!options.preventScroll) {\n      this.scrollSelectionIntoView();\n    }\n  }\n\n  format(\n    name: string,\n    value: unknown,\n    source: EmitterSource = Emitter.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        const range = this.getSelection(true);\n        let change = new Delta();\n        if (range == null) return change;\n        if (this.scroll.query(name, Parchment.Scope.BLOCK)) {\n          change = this.editor.formatLine(range.index, range.length, {\n            [name]: value,\n          });\n        } else if (range.length === 0) {\n          this.selection.format(name, value);\n          return change;\n        } else {\n          change = this.editor.formatText(range.index, range.length, {\n            [name]: value,\n          });\n        }\n        this.setSelection(range, Emitter.sources.SILENT);\n        return change;\n      },\n      source,\n    );\n  }\n\n  formatLine(\n    index: number,\n    length: number,\n    formats: Record<string, unknown>,\n    source?: EmitterSource,\n  ): Delta;\n  formatLine(\n    index: number,\n    length: number,\n    name: string,\n    value?: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  formatLine(\n    index: number,\n    length: number,\n    name: string | Record<string, unknown>,\n    value?: unknown | EmitterSource,\n    source?: EmitterSource,\n  ): Delta {\n    let formats: Record<string, unknown>;\n    // eslint-disable-next-line prefer-const\n    [index, length, formats, source] = overload(\n      index,\n      length,\n      // @ts-expect-error\n      name,\n      value,\n      source,\n    );\n    return modify.call(\n      this,\n      () => {\n        return this.editor.formatLine(index, length, formats);\n      },\n      source,\n      index,\n      0,\n    );\n  }\n\n  formatText(\n    range: Range,\n    name: string,\n    value: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  formatText(\n    index: number,\n    length: number,\n    name: string,\n    value: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  formatText(\n    index: number,\n    length: number,\n    formats: Record<string, unknown>,\n    source?: EmitterSource,\n  ): Delta;\n  formatText(\n    index: number | Range,\n    length: number | string,\n    name: string | unknown,\n    value?: unknown | EmitterSource,\n    source?: EmitterSource,\n  ): Delta {\n    let formats: Record<string, unknown>;\n    // eslint-disable-next-line prefer-const\n    [index, length, formats, source] = overload(\n      // @ts-expect-error\n      index,\n      length,\n      name,\n      value,\n      source,\n    );\n    return modify.call(\n      this,\n      () => {\n        return this.editor.formatText(index, length, formats);\n      },\n      source,\n      index,\n      0,\n    );\n  }\n\n  getBounds(index: number | Range, length = 0): Bounds | null {\n    let bounds: Bounds | null = null;\n    if (typeof index === 'number') {\n      bounds = this.selection.getBounds(index, length);\n    } else {\n      bounds = this.selection.getBounds(index.index, index.length);\n    }\n    if (!bounds) return null;\n    const containerBounds = this.container.getBoundingClientRect();\n    return {\n      bottom: bounds.bottom - containerBounds.top,\n      height: bounds.height,\n      left: bounds.left - containerBounds.left,\n      right: bounds.right - containerBounds.left,\n      top: bounds.top - containerBounds.top,\n      width: bounds.width,\n    };\n  }\n\n  getContents(index = 0, length = this.getLength() - index) {\n    [index, length] = overload(index, length);\n    return this.editor.getContents(index, length);\n  }\n\n  getFormat(index?: number, length?: number): { [format: string]: unknown };\n  getFormat(range?: Range): {\n    [format: string]: unknown;\n  };\n  getFormat(\n    index: Range | number = this.getSelection(true),\n    length = 0,\n  ): { [format: string]: unknown } {\n    if (typeof index === 'number') {\n      return this.editor.getFormat(index, length);\n    }\n    return this.editor.getFormat(index.index, index.length);\n  }\n\n  getIndex(blot: Parchment.Blot) {\n    return blot.offset(this.scroll);\n  }\n\n  getLength() {\n    return this.scroll.length();\n  }\n\n  getLeaf(index: number) {\n    return this.scroll.leaf(index);\n  }\n\n  getLine(index: number) {\n    return this.scroll.line(index);\n  }\n\n  getLines(range: Range): (Block | BlockEmbed)[];\n  getLines(index?: number, length?: number): (Block | BlockEmbed)[];\n  getLines(\n    index: Range | number = 0,\n    length = Number.MAX_VALUE,\n  ): (Block | BlockEmbed)[] {\n    if (typeof index !== 'number') {\n      return this.scroll.lines(index.index, index.length);\n    }\n    return this.scroll.lines(index, length);\n  }\n\n  getModule(name: string) {\n    return this.theme.modules[name];\n  }\n\n  getSelection(focus: true): Range;\n  getSelection(focus?: boolean): Range | null;\n  getSelection(focus = false): Range | null {\n    if (focus) this.focus();\n    this.update(); // Make sure we access getRange with editor in consistent state\n    return this.selection.getRange()[0];\n  }\n\n  getSemanticHTML(range: Range): string;\n  getSemanticHTML(index?: number, length?: number): string;\n  getSemanticHTML(index: Range | number = 0, length?: number) {\n    if (typeof index === 'number') {\n      length = length ?? this.getLength() - index;\n    }\n    // @ts-expect-error\n    [index, length] = overload(index, length);\n    return this.editor.getHTML(index, length);\n  }\n\n  getText(range?: Range): string;\n  getText(index?: number, length?: number): string;\n  getText(index: Range | number = 0, length?: number): string {\n    if (typeof index === 'number') {\n      length = length ?? this.getLength() - index;\n    }\n    // @ts-expect-error\n    [index, length] = overload(index, length);\n    return this.editor.getText(index, length);\n  }\n\n  hasFocus() {\n    return this.selection.hasFocus();\n  }\n\n  insertEmbed(\n    index: number,\n    embed: string,\n    value: unknown,\n    source: EmitterSource = Quill.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        return this.editor.insertEmbed(index, embed, value);\n      },\n      source,\n      index,\n    );\n  }\n\n  insertText(index: number, text: string, source?: EmitterSource): Delta;\n  insertText(\n    index: number,\n    text: string,\n    formats: Record<string, unknown>,\n    source?: EmitterSource,\n  ): Delta;\n  insertText(\n    index: number,\n    text: string,\n    name: string,\n    value: unknown,\n    source?: EmitterSource,\n  ): Delta;\n  insertText(\n    index: number,\n    text: string,\n    name?: string | Record<string, unknown> | EmitterSource,\n    value?: unknown,\n    source?: EmitterSource,\n  ): Delta {\n    let formats: Record<string, unknown>;\n    // eslint-disable-next-line prefer-const\n    // @ts-expect-error\n    [index, , formats, source] = overload(index, 0, name, value, source);\n    return modify.call(\n      this,\n      () => {\n        return this.editor.insertText(index, text, formats);\n      },\n      source,\n      index,\n      text.length,\n    );\n  }\n\n  isEnabled() {\n    return this.scroll.isEnabled();\n  }\n\n  off(...args: Parameters<(typeof Emitter)['prototype']['off']>) {\n    return this.emitter.off(...args);\n  }\n\n  on(\n    event: (typeof Emitter)['events']['TEXT_CHANGE'],\n    handler: (delta: Delta, oldContent: Delta, source: EmitterSource) => void,\n  ): Emitter;\n  on(\n    event: (typeof Emitter)['events']['SELECTION_CHANGE'],\n    handler: (range: Range, oldRange: Range, source: EmitterSource) => void,\n  ): Emitter;\n  on(\n    event: (typeof Emitter)['events']['EDITOR_CHANGE'],\n    handler: (\n      ...args:\n        | [\n            (typeof Emitter)['events']['TEXT_CHANGE'],\n            Delta,\n            Delta,\n            EmitterSource,\n          ]\n        | [\n            (typeof Emitter)['events']['SELECTION_CHANGE'],\n            Range,\n            Range,\n            EmitterSource,\n          ]\n    ) => void,\n  ): Emitter;\n  on(event: string, ...args: unknown[]): Emitter;\n  on(...args: Parameters<(typeof Emitter)['prototype']['on']>): Emitter {\n    return this.emitter.on(...args);\n  }\n\n  once(...args: Parameters<(typeof Emitter)['prototype']['once']>) {\n    return this.emitter.once(...args);\n  }\n\n  removeFormat(index: number, length: number, source?: EmitterSource): Delta {\n    [index, length, , source] = overload(index, length, source);\n    return modify.call(\n      this,\n      () => {\n        return this.editor.removeFormat(index, length);\n      },\n      source,\n      index,\n    );\n  }\n\n  scrollRectIntoView(rect: Rect) {\n    scrollRectIntoView(this.root, rect);\n  }\n\n  /**\n   * @deprecated Use Quill#scrollSelectionIntoView() instead.\n   */\n  scrollIntoView() {\n    console.warn(\n      'Quill#scrollIntoView() has been deprecated and will be removed in the near future. Please use Quill#scrollSelectionIntoView() instead.',\n    );\n    this.scrollSelectionIntoView();\n  }\n\n  /**\n   * Scroll the current selection into the visible area.\n   * If the selection is already visible, no scrolling will occur.\n   */\n  scrollSelectionIntoView() {\n    const range = this.selection.lastRange;\n    const bounds = range && this.selection.getBounds(range.index, range.length);\n    if (bounds) {\n      this.scrollRectIntoView(bounds);\n    }\n  }\n\n  setContents(\n    delta: Delta | Op[],\n    source: EmitterSource = Emitter.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        delta = new Delta(delta);\n        const length = this.getLength();\n        // Quill will set empty editor to \\n\n        const delete1 = this.editor.deleteText(0, length);\n        const applied = this.editor.insertContents(0, delta);\n        // Remove extra \\n from empty editor initialization\n        const delete2 = this.editor.deleteText(this.getLength() - 1, 1);\n        return delete1.compose(applied).compose(delete2);\n      },\n      source,\n    );\n  }\n  setSelection(range: Range | null, source?: EmitterSource): void;\n  setSelection(index: number, source?: EmitterSource): void;\n  setSelection(index: number, length?: number, source?: EmitterSource): void;\n  setSelection(index: number, source?: EmitterSource): void;\n  setSelection(\n    index: Range | null | number,\n    length?: EmitterSource | number,\n    source?: EmitterSource,\n  ): void {\n    if (index == null) {\n      // @ts-expect-error https://github.com/microsoft/TypeScript/issues/22609\n      this.selection.setRange(null, length || Quill.sources.API);\n    } else {\n      // @ts-expect-error\n      [index, length, , source] = overload(index, length, source);\n      this.selection.setRange(new Range(Math.max(0, index), length), source);\n      if (source !== Emitter.sources.SILENT) {\n        this.scrollSelectionIntoView();\n      }\n    }\n  }\n\n  setText(text: string, source: EmitterSource = Emitter.sources.API) {\n    const delta = new Delta().insert(text);\n    return this.setContents(delta, source);\n  }\n\n  update(source: EmitterSource = Emitter.sources.USER) {\n    const change = this.scroll.update(source); // Will update selection before selection.update() does if text changes\n    this.selection.update(source);\n    // TODO this is usually undefined\n    return change;\n  }\n\n  updateContents(\n    delta: Delta | Op[],\n    source: EmitterSource = Emitter.sources.API,\n  ): Delta {\n    return modify.call(\n      this,\n      () => {\n        delta = new Delta(delta);\n        return this.editor.applyDelta(delta);\n      },\n      source,\n      true,\n    );\n  }\n}\n\nfunction resolveSelector(selector: string | HTMLElement | null | undefined) {\n  return typeof selector === 'string'\n    ? document.querySelector<HTMLElement>(selector)\n    : selector;\n}\n\nfunction expandModuleConfig(config: Record<string, unknown> | undefined) {\n  return Object.entries(config ?? {}).reduce(\n    (expanded, [key, value]) => ({\n      ...expanded,\n      [key]: value === true ? {} : value,\n    }),\n    {} as Record<string, unknown>,\n  );\n}\n\nfunction omitUndefinedValuesFromOptions(obj: QuillOptions) {\n  return Object.fromEntries(\n    Object.entries(obj).filter((entry) => entry[1] !== undefined),\n  );\n}\n\nfunction expandConfig(\n  containerOrSelector: HTMLElement | string,\n  options: QuillOptions,\n): ExpandedQuillOptions {\n  const container = resolveSelector(containerOrSelector);\n  if (!container) {\n    throw new Error('Invalid Quill container');\n  }\n\n  const shouldUseDefaultTheme =\n    !options.theme || options.theme === Quill.DEFAULTS.theme;\n  const theme = shouldUseDefaultTheme\n    ? Theme\n    : Quill.import(`themes/${options.theme}`);\n  if (!theme) {\n    throw new Error(`Invalid theme ${options.theme}. Did you register it?`);\n  }\n\n  const { modules: quillModuleDefaults, ...quillDefaults } = Quill.DEFAULTS;\n  const { modules: themeModuleDefaults, ...themeDefaults } = theme.DEFAULTS;\n\n  let userModuleOptions = expandModuleConfig(options.modules);\n  // Special case toolbar shorthand\n  if (\n    userModuleOptions != null &&\n    userModuleOptions.toolbar &&\n    userModuleOptions.toolbar.constructor !== Object\n  ) {\n    userModuleOptions = {\n      ...userModuleOptions,\n      toolbar: { container: userModuleOptions.toolbar },\n    };\n  }\n\n  const modules: ExpandedQuillOptions['modules'] = merge(\n    {},\n    expandModuleConfig(quillModuleDefaults),\n    expandModuleConfig(themeModuleDefaults),\n    userModuleOptions,\n  );\n\n  const config = {\n    ...quillDefaults,\n    ...omitUndefinedValuesFromOptions(themeDefaults),\n    ...omitUndefinedValuesFromOptions(options),\n  };\n\n  let registry = options.registry;\n  if (registry) {\n    if (options.formats) {\n      debug.warn('Ignoring \"formats\" option because \"registry\" is specified');\n    }\n  } else {\n    registry = options.formats\n      ? createRegistryWithFormats(options.formats, config.registry, debug)\n      : config.registry;\n  }\n\n  return {\n    ...config,\n    registry,\n    container,\n    theme,\n    modules: Object.entries(modules).reduce(\n      (modulesWithDefaults, [name, value]) => {\n        if (!value) return modulesWithDefaults;\n\n        const moduleClass = Quill.import(`modules/${name}`);\n        if (moduleClass == null) {\n          debug.error(\n            `Cannot load ${name} module. Are you sure you registered it?`,\n          );\n          return modulesWithDefaults;\n        }\n        return {\n          ...modulesWithDefaults,\n          // @ts-expect-error\n          [name]: merge({}, moduleClass.DEFAULTS || {}, value),\n        };\n      },\n      {},\n    ),\n    bounds: resolveSelector(config.bounds),\n  };\n}\n\n// Handle selection preservation and TEXT_CHANGE emission\n// common to modification APIs\nfunction modify(\n  modifier: () => Delta,\n  source: EmitterSource,\n  index: number | boolean,\n  shift: number | null,\n) {\n  if (\n    !this.isEnabled() &&\n    source === Emitter.sources.USER &&\n    !this.allowReadOnlyEdits\n  ) {\n    return new Delta();\n  }\n  let range = index == null ? null : this.getSelection();\n  const oldDelta = this.editor.delta;\n  const change = modifier();\n  if (range != null) {\n    if (index === true) {\n      index = range.index; // eslint-disable-line prefer-destructuring\n    }\n    if (shift == null) {\n      range = shiftRange(range, change, source);\n    } else if (shift !== 0) {\n      // @ts-expect-error index should always be number\n      range = shiftRange(range, index, shift, source);\n    }\n    this.setSelection(range, Emitter.sources.SILENT);\n  }\n  if (change.length() > 0) {\n    const args = [Emitter.events.TEXT_CHANGE, change, oldDelta, source];\n    this.emitter.emit(Emitter.events.EDITOR_CHANGE, ...args);\n    if (source !== Emitter.sources.SILENT) {\n      this.emitter.emit(...args);\n    }\n  }\n  return change;\n}\n\ntype NormalizedIndexLength = [\n  number,\n  number,\n  Record<string, unknown>,\n  EmitterSource,\n];\nfunction overload(index: number, source?: EmitterSource): NormalizedIndexLength;\nfunction overload(\n  index: number,\n  length: number,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  index: number,\n  length: number,\n  format: string,\n  value: unknown,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  index: number,\n  length: number,\n  format: Record<string, unknown>,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(range: Range, source?: EmitterSource): NormalizedIndexLength;\nfunction overload(\n  range: Range,\n  format: string,\n  value: unknown,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  range: Range,\n  format: Record<string, unknown>,\n  source?: EmitterSource,\n): NormalizedIndexLength;\nfunction overload(\n  index: Range | number,\n  length?: number | string | Record<string, unknown> | EmitterSource,\n  name?: string | unknown | Record<string, unknown> | EmitterSource,\n  value?: unknown | EmitterSource,\n  source?: EmitterSource,\n): NormalizedIndexLength {\n  let formats: Record<string, unknown> = {};\n  // @ts-expect-error\n  if (typeof index.index === 'number' && typeof index.length === 'number') {\n    // Allow for throwaway end (used by insertText/insertEmbed)\n    if (typeof length !== 'number') {\n      // @ts-expect-error\n      source = value;\n      value = name;\n      name = length;\n      // @ts-expect-error\n      length = index.length; // eslint-disable-line prefer-destructuring\n      // @ts-expect-error\n      index = index.index; // eslint-disable-line prefer-destructuring\n    } else {\n      // @ts-expect-error\n      length = index.length; // eslint-disable-line prefer-destructuring\n      // @ts-expect-error\n      index = index.index; // eslint-disable-line prefer-destructuring\n    }\n  } else if (typeof length !== 'number') {\n    // @ts-expect-error\n    source = value;\n    value = name;\n    name = length;\n    length = 0;\n  }\n  // Handle format being object, two format name/value strings or excluded\n  if (typeof name === 'object') {\n    // @ts-expect-error Fix me later\n    formats = name;\n    // @ts-expect-error\n    source = value;\n  } else if (typeof name === 'string') {\n    if (value != null) {\n      formats[name] = value;\n    } else {\n      // @ts-expect-error\n      source = name;\n    }\n  }\n  // Handle optional source\n  source = source || Emitter.sources.API;\n  // @ts-expect-error\n  return [index, length, formats, source];\n}\n\nfunction shiftRange(range: Range, change: Delta, source?: EmitterSource): Range;\nfunction shiftRange(\n  range: Range,\n  index: number,\n  length?: number,\n  source?: EmitterSource,\n): Range;\nfunction shiftRange(\n  range: Range,\n  index: number | Delta,\n  lengthOrSource?: number | EmitterSource,\n  source?: EmitterSource,\n) {\n  const length = typeof lengthOrSource === 'number' ? lengthOrSource : 0;\n  if (range == null) return null;\n  let start;\n  let end;\n  // @ts-expect-error -- TODO: add a better type guard around `index`\n  if (index && typeof index.transformPosition === 'function') {\n    [start, end] = [range.index, range.index + range.length].map((pos) =>\n      // @ts-expect-error -- TODO: add a better type guard around `index`\n      index.transformPosition(pos, source !== Emitter.sources.USER),\n    );\n  } else {\n    [start, end] = [range.index, range.index + range.length].map((pos) => {\n      // @ts-expect-error -- TODO: add a better type guard around `index`\n      if (pos < index || (pos === index && source === Emitter.sources.USER))\n        return pos;\n      if (length >= 0) {\n        return pos + length;\n      }\n      // @ts-expect-error -- TODO: add a better type guard around `index`\n      return Math.max(index, pos + length);\n    });\n  }\n  return new Range(start, end - start);\n}\n\nexport type { Bounds, DebugLevel, EmitterSource };\nexport { Parchment, Range };\n\nexport { globalRegistry, expandConfig, overload, Quill as default };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,WAAW;AACjC,OAAO,KAAKC,SAAS,MAAM,WAAW;AAEtC,OAAOC,KAAK,MAAM,aAAa;AAQ/B,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,OAAO,MAAM,cAAc;AAElC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,MAAM,MAAM,aAAa;AAEhC,OAAOC,MAAM,MAAM,aAAa;AAChC,OAAOC,SAAS,IAAIC,KAAK,QAAQ,gBAAgB;AAEjD,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,KAAK,MAAM,YAAY;AAE9B,OAAOC,kBAAkB,MAAM,+BAA+B;AAE9D,OAAOC,yBAAyB,MAAM,sCAAsC;AAE5E,MAAMC,KAAK,GAAGR,MAAM,CAAC,OAAO,CAAC;AAE7B,MAAMS,cAAc,GAAG,IAAId,SAAS,CAACe,QAAQ,CAAC,CAAC;AAC/Cf,SAAS,CAACgB,UAAU,CAACC,OAAO,GAAG,OAAO;;AAEtC;AACA;AACA;;AA2BA;AACA;AACA;AACA;;AAWA,MAAMC,KAAK,CAAC;EACV,OAAOC,QAAQ,GAAG;IAChBC,MAAM,EAAE,IAAI;IACZC,OAAO,EAAE;MACPC,SAAS,EAAE,IAAI;MACfC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC;IACDC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAEd,cAAc;IACxBe,KAAK,EAAE;EACT,CAAC;EACD,OAAOC,MAAM,GAAG3B,OAAO,CAAC2B,MAAM;EAC9B,OAAOC,OAAO,GAAG5B,OAAO,CAAC4B,OAAO;EAChC,OAAOC,OAAO,GAAG,cAAoB,KAAK,WAAW,GAAG,KAAK,UAAgB;EAE7E,OAAOC,OAAO,GAA4B;IACxCC,KAAK,EAAEjC,KAAK;IACZkC,SAAS,EAAEnC,SAAS;IACpB,aAAa,EAAEM,MAAM;IACrB,YAAY,EAAEI;EAChB,CAAC;EAED,OAAOG,KAAKA,CAACuB,KAA2B,EAAE;IACxC,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBA,KAAK,GAAG,KAAK;IACf;IACA/B,MAAM,CAACgC,KAAK,CAACD,KAAK,CAAC;EACrB;EAEA,OAAOE,IAAIA,CAACC,IAAU,EAAkB;IAAA,IAAhBC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACpC,OAAOrC,SAAS,CAACwC,GAAG,CAACL,IAAI,CAAC,IAAIzB,cAAc,CAACwB,IAAI,CAACC,IAAI,EAAEC,MAAM,CAAC;EACjE;EAOA,OAAOK,MAAMA,CAACC,IAAY,EAAE;IAC1B,IAAI,IAAI,CAACb,OAAO,CAACa,IAAI,CAAC,IAAI,IAAI,EAAE;MAC9BjC,KAAK,CAACkC,KAAK,CAAE,iBAAgBD,IAAK,mCAAkC,CAAC;IACvE;IACA,OAAO,IAAI,CAACb,OAAO,CAACa,IAAI,CAAC;EAC3B;EAkBA,OAAOE,QAAQA,CAAA,EAAuB;IACpC,IAAI,QAAAP,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAc,KAAK,QAAQ,EAAE;MAC/B,MAAMQ,MAAM,GAAAR,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;MACtB,MAAMS,SAAS,GAAG,CAAC,EAAAT,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAQ;MAE3B,MAAMK,IAAI,GAAG,UAAU,IAAIG,MAAM,GAAGA,MAAM,CAACE,QAAQ,GAAGF,MAAM,CAACG,QAAQ;MACrE,IAAI,OAAON,IAAI,KAAK,QAAQ,EAAE;QAC5B;QACA;QACA,IAAI,CAACE,QAAQ,CAAE,WAAUF,IAAK,EAAC,EAAEG,MAAM,EAAEC,SAAS,CAAC;MACrD,CAAC,MAAM;QACLG,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,OAAO,CAAEC,GAAG,IAAK;UACnC,IAAI,CAACR,QAAQ,CAACQ,GAAG,EAAEP,MAAM,CAACO,GAAG,CAAC,EAAEN,SAAS,CAAC;QAC5C,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL,MAAMO,IAAI,GAAAhB,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;MACpB,MAAMQ,MAAM,GAAAR,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,GAAU;MACtB,MAAMS,SAAS,GAAG,CAAC,EAAAT,SAAA,CAAAC,MAAA,QAAAC,SAAA,GAAAF,SAAA,IAAQ;MAE3B,IAAI,IAAI,CAACR,OAAO,CAACwB,IAAI,CAAC,IAAI,IAAI,IAAI,CAACP,SAAS,EAAE;QAC5CrC,KAAK,CAAC6C,IAAI,CAAE,eAAcD,IAAK,OAAM,EAAER,MAAM,CAAC;MAChD;MACA,IAAI,CAAChB,OAAO,CAACwB,IAAI,CAAC,GAAGR,MAAM;MAC3B,IACE,CAACQ,IAAI,CAACE,UAAU,CAAC,QAAQ,CAAC,IAAIF,IAAI,CAACE,UAAU,CAAC,UAAU,CAAC,KACzDV,MAAM,IACN,OAAOA,MAAM,KAAK,SAAS,IAC3BA,MAAM,CAACG,QAAQ,KAAK,UAAU,EAC9B;QACAtC,cAAc,CAACkC,QAAQ,CAACC,MAAM,CAAC;MACjC;MACA,IAAI,OAAOA,MAAM,CAACD,QAAQ,KAAK,UAAU,EAAE;QACzCC,MAAM,CAACD,QAAQ,CAAClC,cAAc,CAAC;MACjC;IACF;EACF;EAmBA8C,WAAWA,CAACC,SAA+B,EAA8B;IAAA,IAA5BC,OAAqB,GAAArB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACrE,IAAI,CAACqB,OAAO,GAAGC,YAAY,CAACF,SAAS,EAAEC,OAAO,CAAC;IAC/C,IAAI,CAACD,SAAS,GAAG,IAAI,CAACC,OAAO,CAACD,SAAS;IACvC,IAAI,IAAI,CAACA,SAAS,IAAI,IAAI,EAAE;MAC1BhD,KAAK,CAACkC,KAAK,CAAC,yBAAyB,EAAEc,SAAS,CAAC;MACjD;IACF;IACA,IAAI,IAAI,CAACC,OAAO,CAACjD,KAAK,EAAE;MACtBK,KAAK,CAACL,KAAK,CAAC,IAAI,CAACiD,OAAO,CAACjD,KAAK,CAAC;IACjC;IACA,MAAMmD,IAAI,GAAG,IAAI,CAACH,SAAS,CAACI,SAAS,CAACC,IAAI,CAAC,CAAC;IAC5C,IAAI,CAACL,SAAS,CAACM,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;IAC5C,IAAI,CAACP,SAAS,CAACI,SAAS,GAAG,EAAE;IAC7B7D,SAAS,CAACiE,GAAG,CAAC,IAAI,CAACR,SAAS,EAAE,IAAI,CAAC;IACnC,IAAI,CAACS,IAAI,GAAG,IAAI,CAACC,YAAY,CAAC,WAAW,CAAC;IAC1C,IAAI,CAACD,IAAI,CAACH,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;IACnC,IAAI,CAACI,OAAO,GAAG,IAAIrE,OAAO,CAAC,CAAC;IAC5B,MAAMsE,cAAc,GAAGzE,SAAS,CAAC0E,UAAU,CAACtB,QAAQ;IACpD,MAAMsB,UAAU,GAAG,IAAI,CAACZ,OAAO,CAAClC,QAAQ,CAAC+C,KAAK,CAACF,cAAc,CAAC;IAC9D,IAAI,CAACC,UAAU,IAAI,EAAE,UAAU,IAAIA,UAAU,CAAC,EAAE;MAC9C,MAAM,IAAIE,KAAK,CACZ,oCAAmCH,cAAe,QACrD,CAAC;IACH;IACA,IAAI,CAACI,MAAM,GAAG,IAAIH,UAAU,CAAC,IAAI,CAACZ,OAAO,CAAClC,QAAQ,EAAE,IAAI,CAAC0C,IAAI,EAAE;MAC7DE,OAAO,EAAE,IAAI,CAACA;IAChB,CAAC,CAAW;IACZ,IAAI,CAACM,MAAM,GAAG,IAAI5E,MAAM,CAAC,IAAI,CAAC2E,MAAM,CAAC;IACrC,IAAI,CAACE,SAAS,GAAG,IAAIxE,SAAS,CAAC,IAAI,CAACsE,MAAM,EAAE,IAAI,CAACL,OAAO,CAAC;IACzD,IAAI,CAACQ,WAAW,GAAG,IAAIvE,WAAW,CAAC,IAAI,CAACoE,MAAM,EAAE,IAAI,CAACL,OAAO,CAAC;IAC7D,IAAI,CAAC3C,KAAK,GAAG,IAAI,IAAI,CAACiC,OAAO,CAACjC,KAAK,CAAC,IAAI,EAAE,IAAI,CAACiC,OAAO,CAAC,CAAC,CAAC;IACzD,IAAI,CAACvC,QAAQ,GAAG,IAAI,CAACM,KAAK,CAACoD,SAAS,CAAC,UAAU,CAAC;IAChD,IAAI,CAAC3D,SAAS,GAAG,IAAI,CAACO,KAAK,CAACoD,SAAS,CAAC,WAAW,CAAC;IAClD,IAAI,CAACzD,OAAO,GAAG,IAAI,CAACK,KAAK,CAACoD,SAAS,CAAC,SAAS,CAAC;IAC9C,IAAI,CAACxD,QAAQ,GAAG,IAAI,CAACI,KAAK,CAACoD,SAAS,CAAC,UAAU,CAAC;IAChD,IAAI,CAACpD,KAAK,CAACoD,SAAS,CAAC,OAAO,CAAC;IAC7B,IAAI,CAACpD,KAAK,CAACoD,SAAS,CAAC,QAAQ,CAAC;IAC9B,IAAI,CAACpD,KAAK,CAACqD,IAAI,CAAC,CAAC;IACjB,IAAI,CAACV,OAAO,CAACW,EAAE,CAAChF,OAAO,CAAC2B,MAAM,CAACsD,aAAa,EAAGC,IAAI,IAAK;MACtD,IAAIA,IAAI,KAAKlF,OAAO,CAAC2B,MAAM,CAACwD,WAAW,EAAE;QACvC,IAAI,CAAChB,IAAI,CAACH,SAAS,CAACoB,MAAM,CAAC,UAAU,EAAE,IAAI,CAACT,MAAM,CAACU,OAAO,CAAC,CAAC,CAAC;MAC/D;IACF,CAAC,CAAC;IACF,IAAI,CAAChB,OAAO,CAACW,EAAE,CAAChF,OAAO,CAAC2B,MAAM,CAAC2D,aAAa,EAAE,CAACC,MAAM,EAAEC,SAAS,KAAK;MACnE,MAAMC,QAAQ,GAAG,IAAI,CAACb,SAAS,CAACc,SAAS;MACzC,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACf,SAAS,CAACgB,QAAQ,CAAC,CAAC;MAC5C,MAAMC,aAAa,GACjBJ,QAAQ,IAAIE,QAAQ,GAAG;QAAEF,QAAQ;QAAEE;MAAS,CAAC,GAAGnD,SAAS;MAC3DsD,MAAM,CAACC,IAAI,CACT,IAAI,EACJ,MAAM,IAAI,CAACpB,MAAM,CAACqB,MAAM,CAAC,IAAI,EAAER,SAAS,EAAEK,aAAa,CAAC,EACxDN,MACF,CAAC;IACH,CAAC,CAAC;IACF,IAAI,CAAClB,OAAO,CAACW,EAAE,CAAChF,OAAO,CAAC2B,MAAM,CAACsE,mBAAmB,EAAE,CAACC,IAAI,EAAEnE,KAAK,KAAK;MACnE,MAAM0D,QAAQ,GAAG,IAAI,CAACb,SAAS,CAACc,SAAS;MACzC,MAAM,CAACC,QAAQ,CAAC,GAAG,IAAI,CAACf,SAAS,CAACgB,QAAQ,CAAC,CAAC;MAC5C,MAAMC,aAAa,GACjBJ,QAAQ,IAAIE,QAAQ,GAAG;QAAEF,QAAQ;QAAEE;MAAS,CAAC,GAAGnD,SAAS;MAC3DsD,MAAM,CAACC,IAAI,CACT,IAAI,EACJ,MAAM;QACJ,MAAMI,MAAM,GAAG,IAAIrG,KAAK,CAAC,CAAC,CACvBsG,MAAM,CAACF,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC,CAAC,CACzBD,MAAM,CAAC;UAAE,CAACF,IAAI,CAACI,OAAO,CAACrD,QAAQ,GAAGlB;QAAM,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC4C,MAAM,CAACqB,MAAM,CAACG,MAAM,EAAE,EAAE,EAAEN,aAAa,CAAC;MACtD,CAAC,EACD9E,KAAK,CAACa,OAAO,CAAC2E,IAChB,CAAC;IACH,CAAC,CAAC;IACF,IAAI1C,IAAI,EAAE;MACR,MAAM2C,QAAQ,GAAG,IAAI,CAACrF,SAAS,CAACsF,OAAO,CAAC;QACtC5C,IAAI,EAAG,GAAEA,IAAK,aAAY;QAC1B6C,IAAI,EAAE;MACR,CAAC,CAAC;MACF,IAAI,CAACC,WAAW,CAACH,QAAQ,CAAC;IAC5B;IACA,IAAI,CAACnF,OAAO,CAACuF,KAAK,CAAC,CAAC;IACpB,IAAI,IAAI,CAACjD,OAAO,CAACpC,WAAW,EAAE;MAC5B,IAAI,CAAC4C,IAAI,CAAC0C,YAAY,CAAC,kBAAkB,EAAE,IAAI,CAAClD,OAAO,CAACpC,WAAW,CAAC;IACtE;IACA,IAAI,IAAI,CAACoC,OAAO,CAACnC,QAAQ,EAAE;MACzB,IAAI,CAACsF,OAAO,CAAC,CAAC;IAChB;IACA,IAAI,CAACC,kBAAkB,GAAG,KAAK;EACjC;EAIA3C,YAAYA,CACVV,SAA+B,EAED;IAAA,IAD9BsD,OAAoB,GAAA1E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IAE3B,IAAI,OAAOoB,SAAS,KAAK,QAAQ,EAAE;MACjC,MAAMuD,SAAS,GAAGvD,SAAS;MAC3BA,SAAS,GAAGwD,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACzCzD,SAAS,CAACM,SAAS,CAACC,GAAG,CAACgD,SAAS,CAAC;IACpC;IACA,IAAI,CAACvD,SAAS,CAAC0D,YAAY,CAAC1D,SAAS,EAAEsD,OAAO,CAAC;IAC/C,OAAOtD,SAAS;EAClB;EAEA2D,IAAIA,CAAA,EAAG;IACL,IAAI,CAACzC,SAAS,CAAC0C,QAAQ,CAAC,IAAI,CAAC;EAC/B;EAIAC,UAAUA,CACRC,KAAqB,EACrBjF,MAA+B,EAC/BgD,MAAsB,EACf;IACP;IACA,CAACiC,KAAK,EAAEjF,MAAM,GAAIgD,MAAM,CAAC,GAAGkC,QAAQ,CAACD,KAAK,EAAEjF,MAAM,EAAEgD,MAAM,CAAC;IAC3D,OAAOO,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,OAAO,IAAI,CAACpB,MAAM,CAAC4C,UAAU,CAACC,KAAK,EAAEjF,MAAM,CAAC;IAC9C,CAAC,EACDgD,MAAM,EACNiC,KAAK,EACL,CAAC,CAAC,GAAGjF,MACP,CAAC;EACH;EAEAuE,OAAOA,CAAA,EAAG;IACR,IAAI,CAACY,MAAM,CAAC,KAAK,CAAC;EACpB;EAEAC,YAAYA,CAAIC,QAAiB,EAAK;IACpC,IAAI,CAACb,kBAAkB,GAAG,IAAI;IAC9B,MAAMc,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACb,kBAAkB,GAAG,KAAK;IAC/B,OAAOc,KAAK;EACd;EAEAH,MAAMA,CAAA,EAAiB;IAAA,IAAhBI,OAAO,GAAAxF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;IACnB,IAAI,CAACoC,MAAM,CAACgD,MAAM,CAACI,OAAO,CAAC;IAC3B,IAAI,CAACpE,SAAS,CAACM,SAAS,CAACoB,MAAM,CAAC,aAAa,EAAE,CAAC0C,OAAO,CAAC;EAC1D;EAEAC,KAAKA,CAAA,EAA4C;IAAA,IAA3CpE,OAAoC,GAAArB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC7C,IAAI,CAACsC,SAAS,CAACmD,KAAK,CAAC,CAAC;IACtB,IAAI,CAACpE,OAAO,CAACqE,aAAa,EAAE;MAC1B,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChC;EACF;EAEAC,MAAMA,CACJvF,IAAY,EACZkF,KAAc,EAEP;IAAA,IADPtC,MAAqB,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtC,OAAO,CAAC4B,OAAO,CAACuG,GAAG;IAE3C,OAAOrC,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,MAAMqC,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,IAAI,CAAC;MACrC,IAAIlC,MAAM,GAAG,IAAIrG,KAAK,CAAC,CAAC;MACxB,IAAIsI,KAAK,IAAI,IAAI,EAAE,OAAOjC,MAAM;MAChC,IAAI,IAAI,CAACzB,MAAM,CAACF,KAAK,CAAC7B,IAAI,EAAE9C,SAAS,CAACyI,KAAK,CAACC,KAAK,CAAC,EAAE;QAClDpC,MAAM,GAAG,IAAI,CAACxB,MAAM,CAAC6D,UAAU,CAACJ,KAAK,CAACZ,KAAK,EAAEY,KAAK,CAAC7F,MAAM,EAAE;UACzD,CAACI,IAAI,GAAGkF;QACV,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIO,KAAK,CAAC7F,MAAM,KAAK,CAAC,EAAE;QAC7B,IAAI,CAACqC,SAAS,CAACsD,MAAM,CAACvF,IAAI,EAAEkF,KAAK,CAAC;QAClC,OAAO1B,MAAM;MACf,CAAC,MAAM;QACLA,MAAM,GAAG,IAAI,CAACxB,MAAM,CAAC8D,UAAU,CAACL,KAAK,CAACZ,KAAK,EAAEY,KAAK,CAAC7F,MAAM,EAAE;UACzD,CAACI,IAAI,GAAGkF;QACV,CAAC,CAAC;MACJ;MACA,IAAI,CAACa,YAAY,CAACN,KAAK,EAAEpI,OAAO,CAAC4B,OAAO,CAAC+G,MAAM,CAAC;MAChD,OAAOxC,MAAM;IACf,CAAC,EACDZ,MACF,CAAC;EACH;EAeAiD,UAAUA,CACRhB,KAAa,EACbjF,MAAc,EACdI,IAAsC,EACtCkF,KAA+B,EAC/BtC,MAAsB,EACf;IACP,IAAIqD,OAAgC;IACpC;IACA,CAACpB,KAAK,EAAEjF,MAAM,EAAEqG,OAAO,EAAErD,MAAM,CAAC,GAAGkC,QAAQ,CACzCD,KAAK,EACLjF,MAAM;IACN;IACAI,IAAI,EACJkF,KAAK,EACLtC,MACF,CAAC;IACD,OAAOO,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,OAAO,IAAI,CAACpB,MAAM,CAAC6D,UAAU,CAAChB,KAAK,EAAEjF,MAAM,EAAEqG,OAAO,CAAC;IACvD,CAAC,EACDrD,MAAM,EACNiC,KAAK,EACL,CACF,CAAC;EACH;EAqBAiB,UAAUA,CACRjB,KAAqB,EACrBjF,MAAuB,EACvBI,IAAsB,EACtBkF,KAA+B,EAC/BtC,MAAsB,EACf;IACP,IAAIqD,OAAgC;IACpC;IACA,CAACpB,KAAK,EAAEjF,MAAM,EAAEqG,OAAO,EAAErD,MAAM,CAAC,GAAGkC,QAAQ;IACzC;IACAD,KAAK,EACLjF,MAAM,EACNI,IAAI,EACJkF,KAAK,EACLtC,MACF,CAAC;IACD,OAAOO,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,OAAO,IAAI,CAACpB,MAAM,CAAC8D,UAAU,CAACjB,KAAK,EAAEjF,MAAM,EAAEqG,OAAO,CAAC;IACvD,CAAC,EACDrD,MAAM,EACNiC,KAAK,EACL,CACF,CAAC;EACH;EAEAqB,SAASA,CAACrB,KAAqB,EAA6B;IAAA,IAA3BjF,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IACzC,IAAIrB,MAAqB,GAAG,IAAI;IAChC,IAAI,OAAOuG,KAAK,KAAK,QAAQ,EAAE;MAC7BvG,MAAM,GAAG,IAAI,CAAC2D,SAAS,CAACiE,SAAS,CAACrB,KAAK,EAAEjF,MAAM,CAAC;IAClD,CAAC,MAAM;MACLtB,MAAM,GAAG,IAAI,CAAC2D,SAAS,CAACiE,SAAS,CAACrB,KAAK,CAACA,KAAK,EAAEA,KAAK,CAACjF,MAAM,CAAC;IAC9D;IACA,IAAI,CAACtB,MAAM,EAAE,OAAO,IAAI;IACxB,MAAM6H,eAAe,GAAG,IAAI,CAACpF,SAAS,CAACqF,qBAAqB,CAAC,CAAC;IAC9D,OAAO;MACLC,MAAM,EAAE/H,MAAM,CAAC+H,MAAM,GAAGF,eAAe,CAACG,GAAG;MAC3CC,MAAM,EAAEjI,MAAM,CAACiI,MAAM;MACrBC,IAAI,EAAElI,MAAM,CAACkI,IAAI,GAAGL,eAAe,CAACK,IAAI;MACxCC,KAAK,EAAEnI,MAAM,CAACmI,KAAK,GAAGN,eAAe,CAACK,IAAI;MAC1CF,GAAG,EAAEhI,MAAM,CAACgI,GAAG,GAAGH,eAAe,CAACG,GAAG;MACrCI,KAAK,EAAEpI,MAAM,CAACoI;IAChB,CAAC;EACH;EAEAC,WAAWA,CAAA,EAA+C;IAAA,IAA9C9B,KAAK,GAAAlF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,IAAEC,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAACiH,SAAS,CAAC,CAAC,GAAG/B,KAAK;IACtD,CAACA,KAAK,EAAEjF,MAAM,CAAC,GAAGkF,QAAQ,CAACD,KAAK,EAAEjF,MAAM,CAAC;IACzC,OAAO,IAAI,CAACoC,MAAM,CAAC2E,WAAW,CAAC9B,KAAK,EAAEjF,MAAM,CAAC;EAC/C;EAMAiH,SAASA,CAAA,EAGwB;IAAA,IAF/BhC,KAAqB,GAAAlF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI,CAAC+F,YAAY,CAAC,IAAI,CAAC;IAAA,IAC/C9F,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAEV,IAAI,OAAOkF,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI,CAAC7C,MAAM,CAAC6E,SAAS,CAAChC,KAAK,EAAEjF,MAAM,CAAC;IAC7C;IACA,OAAO,IAAI,CAACoC,MAAM,CAAC6E,SAAS,CAAChC,KAAK,CAACA,KAAK,EAAEA,KAAK,CAACjF,MAAM,CAAC;EACzD;EAEAkH,QAAQA,CAACvD,IAAoB,EAAE;IAC7B,OAAOA,IAAI,CAACG,MAAM,CAAC,IAAI,CAAC3B,MAAM,CAAC;EACjC;EAEA6E,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC7E,MAAM,CAACnC,MAAM,CAAC,CAAC;EAC7B;EAEAmH,OAAOA,CAAClC,KAAa,EAAE;IACrB,OAAO,IAAI,CAAC9C,MAAM,CAACiF,IAAI,CAACnC,KAAK,CAAC;EAChC;EAEAoC,OAAOA,CAACpC,KAAa,EAAE;IACrB,OAAO,IAAI,CAAC9C,MAAM,CAACmF,IAAI,CAACrC,KAAK,CAAC;EAChC;EAIAsC,QAAQA,CAAA,EAGkB;IAAA,IAFxBtC,KAAqB,GAAAlF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,IACzBC,MAAM,GAAAD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGyH,MAAM,CAACC,SAAS;IAEzB,IAAI,OAAOxC,KAAK,KAAK,QAAQ,EAAE;MAC7B,OAAO,IAAI,CAAC9C,MAAM,CAACuF,KAAK,CAACzC,KAAK,CAACA,KAAK,EAAEA,KAAK,CAACjF,MAAM,CAAC;IACrD;IACA,OAAO,IAAI,CAACmC,MAAM,CAACuF,KAAK,CAACzC,KAAK,EAAEjF,MAAM,CAAC;EACzC;EAEA2H,SAASA,CAACvH,IAAY,EAAE;IACtB,OAAO,IAAI,CAACjB,KAAK,CAACR,OAAO,CAACyB,IAAI,CAAC;EACjC;EAIA0F,YAAYA,CAAA,EAA8B;IAAA,IAA7BN,KAAK,GAAAzF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IACxB,IAAIyF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAC,CAAC;IACvB,IAAI,CAAC/B,MAAM,CAAC,CAAC,CAAC,CAAC;IACf,OAAO,IAAI,CAACpB,SAAS,CAACgB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;EACrC;EAIAuE,eAAeA,CAAA,EAA6C;IAAA,IAA5C3C,KAAqB,GAAAlF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,IAAEC,MAAe,GAAAD,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IACxD,IAAI,OAAOgF,KAAK,KAAK,QAAQ,EAAE;MAC7BjF,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgH,SAAS,CAAC,CAAC,GAAG/B,KAAK;IAC7C;IACA;IACA,CAACA,KAAK,EAAEjF,MAAM,CAAC,GAAGkF,QAAQ,CAACD,KAAK,EAAEjF,MAAM,CAAC;IACzC,OAAO,IAAI,CAACoC,MAAM,CAACyF,OAAO,CAAC5C,KAAK,EAAEjF,MAAM,CAAC;EAC3C;EAIA8H,OAAOA,CAAA,EAAqD;IAAA,IAApD7C,KAAqB,GAAAlF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;IAAA,IAAEC,MAAe,GAAAD,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IAChD,IAAI,OAAOgF,KAAK,KAAK,QAAQ,EAAE;MAC7BjF,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACgH,SAAS,CAAC,CAAC,GAAG/B,KAAK;IAC7C;IACA;IACA,CAACA,KAAK,EAAEjF,MAAM,CAAC,GAAGkF,QAAQ,CAACD,KAAK,EAAEjF,MAAM,CAAC;IACzC,OAAO,IAAI,CAACoC,MAAM,CAAC0F,OAAO,CAAC7C,KAAK,EAAEjF,MAAM,CAAC;EAC3C;EAEA+H,QAAQA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC1F,SAAS,CAAC0F,QAAQ,CAAC,CAAC;EAClC;EAEAC,WAAWA,CACT/C,KAAa,EACbgD,KAAa,EACb3C,KAAc,EAEP;IAAA,IADPtC,MAAqB,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGvB,KAAK,CAACa,OAAO,CAACuG,GAAG;IAEzC,OAAOrC,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,OAAO,IAAI,CAACpB,MAAM,CAAC4F,WAAW,CAAC/C,KAAK,EAAEgD,KAAK,EAAE3C,KAAK,CAAC;IACrD,CAAC,EACDtC,MAAM,EACNiC,KACF,CAAC;EACH;EAgBAiD,UAAUA,CACRjD,KAAa,EACbd,IAAY,EACZ/D,IAAuD,EACvDkF,KAAe,EACftC,MAAsB,EACf;IACP,IAAIqD,OAAgC;IACpC;IACA;IACA,CAACpB,KAAK,GAAIoB,OAAO,EAAErD,MAAM,CAAC,GAAGkC,QAAQ,CAACD,KAAK,EAAE,CAAC,EAAE7E,IAAI,EAAEkF,KAAK,EAAEtC,MAAM,CAAC;IACpE,OAAOO,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,OAAO,IAAI,CAACpB,MAAM,CAAC8F,UAAU,CAACjD,KAAK,EAAEd,IAAI,EAAEkC,OAAO,CAAC;IACrD,CAAC,EACDrD,MAAM,EACNiC,KAAK,EACLd,IAAI,CAACnE,MACP,CAAC;EACH;EAEAmI,SAASA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChG,MAAM,CAACgG,SAAS,CAAC,CAAC;EAChC;EAEAC,GAAGA,CAAA,EAA4D;IAC7D,OAAO,IAAI,CAACtG,OAAO,CAACsG,GAAG,CAAC,GAAArI,SAAO,CAAC;EAClC;EA6BA0C,EAAEA,CAAA,EAAoE;IACpE,OAAO,IAAI,CAACX,OAAO,CAACW,EAAE,CAAC,GAAA1C,SAAO,CAAC;EACjC;EAEAsI,IAAIA,CAAA,EAA6D;IAC/D,OAAO,IAAI,CAACvG,OAAO,CAACuG,IAAI,CAAC,GAAAtI,SAAO,CAAC;EACnC;EAEAuI,YAAYA,CAACrD,KAAa,EAAEjF,MAAc,EAAEgD,MAAsB,EAAS;IACzE,CAACiC,KAAK,EAAEjF,MAAM,GAAIgD,MAAM,CAAC,GAAGkC,QAAQ,CAACD,KAAK,EAAEjF,MAAM,EAAEgD,MAAM,CAAC;IAC3D,OAAOO,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJ,OAAO,IAAI,CAACpB,MAAM,CAACkG,YAAY,CAACrD,KAAK,EAAEjF,MAAM,CAAC;IAChD,CAAC,EACDgD,MAAM,EACNiC,KACF,CAAC;EACH;EAEAhH,kBAAkBA,CAACsK,IAAU,EAAE;IAC7BtK,kBAAkB,CAAC,IAAI,CAAC2D,IAAI,EAAE2G,IAAI,CAAC;EACrC;;EAEA;AACF;AACA;EACEC,cAAcA,CAAA,EAAG;IACfC,OAAO,CAACzH,IAAI,CACV,wIACF,CAAC;IACD,IAAI,CAAC0E,uBAAuB,CAAC,CAAC;EAChC;;EAEA;AACF;AACA;AACA;EACEA,uBAAuBA,CAAA,EAAG;IACxB,MAAMG,KAAK,GAAG,IAAI,CAACxD,SAAS,CAACc,SAAS;IACtC,MAAMzE,MAAM,GAAGmH,KAAK,IAAI,IAAI,CAACxD,SAAS,CAACiE,SAAS,CAACT,KAAK,CAACZ,KAAK,EAAEY,KAAK,CAAC7F,MAAM,CAAC;IAC3E,IAAItB,MAAM,EAAE;MACV,IAAI,CAACT,kBAAkB,CAACS,MAAM,CAAC;IACjC;EACF;EAEA0F,WAAWA,CACT5E,KAAmB,EAEZ;IAAA,IADPwD,MAAqB,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtC,OAAO,CAAC4B,OAAO,CAACuG,GAAG;IAE3C,OAAOrC,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJhE,KAAK,GAAG,IAAIjC,KAAK,CAACiC,KAAK,CAAC;MACxB,MAAMQ,MAAM,GAAG,IAAI,CAACgH,SAAS,CAAC,CAAC;MAC/B;MACA,MAAM0B,OAAO,GAAG,IAAI,CAACtG,MAAM,CAAC4C,UAAU,CAAC,CAAC,EAAEhF,MAAM,CAAC;MACjD,MAAM2I,OAAO,GAAG,IAAI,CAACvG,MAAM,CAACwG,cAAc,CAAC,CAAC,EAAEpJ,KAAK,CAAC;MACpD;MACA,MAAMqJ,OAAO,GAAG,IAAI,CAACzG,MAAM,CAAC4C,UAAU,CAAC,IAAI,CAACgC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;MAC/D,OAAO0B,OAAO,CAACI,OAAO,CAACH,OAAO,CAAC,CAACG,OAAO,CAACD,OAAO,CAAC;IAClD,CAAC,EACD7F,MACF,CAAC;EACH;EAKAmD,YAAYA,CACVlB,KAA4B,EAC5BjF,MAA+B,EAC/BgD,MAAsB,EAChB;IACN,IAAIiC,KAAK,IAAI,IAAI,EAAE;MACjB;MACA,IAAI,CAAC5C,SAAS,CAAC0C,QAAQ,CAAC,IAAI,EAAE/E,MAAM,IAAIxB,KAAK,CAACa,OAAO,CAACuG,GAAG,CAAC;IAC5D,CAAC,MAAM;MACL;MACA,CAACX,KAAK,EAAEjF,MAAM,GAAIgD,MAAM,CAAC,GAAGkC,QAAQ,CAACD,KAAK,EAAEjF,MAAM,EAAEgD,MAAM,CAAC;MAC3D,IAAI,CAACX,SAAS,CAAC0C,QAAQ,CAAC,IAAIjH,KAAK,CAACiL,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/D,KAAK,CAAC,EAAEjF,MAAM,CAAC,EAAEgD,MAAM,CAAC;MACtE,IAAIA,MAAM,KAAKvF,OAAO,CAAC4B,OAAO,CAAC+G,MAAM,EAAE;QACrC,IAAI,CAACV,uBAAuB,CAAC,CAAC;MAChC;IACF;EACF;EAEAuD,OAAOA,CAAC9E,IAAY,EAA+C;IAAA,IAA7CnB,MAAqB,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtC,OAAO,CAAC4B,OAAO,CAACuG,GAAG;IAC/D,MAAMpG,KAAK,GAAG,IAAIjC,KAAK,CAAC,CAAC,CAAC2L,MAAM,CAAC/E,IAAI,CAAC;IACtC,OAAO,IAAI,CAACC,WAAW,CAAC5E,KAAK,EAAEwD,MAAM,CAAC;EACxC;EAEAS,MAAMA,CAAA,EAA+C;IAAA,IAA9CT,MAAqB,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtC,OAAO,CAAC4B,OAAO,CAAC2E,IAAI;IACjD,MAAMJ,MAAM,GAAG,IAAI,CAACzB,MAAM,CAACsB,MAAM,CAACT,MAAM,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACX,SAAS,CAACoB,MAAM,CAACT,MAAM,CAAC;IAC7B;IACA,OAAOY,MAAM;EACf;EAEAuF,cAAcA,CACZ3J,KAAmB,EAEZ;IAAA,IADPwD,MAAqB,GAAAjD,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGtC,OAAO,CAAC4B,OAAO,CAACuG,GAAG;IAE3C,OAAOrC,MAAM,CAACC,IAAI,CAChB,IAAI,EACJ,MAAM;MACJhE,KAAK,GAAG,IAAIjC,KAAK,CAACiC,KAAK,CAAC;MACxB,OAAO,IAAI,CAAC4C,MAAM,CAACgH,UAAU,CAAC5J,KAAK,CAAC;IACtC,CAAC,EACDwD,MAAM,EACN,IACF,CAAC;EACH;AACF;AAEA,SAASqG,eAAeA,CAACC,QAAiD,EAAE;EAC1E,OAAO,OAAOA,QAAQ,KAAK,QAAQ,GAC/B3E,QAAQ,CAAC4E,aAAa,CAAcD,QAAQ,CAAC,GAC7CA,QAAQ;AACd;AAEA,SAASE,kBAAkBA,CAACC,MAA2C,EAAE;EACvE,OAAO9I,MAAM,CAAC+I,OAAO,CAACD,MAAM,IAAI,CAAC,CAAC,CAAC,CAACE,MAAM,CACxC,CAACC,QAAQ,EAAAC,IAAA;IAAA,IAAE,CAAC/I,GAAG,EAAEwE,KAAK,CAAC,GAAAuE,IAAA;IAAA,OAAM;MAC3B,GAAGD,QAAQ;MACX,CAAC9I,GAAG,GAAGwE,KAAK,KAAK,IAAI,GAAG,CAAC,CAAC,GAAGA;IAC/B,CAAC;EAAA,CAAC,EACF,CAAC,CACH,CAAC;AACH;AAEA,SAASwE,8BAA8BA,CAACC,GAAiB,EAAE;EACzD,OAAOpJ,MAAM,CAACqJ,WAAW,CACvBrJ,MAAM,CAAC+I,OAAO,CAACK,GAAG,CAAC,CAACE,MAAM,CAAEC,KAAK,IAAKA,KAAK,CAAC,CAAC,CAAC,KAAKjK,SAAS,CAC9D,CAAC;AACH;AAEA,SAASoB,YAAYA,CACnB8I,mBAAyC,EACzC/I,OAAqB,EACC;EACtB,MAAMD,SAAS,GAAGkI,eAAe,CAACc,mBAAmB,CAAC;EACtD,IAAI,CAAChJ,SAAS,EAAE;IACd,MAAM,IAAIe,KAAK,CAAC,yBAAyB,CAAC;EAC5C;EAEA,MAAMkI,qBAAqB,GACzB,CAAChJ,OAAO,CAACjC,KAAK,IAAIiC,OAAO,CAACjC,KAAK,KAAKX,KAAK,CAACC,QAAQ,CAACU,KAAK;EAC1D,MAAMA,KAAK,GAAGiL,qBAAqB,GAC/BpM,KAAK,GACLQ,KAAK,CAAC2B,MAAM,CAAE,UAASiB,OAAO,CAACjC,KAAM,EAAC,CAAC;EAC3C,IAAI,CAACA,KAAK,EAAE;IACV,MAAM,IAAI+C,KAAK,CAAE,iBAAgBd,OAAO,CAACjC,KAAM,wBAAuB,CAAC;EACzE;EAEA,MAAM;IAAER,OAAO,EAAE0L,mBAAmB;IAAE,GAAGC;EAAc,CAAC,GAAG9L,KAAK,CAACC,QAAQ;EACzE,MAAM;IAAEE,OAAO,EAAE4L,mBAAmB;IAAE,GAAGC;EAAc,CAAC,GAAGrL,KAAK,CAACV,QAAQ;EAEzE,IAAIgM,iBAAiB,GAAGjB,kBAAkB,CAACpI,OAAO,CAACzC,OAAO,CAAC;EAC3D;EACA,IACE8L,iBAAiB,IAAI,IAAI,IACzBA,iBAAiB,CAACC,OAAO,IACzBD,iBAAiB,CAACC,OAAO,CAACxJ,WAAW,KAAKP,MAAM,EAChD;IACA8J,iBAAiB,GAAG;MAClB,GAAGA,iBAAiB;MACpBC,OAAO,EAAE;QAAEvJ,SAAS,EAAEsJ,iBAAiB,CAACC;MAAQ;IAClD,CAAC;EACH;EAEA,MAAM/L,OAAwC,GAAGtB,KAAK,CACpD,CAAC,CAAC,EACFmM,kBAAkB,CAACa,mBAAmB,CAAC,EACvCb,kBAAkB,CAACe,mBAAmB,CAAC,EACvCE,iBACF,CAAC;EAED,MAAMhB,MAAM,GAAG;IACb,GAAGa,aAAa;IAChB,GAAGR,8BAA8B,CAACU,aAAa,CAAC;IAChD,GAAGV,8BAA8B,CAAC1I,OAAO;EAC3C,CAAC;EAED,IAAIlC,QAAQ,GAAGkC,OAAO,CAAClC,QAAQ;EAC/B,IAAIA,QAAQ,EAAE;IACZ,IAAIkC,OAAO,CAACiF,OAAO,EAAE;MACnBlI,KAAK,CAAC6C,IAAI,CAAC,2DAA2D,CAAC;IACzE;EACF,CAAC,MAAM;IACL9B,QAAQ,GAAGkC,OAAO,CAACiF,OAAO,GACtBnI,yBAAyB,CAACkD,OAAO,CAACiF,OAAO,EAAEoD,MAAM,CAACvK,QAAQ,EAAEf,KAAK,CAAC,GAClEsL,MAAM,CAACvK,QAAQ;EACrB;EAEA,OAAO;IACL,GAAGuK,MAAM;IACTvK,QAAQ;IACRiC,SAAS;IACThC,KAAK;IACLR,OAAO,EAAEgC,MAAM,CAAC+I,OAAO,CAAC/K,OAAO,CAAC,CAACgL,MAAM,CACrC,CAACgB,mBAAmB,EAAAC,KAAA,KAAoB;MAAA,IAAlB,CAACxK,IAAI,EAAEkF,KAAK,CAAC,GAAAsF,KAAA;MACjC,IAAI,CAACtF,KAAK,EAAE,OAAOqF,mBAAmB;MAEtC,MAAME,WAAW,GAAGrM,KAAK,CAAC2B,MAAM,CAAE,WAAUC,IAAK,EAAC,CAAC;MACnD,IAAIyK,WAAW,IAAI,IAAI,EAAE;QACvB1M,KAAK,CAACkC,KAAK,CACR,eAAcD,IAAK,0CACtB,CAAC;QACD,OAAOuK,mBAAmB;MAC5B;MACA,OAAO;QACL,GAAGA,mBAAmB;QACtB;QACA,CAACvK,IAAI,GAAG/C,KAAK,CAAC,CAAC,CAAC,EAAEwN,WAAW,CAACpM,QAAQ,IAAI,CAAC,CAAC,EAAE6G,KAAK;MACrD,CAAC;IACH,CAAC,EACD,CAAC,CACH,CAAC;IACD5G,MAAM,EAAE2K,eAAe,CAACI,MAAM,CAAC/K,MAAM;EACvC,CAAC;AACH;;AAEA;AACA;AACA,SAAS6E,MAAMA,CACb8B,QAAqB,EACrBrC,MAAqB,EACrBiC,KAAuB,EACvB6F,KAAoB,EACpB;EACA,IACE,CAAC,IAAI,CAAC3C,SAAS,CAAC,CAAC,IACjBnF,MAAM,KAAKvF,OAAO,CAAC4B,OAAO,CAAC2E,IAAI,IAC/B,CAAC,IAAI,CAACQ,kBAAkB,EACxB;IACA,OAAO,IAAIjH,KAAK,CAAC,CAAC;EACpB;EACA,IAAIsI,KAAK,GAAGZ,KAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACa,YAAY,CAAC,CAAC;EACtD,MAAMiF,QAAQ,GAAG,IAAI,CAAC3I,MAAM,CAAC5C,KAAK;EAClC,MAAMoE,MAAM,GAAGyB,QAAQ,CAAC,CAAC;EACzB,IAAIQ,KAAK,IAAI,IAAI,EAAE;IACjB,IAAIZ,KAAK,KAAK,IAAI,EAAE;MAClBA,KAAK,GAAGY,KAAK,CAACZ,KAAK,CAAC,CAAC;IACvB;IACA,IAAI6F,KAAK,IAAI,IAAI,EAAE;MACjBjF,KAAK,GAAGmF,UAAU,CAACnF,KAAK,EAAEjC,MAAM,EAAEZ,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAI8H,KAAK,KAAK,CAAC,EAAE;MACtB;MACAjF,KAAK,GAAGmF,UAAU,CAACnF,KAAK,EAAEZ,KAAK,EAAE6F,KAAK,EAAE9H,MAAM,CAAC;IACjD;IACA,IAAI,CAACmD,YAAY,CAACN,KAAK,EAAEpI,OAAO,CAAC4B,OAAO,CAAC+G,MAAM,CAAC;EAClD;EACA,IAAIxC,MAAM,CAAC5D,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;IACvB,MAAMiL,IAAI,GAAG,CAACxN,OAAO,CAAC2B,MAAM,CAACwD,WAAW,EAAEgB,MAAM,EAAEmH,QAAQ,EAAE/H,MAAM,CAAC;IACnE,IAAI,CAAClB,OAAO,CAACoJ,IAAI,CAACzN,OAAO,CAAC2B,MAAM,CAACsD,aAAa,EAAE,GAAGuI,IAAI,CAAC;IACxD,IAAIjI,MAAM,KAAKvF,OAAO,CAAC4B,OAAO,CAAC+G,MAAM,EAAE;MACrC,IAAI,CAACtE,OAAO,CAACoJ,IAAI,CAAC,GAAGD,IAAI,CAAC;IAC5B;EACF;EACA,OAAOrH,MAAM;AACf;AAuCA,SAASsB,QAAQA,CACfD,KAAqB,EACrBjF,MAAkE,EAClEI,IAAiE,EACjEkF,KAA+B,EAC/BtC,MAAsB,EACC;EACvB,IAAIqD,OAAgC,GAAG,CAAC,CAAC;EACzC;EACA,IAAI,OAAOpB,KAAK,CAACA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACjF,MAAM,KAAK,QAAQ,EAAE;IACvE;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B;MACAgD,MAAM,GAAGsC,KAAK;MACdA,KAAK,GAAGlF,IAAI;MACZA,IAAI,GAAGJ,MAAM;MACb;MACAA,MAAM,GAAGiF,KAAK,CAACjF,MAAM,CAAC,CAAC;MACvB;MACAiF,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC;IACvB,CAAC,MAAM;MACL;MACAjF,MAAM,GAAGiF,KAAK,CAACjF,MAAM,CAAC,CAAC;MACvB;MACAiF,KAAK,GAAGA,KAAK,CAACA,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,MAAM,IAAI,OAAOjF,MAAM,KAAK,QAAQ,EAAE;IACrC;IACAgD,MAAM,GAAGsC,KAAK;IACdA,KAAK,GAAGlF,IAAI;IACZA,IAAI,GAAGJ,MAAM;IACbA,MAAM,GAAG,CAAC;EACZ;EACA;EACA,IAAI,OAAOI,IAAI,KAAK,QAAQ,EAAE;IAC5B;IACAiG,OAAO,GAAGjG,IAAI;IACd;IACA4C,MAAM,GAAGsC,KAAK;EAChB,CAAC,MAAM,IAAI,OAAOlF,IAAI,KAAK,QAAQ,EAAE;IACnC,IAAIkF,KAAK,IAAI,IAAI,EAAE;MACjBe,OAAO,CAACjG,IAAI,CAAC,GAAGkF,KAAK;IACvB,CAAC,MAAM;MACL;MACAtC,MAAM,GAAG5C,IAAI;IACf;EACF;EACA;EACA4C,MAAM,GAAGA,MAAM,IAAIvF,OAAO,CAAC4B,OAAO,CAACuG,GAAG;EACtC;EACA,OAAO,CAACX,KAAK,EAAEjF,MAAM,EAAEqG,OAAO,EAAErD,MAAM,CAAC;AACzC;AASA,SAASgI,UAAUA,CACjBnF,KAAY,EACZZ,KAAqB,EACrBkG,cAAuC,EACvCnI,MAAsB,EACtB;EACA,MAAMhD,MAAM,GAAG,OAAOmL,cAAc,KAAK,QAAQ,GAAGA,cAAc,GAAG,CAAC;EACtE,IAAItF,KAAK,IAAI,IAAI,EAAE,OAAO,IAAI;EAC9B,IAAIuF,KAAK;EACT,IAAIC,GAAG;EACP;EACA,IAAIpG,KAAK,IAAI,OAAOA,KAAK,CAACqG,iBAAiB,KAAK,UAAU,EAAE;IAC1D,CAACF,KAAK,EAAEC,GAAG,CAAC,GAAG,CAACxF,KAAK,CAACZ,KAAK,EAAEY,KAAK,CAACZ,KAAK,GAAGY,KAAK,CAAC7F,MAAM,CAAC,CAACuL,GAAG,CAAEC,GAAG;IAC/D;IACAvG,KAAK,CAACqG,iBAAiB,CAACE,GAAG,EAAExI,MAAM,KAAKvF,OAAO,CAAC4B,OAAO,CAAC2E,IAAI,CAC9D,CAAC;EACH,CAAC,MAAM;IACL,CAACoH,KAAK,EAAEC,GAAG,CAAC,GAAG,CAACxF,KAAK,CAACZ,KAAK,EAAEY,KAAK,CAACZ,KAAK,GAAGY,KAAK,CAAC7F,MAAM,CAAC,CAACuL,GAAG,CAAEC,GAAG,IAAK;MACpE;MACA,IAAIA,GAAG,GAAGvG,KAAK,IAAKuG,GAAG,KAAKvG,KAAK,IAAIjC,MAAM,KAAKvF,OAAO,CAAC4B,OAAO,CAAC2E,IAAK,EACnE,OAAOwH,GAAG;MACZ,IAAIxL,MAAM,IAAI,CAAC,EAAE;QACf,OAAOwL,GAAG,GAAGxL,MAAM;MACrB;MACA;MACA,OAAO+I,IAAI,CAACC,GAAG,CAAC/D,KAAK,EAAEuG,GAAG,GAAGxL,MAAM,CAAC;IACtC,CAAC,CAAC;EACJ;EACA,OAAO,IAAIlC,KAAK,CAACsN,KAAK,EAAEC,GAAG,GAAGD,KAAK,CAAC;AACtC;AAGA,SAAS9N,SAAS,EAAEQ,KAAK;AAEzB,SAASM,cAAc,EAAEiD,YAAY,EAAE6D,QAAQ,EAAE1G,KAAK,IAAIiN,OAAO", "ignoreList": []}