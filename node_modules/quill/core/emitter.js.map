{"version": 3, "file": "emitter.js", "names": ["EventEmitter", "instances", "logger", "debug", "EVENTS", "for<PERSON>ach", "eventName", "document", "addEventListener", "_len", "arguments", "length", "args", "Array", "_key", "from", "querySelectorAll", "node", "quill", "get", "emitter", "handleDOM", "Emitter", "events", "EDITOR_CHANGE", "SCROLL_BEFORE_UPDATE", "SCROLL_BLOT_MOUNT", "SCROLL_BLOT_UNMOUNT", "SCROLL_OPTIMIZE", "SCROLL_UPDATE", "SCROLL_EMBED_UPDATE", "SELECTION_CHANGE", "TEXT_CHANGE", "COMPOSITION_BEFORE_START", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "COMPOSITION_END", "sources", "API", "SILENT", "USER", "constructor", "domListeners", "on", "error", "emit", "_len2", "_key2", "log", "call", "event", "_len3", "_key3", "type", "_ref", "handler", "target", "contains", "listenDOM", "push"], "sources": ["../../src/core/emitter.ts"], "sourcesContent": ["import { EventEmitter } from 'eventemitter3';\nimport instances from './instances.js';\nimport logger from './logger.js';\n\nconst debug = logger('quill:events');\nconst EVENTS = ['selectionchange', 'mousedown', 'mouseup', 'click'];\n\nEVENTS.forEach((eventName) => {\n  document.addEventListener(eventName, (...args) => {\n    Array.from(document.querySelectorAll('.ql-container')).forEach((node) => {\n      const quill = instances.get(node);\n      if (quill && quill.emitter) {\n        quill.emitter.handleDOM(...args);\n      }\n    });\n  });\n});\n\nclass Emitter extends EventEmitter<string> {\n  static events = {\n    EDITOR_CHANGE: 'editor-change',\n    SCROLL_BEFORE_UPDATE: 'scroll-before-update',\n    SCROLL_BLOT_MOUNT: 'scroll-blot-mount',\n    SCROLL_BLOT_UNMOUNT: 'scroll-blot-unmount',\n    SCROLL_OPTIMIZE: 'scroll-optimize',\n    SCROLL_UPDATE: 'scroll-update',\n    SCROLL_EMBED_UPDATE: 'scroll-embed-update',\n    SELECTION_CHANGE: 'selection-change',\n    TEXT_CHANGE: 'text-change',\n    COMPOSITION_BEFORE_START: 'composition-before-start',\n    COMPOSITION_START: 'composition-start',\n    COMPOSITION_BEFORE_END: 'composition-before-end',\n    COMPOSITION_END: 'composition-end',\n  } as const;\n\n  static sources = {\n    API: 'api',\n    SILENT: 'silent',\n    USER: 'user',\n  } as const;\n\n  protected domListeners: Record<string, { node: Node; handler: Function }[]>;\n\n  constructor() {\n    super();\n    this.domListeners = {};\n    this.on('error', debug.error);\n  }\n\n  emit(...args: unknown[]): boolean {\n    debug.log.call(debug, ...args);\n    // @ts-expect-error\n    return super.emit(...args);\n  }\n\n  handleDOM(event: Event, ...args: unknown[]) {\n    (this.domListeners[event.type] || []).forEach(({ node, handler }) => {\n      if (event.target === node || node.contains(event.target as Node)) {\n        handler(event, ...args);\n      }\n    });\n  }\n\n  listenDOM(eventName: string, node: Node, handler: EventListener) {\n    if (!this.domListeners[eventName]) {\n      this.domListeners[eventName] = [];\n    }\n    this.domListeners[eventName].push({ node, handler });\n  }\n}\n\nexport type EmitterSource =\n  (typeof Emitter.sources)[keyof typeof Emitter.sources];\n\nexport default Emitter;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,eAAe;AAC5C,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,MAAM,MAAM,aAAa;AAEhC,MAAMC,KAAK,GAAGD,MAAM,CAAC,cAAc,CAAC;AACpC,MAAME,MAAM,GAAG,CAAC,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;AAEnEA,MAAM,CAACC,OAAO,CAAEC,SAAS,IAAK;EAC5BC,QAAQ,CAACC,gBAAgB,CAACF,SAAS,EAAE,YAAa;IAAA,SAAAG,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAATC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;MAAJF,IAAI,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;IAAA;IAC3CD,KAAK,CAACE,IAAI,CAACR,QAAQ,CAACS,gBAAgB,CAAC,eAAe,CAAC,CAAC,CAACX,OAAO,CAAEY,IAAI,IAAK;MACvE,MAAMC,KAAK,GAAGjB,SAAS,CAACkB,GAAG,CAACF,IAAI,CAAC;MACjC,IAAIC,KAAK,IAAIA,KAAK,CAACE,OAAO,EAAE;QAC1BF,KAAK,CAACE,OAAO,CAACC,SAAS,CAAC,GAAGT,IAAI,CAAC;MAClC;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAMU,OAAO,SAAStB,YAAY,CAAS;EACzC,OAAOuB,MAAM,GAAG;IACdC,aAAa,EAAE,eAAe;IAC9BC,oBAAoB,EAAE,sBAAsB;IAC5CC,iBAAiB,EAAE,mBAAmB;IACtCC,mBAAmB,EAAE,qBAAqB;IAC1CC,eAAe,EAAE,iBAAiB;IAClCC,aAAa,EAAE,eAAe;IAC9BC,mBAAmB,EAAE,qBAAqB;IAC1CC,gBAAgB,EAAE,kBAAkB;IACpCC,WAAW,EAAE,aAAa;IAC1BC,wBAAwB,EAAE,0BAA0B;IACpDC,iBAAiB,EAAE,mBAAmB;IACtCC,sBAAsB,EAAE,wBAAwB;IAChDC,eAAe,EAAE;EACnB,CAAC;EAED,OAAOC,OAAO,GAAG;IACfC,GAAG,EAAE,KAAK;IACVC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE;EACR,CAAC;EAIDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,EAAE,CAAC,OAAO,EAAExC,KAAK,CAACyC,KAAK,CAAC;EAC/B;EAEAC,IAAIA,CAAA,EAA8B;IAAA,SAAAC,KAAA,GAAApC,SAAA,CAAAC,MAAA,EAA1BC,IAAI,OAAAC,KAAA,CAAAiC,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJnC,IAAI,CAAAmC,KAAA,IAAArC,SAAA,CAAAqC,KAAA;IAAA;IACV5C,KAAK,CAAC6C,GAAG,CAACC,IAAI,CAAC9C,KAAK,EAAE,GAAGS,IAAI,CAAC;IAC9B;IACA,OAAO,KAAK,CAACiC,IAAI,CAAC,GAAGjC,IAAI,CAAC;EAC5B;EAEAS,SAASA,CAAC6B,KAAY,EAAsB;IAAA,SAAAC,KAAA,GAAAzC,SAAA,CAAAC,MAAA,EAAjBC,IAAI,OAAAC,KAAA,CAAAsC,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;MAAJxC,IAAI,CAAAwC,KAAA,QAAA1C,SAAA,CAAA0C,KAAA;IAAA;IAC7B,CAAC,IAAI,CAACV,YAAY,CAACQ,KAAK,CAACG,IAAI,CAAC,IAAI,EAAE,EAAEhD,OAAO,CAACiD,IAAA,IAAuB;MAAA,IAAtB;QAAErC,IAAI;QAAEsC;MAAQ,CAAC,GAAAD,IAAA;MAC9D,IAAIJ,KAAK,CAACM,MAAM,KAAKvC,IAAI,IAAIA,IAAI,CAACwC,QAAQ,CAACP,KAAK,CAACM,MAAc,CAAC,EAAE;QAChED,OAAO,CAACL,KAAK,EAAE,GAAGtC,IAAI,CAAC;MACzB;IACF,CAAC,CAAC;EACJ;EAEA8C,SAASA,CAACpD,SAAiB,EAAEW,IAAU,EAAEsC,OAAsB,EAAE;IAC/D,IAAI,CAAC,IAAI,CAACb,YAAY,CAACpC,SAAS,CAAC,EAAE;MACjC,IAAI,CAACoC,YAAY,CAACpC,SAAS,CAAC,GAAG,EAAE;IACnC;IACA,IAAI,CAACoC,YAAY,CAACpC,SAAS,CAAC,CAACqD,IAAI,CAAC;MAAE1C,IAAI;MAAEsC;IAAQ,CAAC,CAAC;EACtD;AACF;AAKA,eAAejC,OAAO", "ignoreList": []}