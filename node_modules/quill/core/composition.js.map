{"version": 3, "file": "composition.js", "names": ["Embed", "Emitter", "Composition", "isComposing", "constructor", "scroll", "emitter", "setupListeners", "domNode", "addEventListener", "event", "handleCompositionStart", "queueMicrotask", "handleCompositionEnd", "blot", "target", "Node", "find", "emit", "events", "COMPOSITION_BEFORE_START", "batchStart", "COMPOSITION_START", "COMPOSITION_BEFORE_END", "batchEnd", "COMPOSITION_END"], "sources": ["../../src/core/composition.ts"], "sourcesContent": ["import Embed from '../blots/embed.js';\nimport type Scroll from '../blots/scroll.js';\nimport Emitter from './emitter.js';\n\nclass Composition {\n  isComposing = false;\n\n  constructor(\n    private scroll: Scroll,\n    private emitter: Emitter,\n  ) {\n    this.setupListeners();\n  }\n\n  private setupListeners() {\n    this.scroll.domNode.addEventListener('compositionstart', (event) => {\n      if (!this.isComposing) {\n        this.handleCompositionStart(event);\n      }\n    });\n\n    this.scroll.domNode.addEventListener('compositionend', (event) => {\n      if (this.isComposing) {\n        // Webkit makes DOM changes after compositionend, so we use microtask to\n        // ensure the order.\n        // https://bugs.webkit.org/show_bug.cgi?id=31902\n        queueMicrotask(() => {\n          this.handleCompositionEnd(event);\n        });\n      }\n    });\n  }\n\n  private handleCompositionStart(event: CompositionEvent) {\n    const blot =\n      event.target instanceof Node\n        ? this.scroll.find(event.target, true)\n        : null;\n\n    if (blot && !(blot instanceof Embed)) {\n      this.emitter.emit(Emitter.events.COMPOSITION_BEFORE_START, event);\n      this.scroll.batchStart();\n      this.emitter.emit(Emitter.events.COMPOSITION_START, event);\n      this.isComposing = true;\n    }\n  }\n\n  private handleCompositionEnd(event: CompositionEvent) {\n    this.emitter.emit(Emitter.events.COMPOSITION_BEFORE_END, event);\n    this.scroll.batchEnd();\n    this.emitter.emit(Emitter.events.COMPOSITION_END, event);\n    this.isComposing = false;\n  }\n}\n\nexport default Composition;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,mBAAmB;AAErC,OAAOC,OAAO,MAAM,cAAc;AAElC,MAAMC,WAAW,CAAC;EAChBC,WAAW,GAAG,KAAK;EAEnBC,WAAWA,CACDC,MAAc,EACdC,OAAgB,EACxB;IAAA,KAFQD,MAAc,GAAdA,MAAc;IAAA,KACdC,OAAgB,GAAhBA,OAAgB;IAExB,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEQA,cAAcA,CAAA,EAAG;IACvB,IAAI,CAACF,MAAM,CAACG,OAAO,CAACC,gBAAgB,CAAC,kBAAkB,EAAGC,KAAK,IAAK;MAClE,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;QACrB,IAAI,CAACQ,sBAAsB,CAACD,KAAK,CAAC;MACpC;IACF,CAAC,CAAC;IAEF,IAAI,CAACL,MAAM,CAACG,OAAO,CAACC,gBAAgB,CAAC,gBAAgB,EAAGC,KAAK,IAAK;MAChE,IAAI,IAAI,CAACP,WAAW,EAAE;QACpB;QACA;QACA;QACAS,cAAc,CAAC,MAAM;UACnB,IAAI,CAACC,oBAAoB,CAACH,KAAK,CAAC;QAClC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEQC,sBAAsBA,CAACD,KAAuB,EAAE;IACtD,MAAMI,IAAI,GACRJ,KAAK,CAACK,MAAM,YAAYC,IAAI,GACxB,IAAI,CAACX,MAAM,CAACY,IAAI,CAACP,KAAK,CAACK,MAAM,EAAE,IAAI,CAAC,GACpC,IAAI;IAEV,IAAID,IAAI,IAAI,EAAEA,IAAI,YAAYd,KAAK,CAAC,EAAE;MACpC,IAAI,CAACM,OAAO,CAACY,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAACC,wBAAwB,EAAEV,KAAK,CAAC;MACjE,IAAI,CAACL,MAAM,CAACgB,UAAU,CAAC,CAAC;MACxB,IAAI,CAACf,OAAO,CAACY,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAACG,iBAAiB,EAAEZ,KAAK,CAAC;MAC1D,IAAI,CAACP,WAAW,GAAG,IAAI;IACzB;EACF;EAEQU,oBAAoBA,CAACH,KAAuB,EAAE;IACpD,IAAI,CAACJ,OAAO,CAACY,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAACI,sBAAsB,EAAEb,KAAK,CAAC;IAC/D,IAAI,CAACL,MAAM,CAACmB,QAAQ,CAAC,CAAC;IACtB,IAAI,CAAClB,OAAO,CAACY,IAAI,CAACjB,OAAO,CAACkB,MAAM,CAACM,eAAe,EAAEf,KAAK,CAAC;IACxD,IAAI,CAACP,WAAW,GAAG,KAAK;EAC1B;AACF;AAEA,eAAeD,WAAW", "ignoreList": []}