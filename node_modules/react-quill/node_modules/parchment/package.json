{"name": "parchment", "version": "1.1.4", "description": "A document model for rich text editors", "author": "<PERSON> <<EMAIL>>", "homepage": "http://quilljs.com/docs/parchment", "main": "dist/parchment.js", "files": ["tsconfig.json", "dist", "src"], "types": "dist/src/parchment.d.ts", "devDependencies": {"babel-core": "^6.26.0", "istanbul": "~0.4.5", "jasmine-core": "^2.9.1", "karma": "^2.0.0", "karma-babel-preprocessor": "^7.0.0", "karma-chrome-launcher": "^2.2.0", "karma-coverage": "^1.1.1", "karma-jasmine": "^1.1.1", "karma-sauce-launcher": "^1.2.0", "karma-webpack": "^2.0.9", "ts-loader": "^3.4.0", "typescript": "^2.7.1", "webpack": "^3.10.0"}, "license": "BSD-3-<PERSON><PERSON>", "repository": {"type": "git", "url": "https://github.com/quilljs/parchment"}, "scripts": {"build": "webpack --config webpack.conf.js", "prepublish": "npm run build", "test": "karma start", "test:server": "karma start --no-single-run", "test:travis": "karma start --browsers saucelabs-chrome --reporters dots,saucelabs"}, "bugs": {"url": "https://github.com/quilljs/parchment/issues"}}