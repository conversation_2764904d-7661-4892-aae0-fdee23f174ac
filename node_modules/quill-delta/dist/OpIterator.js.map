{"version": 3, "file": "OpIterator.js", "sourceRoot": "", "sources": ["../src/OpIterator.ts"], "names": [], "mappings": ";;AAAA,6BAAsB;AAEtB,MAAqB,QAAQ;IAK3B,YAAY,GAAS;QACnB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG,QAAQ,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,MAAe;QAClB,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,QAAQ,CAAC;SACnB;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,MAAM,EAAE;YACV,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,QAAQ,GAAG,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,MAAM,IAAI,QAAQ,GAAG,MAAM,EAAE;gBAC/B,MAAM,GAAG,QAAQ,GAAG,MAAM,CAAC;gBAC3B,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;gBAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;aACjB;iBAAM;gBACL,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC;aACvB;YACD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;gBACrC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;aAC3B;iBAAM;gBACL,MAAM,KAAK,GAAO,EAAE,CAAC;gBACrB,IAAI,MAAM,CAAC,UAAU,EAAE;oBACrB,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;iBACtC;gBACD,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;oBACrC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;iBACvB;qBAAM,IACL,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;oBACjC,MAAM,CAAC,MAAM,KAAK,IAAI,EACtB;oBACA,2CAA2C;oBAC3C,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;iBAC9B;qBAAM,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;oBAC5C,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;iBACrD;qBAAM;oBACL,2CAA2C;oBAC3C,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;iBAC9B;gBACD,OAAO,KAAK,CAAC;aACd;SACF;aAAM;YACL,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;SAC7B;IACH,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,UAAU;QACR,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;YACxB,gEAAgE;YAChE,OAAO,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;SACtD;aAAM;YACL,OAAO,QAAQ,CAAC;SACjB;IACH,CAAC;IAED,QAAQ;QACN,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,EAAE,EAAE;YACN,IAAI,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,EAAE;gBACjC,OAAO,QAAQ,CAAC;aACjB;iBAAM,IACL,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ;gBAC7B,CAAC,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,IAAI,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EACrD;gBACA,OAAO,QAAQ,CAAC;aACjB;iBAAM;gBACL,OAAO,QAAQ,CAAC;aACjB;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,IAAI;QACF,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;YACnB,OAAO,EAAE,CAAC;SACX;aAAM,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACnC;aAAM;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YACzB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;YACnB,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC5B;IACH,CAAC;CACF;AAvGD,2BAuGC"}