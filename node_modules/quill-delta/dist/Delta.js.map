{"version": 3, "file": "Delta.js", "sourceRoot": "", "sources": ["../src/Delta.ts"], "names": [], "mappings": ";;;AAAA,kCAAkC;AAClC,8CAA+C;AAC/C,0CAA2C;AAC3C,iDAA0C;AAojBjB,uBApjBlB,sBAAY,CAojBkB;AAnjBrC,6BAAsB;AAmjBb,aAnjBF,YAAE,CAmjBE;AAljBX,6CAAsC;AAkjBzB,qBAljBN,oBAAU,CAkjBM;AAhjBvB,MAAM,cAAc,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,uCAAuC;AAQtF,MAAM,mBAAmB,GAAG,CAC1B,CAA8B,EAC9B,CAAe,EACa,EAAE;IAC9B,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;QACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,EAAE,CAAC,CAAC;KAChD;IACD,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;QACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,OAAO,CAAC,EAAE,CAAC,CAAC;KAChD;IACD,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACpC,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACjD,MAAM,IAAI,KAAK,CACb,4BAA4B,SAAS,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAChE,CAAC;KACH;IACD,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF,MAAM,KAAK;IAuBT,YAAY,GAA0B;QACpC,wCAAwC;QACxC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACtB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;SAChB;aAAM,IAAI,GAAG,IAAI,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAChD,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC;SACpB;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;SACf;IACH,CAAC;IA1BD,MAAM,CAAC,aAAa,CAAI,SAAiB,EAAE,OAAwB;QACjE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;IACrC,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,SAAiB;QACtC,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAEO,MAAM,CAAC,UAAU,CAAC,SAAiB;QACzC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,+BAA+B,SAAS,GAAG,CAAC,CAAC;SAC9D;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAcD,MAAM,CACJ,GAAqC,EACrC,UAAgC;QAEhC,MAAM,KAAK,GAAO,EAAE,CAAC;QACrB,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/C,OAAO,IAAI,CAAC;SACb;QACD,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;QACnB,IACE,UAAU,IAAI,IAAI;YAClB,OAAO,UAAU,KAAK,QAAQ;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAClC;YACA,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CAAC,MAAc;QACnB,IAAI,MAAM,IAAI,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;SACb;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CACJ,MAAwC,EACxC,UAAgC;QAEhC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,IAAI,CAAC,EAAE;YAC7C,OAAO,IAAI,CAAC;SACb;QACD,MAAM,KAAK,GAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QACrC,IACE,UAAU,IAAI,IAAI;YAClB,OAAO,UAAU,KAAK,QAAQ;YAC9B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,GAAG,CAAC,EAClC;YACA,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,IAAI,CAAC,KAAS;QACZ,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC5B,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACjC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;YAC9B,IACE,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;gBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;gBACA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gBAC/D,OAAO,IAAI,CAAC;aACb;YACD,oFAAoF;YACpF,gCAAgC;YAChC,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE;gBAC7D,KAAK,IAAI,CAAC,CAAC;gBACX,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;gBAC7B,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;oBAC9B,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACxB,OAAO,IAAI,CAAC;iBACb;aACF;YACD,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,EAAE;gBAChD,IACE,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;oBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;oBACA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC/D,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE;wBACxC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;qBACnD;oBACD,OAAO,IAAI,CAAC;iBACb;qBAAM,IACL,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ;oBAChC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EACjC;oBACA,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;oBAC/D,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE;wBACxC,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;qBACnD;oBACD,OAAO,IAAI,CAAC;iBACb;aACF;SACF;QACD,IAAI,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;SAClC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI;QACF,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC7C,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACrE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;SAChB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,SAA6C;QAClD,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,OAAO,CAAC,SAA0C;QAChD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAC9B,CAAC;IAED,GAAG,CAAI,SAAuC;QAC5C,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IAED,SAAS,CAAC,SAA8B;QACtC,MAAM,MAAM,GAAS,EAAE,CAAC;QACxB,MAAM,MAAM,GAAS,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClB,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;YAC/C,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED,MAAM,CACJ,SAAmD,EACnD,YAAe;QAEf,OAAO,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;IAClD,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,OAAO,MAAM,GAAG,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aACjC;iBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;gBACtB,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;aAC7B;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAED,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;YAClC,OAAO,MAAM,GAAG,YAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC,EAAE,CAAC,CAAC,CAAC;IACR,CAAC;IAED,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ;QAC7B,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,MAAM,IAAI,GAAG,IAAI,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;YACpC,IAAI,MAAM,CAAC;YACX,IAAI,KAAK,GAAG,KAAK,EAAE;gBACjB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;aACnC;iBAAM;gBACL,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC;gBAChC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aAClB;YACD,KAAK,IAAI,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IACxB,CAAC;IAED,OAAO,CAAC,KAAY;QAClB,MAAM,QAAQ,GAAG,IAAI,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,oBAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,GAAG,GAAG,EAAE,CAAC;QACf,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;QACpC,IACE,UAAU,IAAI,IAAI;YAClB,OAAO,UAAU,CAAC,MAAM,KAAK,QAAQ;YACrC,UAAU,CAAC,UAAU,IAAI,IAAI,EAC7B;YACA,IAAI,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC;YAClC,OACE,QAAQ,CAAC,QAAQ,EAAE,KAAK,QAAQ;gBAChC,QAAQ,CAAC,UAAU,EAAE,IAAI,SAAS,EAClC;gBACA,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;gBACnC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aAC3B;YACD,IAAI,UAAU,CAAC,MAAM,GAAG,SAAS,GAAG,CAAC,EAAE;gBACrC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC;aAC/C;SACF;QACD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC7B,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE;YAChD,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBACrC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;aAC9B;iBAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBAC3C,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aAC7B;iBAAM;gBACL,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrC,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,OAAO,CAAC,MAAM,EAAE;oBAClB,MAAM,KAAK,GAAO,EAAE,CAAC;oBACrB,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;wBACrC,KAAK,CAAC,MAAM;4BACV,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;qBAChE;yBAAM;wBACL,IAAI,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ,EAAE;4BACtC,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;gCACzB,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;6BAC9B;iCAAM;gCACL,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;6BAC9B;yBACF;6BAAM;4BACL,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;4BAC3D,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,mBAAmB,CAC1D,MAAM,CAAC,MAAM,CAAC,EACd,OAAO,CAAC,MAAM,CACf,CAAC;4BACF,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;4BAC5C,KAAK,CAAC,MAAM,CAAC,GAAG;gCACd,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,OAAO,CAC1B,QAAQ,EACR,SAAS,EACT,MAAM,KAAK,QAAQ,CACpB;6BACF,CAAC;yBACH;qBACF;oBACD,8EAA8E;oBAC9E,MAAM,UAAU,GAAG,sBAAY,CAAC,OAAO,CACrC,MAAM,CAAC,UAAU,EACjB,OAAO,CAAC,UAAU,EAClB,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,CAClC,CAAC;oBACF,IAAI,UAAU,EAAE;wBACd,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;qBAC/B;oBACD,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAElB,+CAA+C;oBAC/C,IACE,CAAC,SAAS,CAAC,OAAO,EAAE;wBACpB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,EAC/C;wBACA,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;wBACxC,OAAO,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;qBAClC;oBAED,6DAA6D;oBAC7D,8BAA8B;iBAC/B;qBAAM,IACL,OAAO,OAAO,CAAC,MAAM,KAAK,QAAQ;oBAClC,CAAC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;wBAChC,CAAC,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,EAChE;oBACA,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrB;aACF;SACF;QACD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,KAAY;QACjB,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;QAC1C,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAClD;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,CAAC,KAAY,EAAE,MAAiC;QAClD,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE;YAC1B,OAAO,IAAI,KAAK,EAAE,CAAC;SACpB;QACD,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1C,OAAO,KAAK;iBACT,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACV,IAAI,EAAE,CAAC,MAAM,IAAI,IAAI,EAAE;oBACrB,OAAO,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC;iBACnE;gBACD,MAAM,IAAI,GAAG,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC7C,MAAM,IAAI,KAAK,CAAC,gBAAgB,GAAG,IAAI,GAAG,eAAe,CAAC,CAAC;YAC7D,CAAC,CAAC;iBACD,IAAI,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;QACH,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,IAAI,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,oBAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,UAAU,CAAC,OAAO,CAAC,CAAC,SAAoB,EAAE,EAAE;YAC1C,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;YACjC,OAAO,MAAM,GAAG,CAAC,EAAE;gBACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;gBACjB,QAAQ,SAAS,CAAC,CAAC,CAAC,EAAE;oBACpB,KAAK,IAAI,CAAC,MAAM;wBACd,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,UAAU,EAAE,EAAE,MAAM,CAAC,CAAC;wBACpD,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACxC,MAAM;oBACR,KAAK,IAAI,CAAC,MAAM;wBACd,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;wBACnD,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACxB,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;wBAC1B,MAAM;oBACR,KAAK,IAAI,CAAC,KAAK;wBACb,QAAQ,GAAG,IAAI,CAAC,GAAG,CACjB,QAAQ,CAAC,UAAU,EAAE,EACrB,SAAS,CAAC,UAAU,EAAE,EACtB,MAAM,CACP,CAAC;wBACF,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACvC,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;wBACzC,IAAI,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,EAAE;4BAC1C,QAAQ,CAAC,MAAM,CACb,QAAQ,EACR,sBAAY,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,CACzD,CAAC;yBACH;6BAAM;4BACL,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;yBACzC;wBACD,MAAM;iBACT;gBACD,MAAM,IAAI,QAAQ,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,QAAQ,CACN,SAImB,EACnB,OAAO,GAAG,IAAI;QAEd,MAAM,IAAI,GAAG,IAAI,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACtC,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,GAAG,CAAC,CAAC;QACV,OAAO,IAAI,CAAC,OAAO,EAAE,EAAE;YACrB,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBAChC,OAAO;aACR;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAC3B,MAAM,KAAK,GAAG,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACpD,MAAM,KAAK,GACT,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;gBAC/B,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,KAAK;gBAC/C,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,IAAI,KAAK,GAAG,CAAC,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;aACxB;iBAAM,IAAI,KAAK,GAAG,CAAC,EAAE;gBACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7B;iBAAM;gBACL,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,CAAC,CAAC,KAAK,KAAK,EAAE;oBAC/D,OAAO;iBACR;gBACD,CAAC,IAAI,CAAC,CAAC;gBACP,IAAI,GAAG,IAAI,KAAK,EAAE,CAAC;aACpB;SACF;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;YACrB,SAAS,CAAC,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACxB;IACH,CAAC;IAED,MAAM,CAAC,IAAW;QAChB,MAAM,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE;YAC5B,IAAI,EAAE,CAAC,MAAM,EAAE;gBACb,QAAQ,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;aAChC;iBAAM,IAAI,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,IAAI,EAAE,CAAC,UAAU,IAAI,IAAI,EAAE;gBACjE,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC3B,OAAO,SAAS,GAAG,EAAE,CAAC,MAAM,CAAC;aAC9B;iBAAM,IAAI,EAAE,CAAC,MAAM,IAAI,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,EAAE;gBACrD,MAAM,MAAM,GAAG,CAAC,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,MAAM,CAAW,CAAC;gBAClD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,GAAG,MAAM,CAAC,CAAC;gBACxD,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACvB,IAAI,EAAE,CAAC,MAAM,EAAE;wBACb,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBACvB;yBAAM,IAAI,EAAE,CAAC,MAAM,IAAI,EAAE,CAAC,UAAU,EAAE;wBACrC,QAAQ,CAAC,MAAM,CACb,YAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EACjB,sBAAY,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CACtD,CAAC;qBACH;gBACH,CAAC,CAAC,CAAC;gBACH,OAAO,SAAS,GAAG,MAAM,CAAC;aAC3B;iBAAM,IAAI,OAAO,EAAE,CAAC,MAAM,KAAK,QAAQ,IAAI,EAAE,CAAC,MAAM,KAAK,IAAI,EAAE;gBAC9D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC;gBACnD,MAAM,MAAM,GAAG,IAAI,oBAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;gBAChD,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,GAAG,mBAAmB,CACzD,EAAE,CAAC,MAAM,EACT,MAAM,CAAC,MAAM,CACd,CAAC;gBACF,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC5C,QAAQ,CAAC,MAAM,CACb,EAAE,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,EACnD,sBAAY,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CACtD,CAAC;gBACF,OAAO,SAAS,GAAG,CAAC,CAAC;aACtB;YACD,OAAO,SAAS,CAAC;QACnB,CAAC,EAAE,CAAC,CAAC,CAAC;QACN,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAID,SAAS,CAAC,GAAmB,EAAE,QAAQ,GAAG,KAAK;QAC7C,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QACtB,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;SAC9C;QACD,MAAM,KAAK,GAAU,GAAG,CAAC;QACzB,MAAM,QAAQ,GAAG,IAAI,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAG,IAAI,oBAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;QAC1B,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE,EAAE;YAChD,IACE,QAAQ,CAAC,QAAQ,EAAE,KAAK,QAAQ;gBAChC,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,QAAQ,CAAC,EAC/C;gBACA,KAAK,CAAC,MAAM,CAAC,YAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;aAC1C;iBAAM,IAAI,SAAS,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE;gBAC5C,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;aAC9B;iBAAM;gBACL,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,EAAE,EAAE,SAAS,CAAC,UAAU,EAAE,CAAC,CAAC;gBACvE,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrC,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACvC,IAAI,MAAM,CAAC,MAAM,EAAE;oBACjB,yEAAyE;oBACzE,SAAS;iBACV;qBAAM,IAAI,OAAO,CAAC,MAAM,EAAE;oBACzB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACrB;qBAAM;oBACL,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC;oBAC/B,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;oBACjC,IAAI,eAAe,GACjB,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI;wBACjD,CAAC,CAAC,SAAS;wBACX,CAAC,CAAC,MAAM,CAAC;oBACb,IACE,OAAO,QAAQ,KAAK,QAAQ;wBAC5B,QAAQ,KAAK,IAAI;wBACjB,OAAO,SAAS,KAAK,QAAQ;wBAC7B,SAAS,KAAK,IAAI,EAClB;wBACA,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC3C,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;4BAC5C,IAAI,OAAO,EAAE;gCACX,eAAe,GAAG;oCAChB,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,SAAS,CAC5B,QAAQ,CAAC,SAAS,CAAC,EACnB,SAAS,CAAC,SAAS,CAAC,EACpB,QAAQ,CACT;iCACF,CAAC;6BACH;yBACF;qBACF;oBAED,0CAA0C;oBAC1C,KAAK,CAAC,MAAM,CACV,eAAe,EACf,sBAAY,CAAC,SAAS,CACpB,MAAM,CAAC,UAAU,EACjB,OAAO,CAAC,UAAU,EAClB,QAAQ,CACT,CACF,CAAC;iBACH;aACF;SACF;QACD,OAAO,KAAK,CAAC,IAAI,EAAE,CAAC;IACtB,CAAC;IAED,iBAAiB,CAAC,KAAa,EAAE,QAAQ,GAAG,KAAK;QAC/C,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAI,oBAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,OAAO,QAAQ,CAAC,OAAO,EAAE,IAAI,MAAM,IAAI,KAAK,EAAE;YAC5C,MAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC;YACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACrC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,MAAM,CAAC,CAAC;gBAC1C,SAAS;aACV;iBAAM,IAAI,QAAQ,KAAK,QAAQ,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACjE,KAAK,IAAI,MAAM,CAAC;aACjB;YACD,MAAM,IAAI,MAAM,CAAC;SAClB;QACD,OAAO,KAAK,CAAC;IACf,CAAC;;AA/gBM,QAAE,GAAG,YAAE,CAAC;AACR,gBAAU,GAAG,oBAAU,CAAC;AACxB,kBAAY,GAAG,sBAAY,CAAC;AACpB,cAAQ,GAAmD,EAAE,CAAC;AA+gB/E,kBAAe,KAAK,CAAC;AAIrB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;IAC9B,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;IACvB,MAAM,CAAC,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;CAChC"}